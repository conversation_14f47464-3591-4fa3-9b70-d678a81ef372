# [TASK-007] Perplexity Provider Real Implementation

**Status**: 📝 TODO
**Prioridade**: P1
**Estimativa**: 16h
**Due**: 2025-02-05
**Tags**: [production-ready, api-integration, perplexity, provider]
**Blocked by**: None

## 🎯 Goal

Implementar o PerplexityProvider real para substituir todos os dados mock nos serviços de pesquisa, garantindo integração completa com a API Perplexity.

## 📋 Context

- Todos os serviços atualmente usam `perplexity_provider=None` (mock data)
- API Perplexity já está configurada em `env.PERPLEXITY_API_KEY`
- Modelo: `llama-3.1-sonar-large-128k-online`
- Custo médio: $0.01 por requisição

## 🚀 Implementation Plan

### FASE 1: Implementar PerplexityProvider (6h)

#### MT-1.1: Criar Classe PerplexityProvider (3h)
**Goal**: Implementar provider com interface padrão
**Files Affected**:
- `backend/app/providers/perplexity_provider.py` (NOVO)
**Outline**:
```python
class PerplexityProvider:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.perplexity.ai/chat/completions"
        self.model = "llama-3.1-sonar-large-128k-online"
    
    async def search(self, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        # Implementação real da API
```

#### MT-1.2: Implementar Rate Limiting (2h)
**Goal**: Prevenir rate limit errors
**Files Affected**:
- `backend/app/providers/perplexity_provider.py`
**Outline**:
1. Implementar token bucket algorithm
2. Configurar 50 requests/minute (limite Perplexity)
3. Queue para requisições excedentes
4. Retry logic com backoff exponencial

#### MT-1.3: Implementar Error Handling (1h)
**Goal**: Tratamento robusto de erros da API
**Files Affected**:
- `backend/app/providers/perplexity_provider.py`
**Outline**:
1. Capturar erros HTTP específicos
2. Implementar fallback strategies
3. Logging detalhado
4. Métricas de falha

### FASE 2: Integrar com Serviços (6h)

#### MT-2.1: Atualizar Service Factory (2h)
**Goal**: Injetar provider real nos serviços
**Files Affected**:
- `backend/app/services/research_orchestrator.py`
**Outline**:
```python
from app.providers import PerplexityProvider

perplexity_provider = PerplexityProvider(env.PERPLEXITY_API_KEY)

# Registrar serviços com provider real
services = [
    BasicDossierService(perplexity_provider),
    SwotAnalysisService(perplexity_provider),
    # ... todos os 11 serviços
]
```

#### MT-2.2: Atualizar Serviços Individuais (3h)
**Goal**: Remover lógica mock e usar provider
**Files Affected**:
- Todos os 11 arquivos em `backend/app/services/research/`
**Outline**:
1. Remover métodos `_get_mock_*`
2. Implementar `_build_perplexity_query()`
3. Processar respostas da API
4. Validar estrutura de dados

#### MT-2.3: Implementar Parsers de Resposta (1h)
**Goal**: Converter respostas Perplexity em formato esperado
**Files Affected**:
- `backend/app/providers/response_parsers.py` (NOVO)
**Outline**:
1. Parser JSON robusto
2. Validação de schema
3. Fallback para texto não-estruturado

### FASE 3: Otimizações (4h)

#### MT-3.1: Implementar Batching (2h)
**Goal**: Agrupar requisições para eficiência
**Files Affected**:
- `backend/app/providers/perplexity_provider.py`
**Outline**:
1. Queue de requisições pendentes
2. Batch processor assíncrono
3. Combinar respostas
4. Manter ordem original

#### MT-3.2: Implementar Response Caching (2h)
**Goal**: Cache temporário para reduzir custos
**Files Affected**:
- `backend/app/providers/perplexity_provider.py`
**Outline**:
1. Cache em memória com TTL 1h
2. Hash de queries para keys
3. Cache invalidation strategy
4. Métricas de hit/miss

## 📊 Success Criteria

- [ ] Zero dados mock em produção
- [ ] Taxa de sucesso > 95%
- [ ] Tempo médio resposta < 5s
- [ ] Rate limiting funcionando
- [ ] Error handling robusto
- [ ] Logs estruturados
- [ ] Testes unitários completos

## 🔧 Technical Requirements

### Estrutura de Request
```python
{
    "model": "llama-3.1-sonar-large-128k-online",
    "messages": [
        {"role": "system", "content": "..."},
        {"role": "user", "content": "..."}
    ],
    "temperature": 0.2,
    "max_tokens": 4000,
    "return_citations": True,
    "search_domain_filter": ["perplexity.ai"],
    "search_recency_filter": "month"
}
```

### Headers Obrigatórios
```python
{
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json",
    "Accept": "application/json"
}
```

## 🚨 Riscos e Mitigações

| Risco | Impacto | Mitigação |
|-------|---------|-----------|
| Rate limits | Alto | Token bucket + queue |
| API downtime | Alto | Circuit breaker + cache |
| Custos elevados | Médio | Cache + batching |
| Respostas inconsistentes | Médio | Validação + retry |

---

**Criado em**: 2025-01-30 17:30  
**Criado por**: AI Assistant 