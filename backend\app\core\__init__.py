"""
Core do sistema de pesquisa modular.
"""
from .interfaces import (
    IResearchService,
    IResearchProvider,
    ResearchRequest,
    ResearchResult
)

from .exceptions import (
    ResearchException,
    ResearchServiceNotFoundError,
    ResearchValidationError,
    ResearchProviderError,
    ResearchRateLimitError,
    ResearchTimeoutError
)

from .utils import (
    Timer,
    run_with_timeout,
    calculate_total_cost,
    sanitize_url,
    extract_domain,
    parallel_execute,
    format_confidence_score,
    log_service_execution
)

__all__ = [
    # Interfaces
    'IResearchService',
    'IResearchProvider',
    'ResearchRequest',
    'ResearchResult',

    # Exceptions
    'ResearchException',
    'ResearchServiceNotFoundError',
    'ResearchValidationError',
    'ResearchProviderError',
    'ResearchRateLimitError',
    'ResearchTimeoutError',

    # Utils
    'Timer',
    'run_with_timeout',
    'calculate_total_cost',
    'sanitize_url',
    'extract_domain',
    'parallel_execute',
    'format_confidence_score',
    'log_service_execution'
]

