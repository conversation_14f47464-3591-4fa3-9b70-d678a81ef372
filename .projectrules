# Project Rules - ScopeAI

## 🎯 Regras Fundamentais

### 0. PRODUCTION READY (NOVA - CRÍTICA)
- **JAMAIS** usar dados mock em produção - PRODUCTION READY é obrigatório
- **SEMPRE** implementar integrações reais com APIs e serviços externos
- **SEMPRE** incluir error handling, retry logic e circuit breakers
- **SEMPRE** garantir SLAs: Response < 5s, Uptime > 99.9%
- **SEMPRE** implementar monitoramento e observabilidade completos

### 1. Memory Bank
- **SEMPRE** ler todos os arquivos do memory-bank antes de iniciar qualquer tarefa
- **SEMPRE** atualizar memory-bank/activeContext.md com decisões importantes
- **SEMPRE** criar arquivo de tarefa em memory-bank/roadmap/ antes de implementar
- **SEMPRE** arquivar tarefas concluídas em memory-bank/archive/ com score

### 2. Código e Arquitetura
- Seguir **Arquitetura Simples** conforme python.mdc (app/routes, services, core, config, tests)
- Sem overengineering: apenas o essencial, foco em simplicidade
- Backend: FastAPI com estrutura pragmática e modular
- Frontend: Angular 19 com control flow moderno (@if/@for)
- Zero duplicação de código (DRY)
- Single Responsibility Principle (SRP) em todos os módulos
- Clean Code: nomes descritivos, funções pequenas, código autodocumentado
- Arquivos < 500 linhas (quebrar God Files em serviços menores)
- Research Services: implementar interface IResearchService, incluir confidence score

### 3. Qualidade e Testes
- Cobertura mínima de testes: 80%
- Todo código assíncrono deve usar async/await
- Performance: Response time < 2s, async operations < 10min
- Score mínimo por tarefa: 27/30 (90%)
- TDD quando possível: escrever testes antes da implementação

### 4. Workflow de Desenvolvimento
1. Ler memory-bank completo
2. Usar mcp_Sequential_Thinking antes de qualquer implementação
3. Criar microtarefas atômicas com checkboxes
4. Documentar progresso em memory-bank/roadmap/tasks.md
5. Auto-avaliar com score 0-30
6. Arquivar tarefas concluídas

### 5. Padrões de Commit
- Mensagens em inglês
- Formato: `type(scope): description`
- Types: feat, fix, docs, style, refactor, test, chore
- Exemplo: `feat(services): add basic dossier research service`

### 6. Tecnologias e Versões
- Backend: Python 3.12 + FastAPI 0.104+ + Poetry
- Frontend: Angular 19 + TypeScript 5.3
- Database: MongoDB Atlas + Motor async driver
- Cache: Redis 7.2
- Containers: Docker 24.0

### 7. Integrações e APIs
- Perplexity API: Rate limit 50 req/min (será modularizado em 11 serviços)
- OpenAI: GPT-4-turbo + text-embedding-3-small
- Cohere: Command-R para geração de conteúdo
- Futuro: SearchSociety como alternativa ao Perplexity

### 8. Segurança
- JWT tokens com refresh
- CORS configurado para origins específicas
- Sanitização de inputs obrigatória
- Rate limiting por IP
- Secrets em environment variables

### 9. Performance
- Usar cache multi-nível (L1: memória, L2: Redis, L3: MongoDB)
- Processamento paralelo com asyncio.gather()
- Lazy loading no frontend
- Índices MongoDB otimizados
- Research services executados em paralelo (30s vs 5min)

### 10. Documentação
- Toda função pública deve ter docstring
- README.md sempre atualizado
- Arquivos memory-bank como fonte da verdade
- Comentários apenas quando necessário (código deve ser autoexplicativo)

### 11. Research Services (Novo - Refatoração em Progresso)
- Cada serviço implementa IResearchService com métodos obrigatórios
- Custo definido e retornado no get_cost() (Decimal)
- Confidence score (0.0-1.0) baseado na completude dos dados
- Temperature configurável para cada serviço (0.6-0.7 típico)
- Estrutura JSON bem definida e validada no _process_result()
- Testes com fixtures completas e casos edge
- Prompts especializados no _build_system_prompt() e _build_user_prompt()
- Exemplo de saída obrigatório em get_sample_output()
- Campos opcionais do ResearchRequest devem vir em additional_context
- Usar datetime.now(timezone.utc) ao invés de datetime.utcnow() (Python 3.12+)
- Dependências sempre via Poetry (poetry add --group dev para dev dependencies)
- **FASE 1 COMPLETA**: 5/11 serviços implementados com score médio 94%
- **FASE 2 EM PROGRESSO**: 5/6 serviços implementados (10/11 total - 91% completo)
- Confidence score deve verificar dados reais vs valores padrão ("Dado não encontrado")
- Context building com getattr() para campos opcionais seguros
- Market analysis deve incluir TAM/SAM/SOM, competitive landscape e market dynamics
- Timer.elapsed funciona corretamente com context manager para medir performance
- Serviços com análise multi-dimensional devem implementar scoring ponderado
- Recomendações devem ter fallback garantido (sempre >= 1 recomendação)
- Mock data deve ser rico e realista para facilitar desenvolvimento/demos
- Unpacking de tuplas em loops: for key, value in dict.items()
- AIDEV-NOTE: adicionar em pontos críticos ou não-óbvios do código
- Partnership Service: análise tridimensional (Comercial 35%, Tecnológica 30%, Estratégica 35%)
- Channel strategy: determinar tipo (multi-tier, direct, hybrid) baseado em parceiros
- Partnership maturity: avaliar estágio (nascent, emerging, growth, mature) por dados
- Pricing Service: análise de modelos (subscription, freemium, usage-based), competitividade e monetização
- Pricing Maturity Score: 4 dimensões ponderadas (modelos 25%, tiers 25%, competitiva 25%, estratégias 25%)
- Recomendações baseadas em gaps: categorizar por optimization, expansion, testing, analysis
- Estrutura de tiers deve incluir features detalhadas e limitações específicas
- Business Model Service: Canvas completo com 9 blocos, análise de distribuição/licenciamento/sustentabilidade
- Business Model Maturity Score: 5 dimensões (Canvas 30%, Revenue streams 20%, Value props 20%, Sustentabilidade 20%, Unit economics 10%)
- Mock data pode ser baseado em pesquisa externa (ex: Embroker) para maior realismo
- Unit economics: LTV/CAC ratio como indicador chave (>3 é saudável)
- Channels Reviews Service: análise dual de efetividade de canais e reputação online
- Channel Effectiveness Score: 4 dimensões (Comunicação 30%, Vendas 25%, Reviews 25%, Feedback 20%)
- Online Reputation Score: média ponderada de ratings de diferentes plataformas
- JSON parsing em _process_result deve re-raise JSONDecodeError para testes adequados

## 📋 Checklist Pré-Deploy

- [ ] Todos os testes passando (pytest + jest)
- [ ] Sem erros de linter (ESLint + Black)
- [ ] Memory bank atualizado (activeContext.md + progress.md)
- [ ] Docker build funcionando
- [ ] Performance validada (< 2s response time)
- [ ] Documentação atualizada

## 🚨 Proibições

1. **NUNCA** commitar código não testado
2. **NUNCA** duplicar lógica entre arquivos
3. **NUNCA** usar print() para debug (use logging)
4. **NUNCA** salvar secrets no código
5. **NUNCA** pular o memory-bank ao iniciar tarefa

## 📝 Links Importantes

- Memory Bank: `/memory-bank/`
- Python Architecture Guide: `.cursor/rules/development/python.mdc`
- API Docs: `http://localhost:8040/docs`
- Frontend: `http://localhost:4200`
- MongoDB Atlas: Ver .env para connection string

---

**Última atualização**: 2025-01-30 (FASE 3: Production-Ready System - 4 tarefas críticas planejadas) 