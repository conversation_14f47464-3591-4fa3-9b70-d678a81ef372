"""
SWOT Analysis Service - Serviço para análise SWOT expandida e estratégica.

Este serviço gera análise SWOT aprofundada incluindo:
- Forças (Strengths): Recursos internos e vantagens competitivas
- Fr<PERSON>zas (Weaknesses): Limitações internas e gaps
- Oportunidades (Opportunities): Tendências externas favoráveis
- Ameaças (Threats): Riscos externos e desafios
- Análise cruzada: SO, WO, ST, WT
- Quantificações: Impacto (1-10) e Probabilidade
- Recomendações estratégicas priorizadas
- Matriz de ações por prazo (curto/médio/longo)

Custo: $0.005 por execução
"""
import json
import logging
from typing import Dict, Any, List, Optional
from decimal import Decimal
from datetime import datetime, UTC

from app.core import (
    IResearchService,
    ResearchRequest,
    ResearchResult,
    ResearchProviderError,
    Timer
)

logger = logging.getLogger(__name__)


class SwotAnalysisService(IResearchService):
    """
    Serviço para análise SWOT expandida e estratégica.

    Utiliza Perplexity API para gerar análise SWOT profunda com
    quantificações, priorizações e recomendações estratégicas acionáveis.
    """

    def __init__(self, perplexity_provider):
        """
        Args:
            perplexity_provider: Instância do provider Perplexity
        """
        self.provider = perplexity_provider
        self._version = "1.0.0"
        self._cost = Decimal("0.005")

    def get_name(self) -> str:
        return "swot_analysis"

    def get_version(self) -> str:
        return self._version

    def get_description(self) -> str:
        return (
            "Gera análise SWOT expandida com quantificações de impacto e "
            "probabilidade, análise cruzada (SO/WO/ST/WT) e recomendações "
            "estratégicas priorizadas por prazo de implementação."
        )

    def get_cost(self) -> Decimal:
        return self._cost

    def get_required_fields(self) -> List[str]:
        return ["company_name", "company_url"]

    def get_optional_fields(self) -> List[str]:
        return ["industry", "market_position", "competitors", "business_model"]

    async def validate_request(self, request: ResearchRequest) -> bool:
        """Valida se a requisição tem os campos necessários."""
        # Verifica campos obrigatórios
        if not request.company_name or not request.company_url:
            return False

        # Nome da empresa deve ter pelo menos 2 caracteres
        if len(request.company_name.strip()) < 2:
            return False

        return True

    async def execute(self, request: ResearchRequest) -> ResearchResult:
        """
        Executa a análise SWOT expandida.

        Args:
            request: Requisição com dados da empresa

        Returns:
            ResearchResult com análise SWOT completa
        """
        with Timer() as timer:
            try:
                # Preparar dados
                empresa = request.company_name
                site = request.company_url

                # Extrair contexto adicional se disponível
                contexto_adicional = self._extract_additional_context(request)

                # Construir prompts
                system_content = self._build_system_prompt()
                user_content = self._build_user_prompt(
                    empresa=empresa,
                    site=site,
                    contexto=contexto_adicional
                )

                # Fazer requisição ao provider
                logger.info(f"Gerando análise SWOT expandida para {empresa}")
                result_data = await self.provider.search(
                    system_prompt=system_content,
                    user_prompt=user_content,
                    temperature=0.6  # Menor temperatura para análise mais consistente
                )

                # Processar resultado
                processed_data = self._process_result(result_data)

                # Calcular score de confiança
                confidence_score = self._calculate_confidence_score(
                    processed_data)

                return ResearchResult(
                    service_name=self.get_name(),
                    service_version=self.get_version(),
                    timestamp=datetime.now(UTC),
                    cost=self._cost,
                    success=True,
                    data=processed_data,
                    processing_time_seconds=timer.elapsed,
                    confidence_score=confidence_score
                )

            except Exception as e:
                logger.error(f"Erro ao gerar análise SWOT: {str(e)}")
                return ResearchResult(
                    service_name=self.get_name(),
                    service_version=self.get_version(),
                    timestamp=datetime.now(UTC),
                    cost=Decimal("0"),  # Não cobra em caso de erro
                    success=False,
                    error=str(e),
                    processing_time_seconds=timer.elapsed
                )

    def get_sample_output(self) -> Dict[str, Any]:
        """Retorna exemplo de saída do serviço."""
        return {
            "forcas": [
                {
                    "item": "Tecnologia proprietária avançada",
                    "descricao": "Plataforma desenvolvida internamente com diferenciais únicos",
                    "impacto": 9,
                    "categoria": "tecnologia",
                    "vantagem_competitiva": "Alta barreira de entrada para competidores"
                }
            ],
            "fraquezas": [
                {
                    "item": "Dependência de poucos clientes grandes",
                    "descricao": "80% da receita vem de 3 clientes principais",
                    "impacto": 8,
                    "categoria": "comercial",
                    "risco": "Alta vulnerabilidade a churn",
                    "acao_mitigacao": "Diversificar base de clientes"
                }
            ],
            "oportunidades": [
                {
                    "item": "Expansão para mercado latino-americano",
                    "descricao": "Crescimento de 45% ao ano no setor na região",
                    "impacto": 8,
                    "probabilidade": 7,
                    "prazo": "médio",
                    "categoria": "mercado",
                    "requisitos": "Localização do produto e equipe local"
                }
            ],
            "ameacas": [
                {
                    "item": "Entrada de big techs no segmento",
                    "descricao": "Google e Microsoft anunciaram soluções competidoras",
                    "impacto": 9,
                    "probabilidade": 8,
                    "prazo": "curto",
                    "categoria": "competição",
                    "estrategia_defesa": "Focar em nichos especializados"
                }
            ],
            "analise_cruzada": {
                "so_strategies": [
                    {
                        "estrategia": "Usar tecnologia proprietária para capturar mercado LATAM",
                        "forca_relacionada": "Tecnologia proprietária avançada",
                        "oportunidade_relacionada": "Expansão para mercado latino-americano",
                        "prioridade": "alta"
                    }
                ],
                "wo_strategies": [
                    {
                        "estrategia": "Aproveitar expansão LATAM para diversificar clientes",
                        "fraqueza_relacionada": "Dependência de poucos clientes",
                        "oportunidade_relacionada": "Expansão para mercado latino-americano",
                        "prioridade": "alta"
                    }
                ],
                "st_strategies": [
                    {
                        "estrategia": "Fortalecer parcerias com clientes atuais contra big techs",
                        "forca_relacionada": "Tecnologia proprietária avançada",
                        "ameaca_relacionada": "Entrada de big techs no segmento",
                        "prioridade": "urgente"
                    }
                ],
                "wt_strategies": [
                    {
                        "estrategia": "Programa urgente de diversificação antes da entrada das big techs",
                        "fraqueza_relacionada": "Dependência de poucos clientes",
                        "ameaca_relacionada": "Entrada de big techs no segmento",
                        "prioridade": "urgente"
                    }
                ]
            },
            "matriz_priorizacao": {
                "acoes_curto_prazo": [
                    "Implementar programa de fidelização para clientes atuais",
                    "Iniciar processo de certificações de segurança"
                ],
                "acoes_medio_prazo": [
                    "Estabelecer operação na América Latina",
                    "Desenvolver produtos para PMEs"
                ],
                "acoes_longo_prazo": [
                    "IPO ou rodada série C para expansão global",
                    "Aquisições estratégicas de startups complementares"
                ]
            },
            "score_estrategico": {
                "posicao_competitiva": 7.5,
                "potencial_crescimento": 8.2,
                "nivel_risco": 6.8,
                "recomendacao_geral": "Empresa bem posicionada mas precisa urgentemente diversificar base de clientes"
            }
        }

    def _extract_additional_context(self, request: ResearchRequest) -> Dict[str, Any]:
        """Extrai contexto adicional da requisição."""
        context = {}

        if request.additional_context:
            # Campos relevantes para análise SWOT
            relevant_fields = [
                "industry", "market_position", "competitors",
                "business_model", "target_market", "main_products",
                "funding_stage", "employee_count", "revenue_range"
            ]

            for field in relevant_fields:
                if field in request.additional_context:
                    context[field] = request.additional_context[field]

        return context

    def _build_system_prompt(self) -> str:
        """Constrói o prompt do sistema para análise SWOT."""
        return (
            "Você é um estrategista de negócios sênior especializado em análise SWOT e planejamento estratégico. "
            "Sua missão é gerar análises SWOT extremamente detalhadas e acionáveis, sempre respondendo APENAS em JSON válido. "
            "IMPORTANTE: Cada item SWOT deve incluir quantificações (impacto 1-10, probabilidade quando aplicável), "
            "categorização, e ações específicas. A análise cruzada deve gerar estratégias concretas combinando elementos. "
            "Use dados reais, exemplos específicos do mercado brasileiro, e seja extremamente detalhado nas recomendações. "
            "Priorize insights acionáveis sobre observações genéricas. O tom deve ser profissional e direto ao ponto."
        )

    def _build_user_prompt(self, empresa: str, site: str, contexto: Dict[str, Any]) -> str:
        """Constrói o prompt do usuário com dados da empresa."""
        # Preparar contexto adicional
        contexto_str = ""
        if contexto:
            partes = []
            if "industry" in contexto:
                partes.append(f"Indústria: {contexto['industry']}")
            if "competitors" in contexto:
                partes.append(f"Competidores: {contexto['competitors']}")
            if "market_position" in contexto:
                partes.append(
                    f"Posição de mercado: {contexto['market_position']}")
            if "business_model" in contexto:
                partes.append(
                    f"Modelo de negócio: {contexto['business_model']}")

            if partes:
                contexto_str = f"\nContexto adicional: {', '.join(partes)}"

        return (
            f"Realize uma análise SWOT profunda e estratégica para a empresa {empresa}, site {site}.{contexto_str}\n\n"
            "INSTRUÇÕES CRÍTICAS:\n"
            "1. Cada item deve ter descrição detalhada, quantificação de impacto (1-10) e categoria\n"
            "2. Oportunidades e Ameaças devem incluir probabilidade (1-10) e prazo (curto/médio/longo)\n"
            "3. Análise cruzada deve combinar elementos específicos gerando estratégias concretas\n"
            "4. Priorização clara com justificativas baseadas em dados\n"
            "5. Considere especificamente o contexto do mercado brasileiro\n\n"
            "Responda EXATAMENTE nesta estrutura JSON:\n"
            "{\n"
            "  \"forcas\": [\n"
            "    {\n"
            "      \"item\": \"Título da força\",\n"
            "      \"descricao\": \"Descrição detalhada\",\n"
            "      \"impacto\": 9,\n"
            "      \"categoria\": \"tecnologia|comercial|operacional|financeira|pessoas\",\n"
            "      \"vantagem_competitiva\": \"Como isso diferencia a empresa\"\n"
            "    }\n"
            "  ],\n"
            "  \"fraquezas\": [\n"
            "    {\n"
            "      \"item\": \"Título da fraqueza\",\n"
            "      \"descricao\": \"Descrição detalhada\",\n"
            "      \"impacto\": 8,\n"
            "      \"categoria\": \"tecnologia|comercial|operacional|financeira|pessoas\",\n"
            "      \"risco\": \"Impacto potencial se não resolvido\",\n"
            "      \"acao_mitigacao\": \"Ação específica para resolver\"\n"
            "    }\n"
            "  ],\n"
            "  \"oportunidades\": [\n"
            "    {\n"
            "      \"item\": \"Título da oportunidade\",\n"
            "      \"descricao\": \"Descrição com dados de mercado\",\n"
            "      \"impacto\": 8,\n"
            "      \"probabilidade\": 7,\n"
            "      \"prazo\": \"curto|médio|longo\",\n"
            "      \"categoria\": \"mercado|tecnologia|regulatório|social|econômico\",\n"
            "      \"requisitos\": \"O que é necessário para capturar\"\n"
            "    }\n"
            "  ],\n"
            "  \"ameacas\": [\n"
            "    {\n"
            "      \"item\": \"Título da ameaça\",\n"
            "      \"descricao\": \"Descrição com exemplos concretos\",\n"
            "      \"impacto\": 9,\n"
            "      \"probabilidade\": 8,\n"
            "      \"prazo\": \"curto|médio|longo\",\n"
            "      \"categoria\": \"competição|tecnologia|regulatório|econômico|social\",\n"
            "      \"estrategia_defesa\": \"Como se proteger ou mitigar\"\n"
            "    }\n"
            "  ],\n"
            "  \"analise_cruzada\": {\n"
            "    \"so_strategies\": [\n"
            "      {\n"
            "        \"estrategia\": \"Estratégia específica combinando S+O\",\n"
            "        \"forca_relacionada\": \"Força específica usada\",\n"
            "        \"oportunidade_relacionada\": \"Oportunidade capturada\",\n"
            "        \"prioridade\": \"alta|média|baixa\"\n"
            "      }\n"
            "    ],\n"
            "    \"wo_strategies\": [\n"
            "      {\n"
            "        \"estrategia\": \"Estratégia para superar W aproveitando O\",\n"
            "        \"fraqueza_relacionada\": \"Fraqueza a superar\",\n"
            "        \"oportunidade_relacionada\": \"Oportunidade que ajuda\",\n"
            "        \"prioridade\": \"alta|média|baixa\"\n"
            "      }\n"
            "    ],\n"
            "    \"st_strategies\": [\n"
            "      {\n"
            "        \"estrategia\": \"Usar S para defender de T\",\n"
            "        \"forca_relacionada\": \"Força defensiva\",\n"
            "        \"ameaca_relacionada\": \"Ameaça a defender\",\n"
            "        \"prioridade\": \"urgente|alta|média\"\n"
            "      }\n"
            "    ],\n"
            "    \"wt_strategies\": [\n"
            "      {\n"
            "        \"estrategia\": \"Estratégia defensiva para W+T\",\n"
            "        \"fraqueza_relacionada\": \"Fraqueza exposta\",\n"
            "        \"ameaca_relacionada\": \"Ameaça relacionada\",\n"
            "        \"prioridade\": \"urgente|alta|média\"\n"
            "      }\n"
            "    ]\n"
            "  },\n"
            "  \"matriz_priorizacao\": {\n"
            "    \"acoes_curto_prazo\": [\"Ação 1 (0-6 meses)\", \"Ação 2\"],\n"
            "    \"acoes_medio_prazo\": [\"Ação 1 (6-18 meses)\", \"Ação 2\"],\n"
            "    \"acoes_longo_prazo\": [\"Ação 1 (18+ meses)\", \"Ação 2\"]\n"
            "  },\n"
            "  \"score_estrategico\": {\n"
            "    \"posicao_competitiva\": 7.5,\n"
            "    \"potencial_crescimento\": 8.2,\n"
            "    \"nivel_risco\": 6.8,\n"
            "    \"recomendacao_geral\": \"Síntese executiva com principal insight estratégico\"\n"
            "  }\n"
            "}\n\n"
            "IMPORTANTE: Forneça no mínimo 3 itens relevantes em cada quadrante SWOT. "
            "Use dados específicos, evite generalidades. Todas as estratégias devem ser acionáveis e específicas."
        )

    def _process_result(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """Processa e valida o resultado do provider."""
        # Se o provider retornou um string JSON, fazer parse
        if isinstance(raw_data, str):
            try:
                data = json.loads(raw_data)
            except json.JSONDecodeError:
                logger.error("Resposta não é JSON válido")
                return self._get_empty_result()
        else:
            data = raw_data

        # Validar estrutura esperada
        required_keys = [
            "forcas", "fraquezas", "oportunidades", "ameacas",
            "analise_cruzada", "matriz_priorizacao", "score_estrategico"
        ]

        for key in required_keys:
            if key not in data:
                logger.warning(f"Campo obrigatório ausente: {key}")
                data[key] = self._get_default_value_for_key(key)

        # Validar estrutura da análise cruzada
        if "analise_cruzada" in data:
            cross_keys = ["so_strategies", "wo_strategies",
                          "st_strategies", "wt_strategies"]
            for key in cross_keys:
                if key not in data["analise_cruzada"]:
                    data["analise_cruzada"][key] = []

        return data

    def _calculate_confidence_score(self, data: Dict[str, Any]) -> float:
        """
        Calcula score de confiança baseado na completude e qualidade dos dados.

        Returns:
            Score entre 0.0 e 1.0
        """
        total_checks = 0
        passed_checks = 0

        # Verificar quantidade mínima de itens em cada quadrante (mínimo 3)
        for quadrant in ["forcas", "fraquezas", "oportunidades", "ameacas"]:
            total_checks += 1
            if len(data.get(quadrant, [])) >= 3:
                passed_checks += 1

        # Verificar presença de quantificações
        for quadrant in ["forcas", "fraquezas", "oportunidades", "ameacas"]:
            items = data.get(quadrant, [])
            if items:
                total_checks += 1
                # Verificar se todos os itens têm impacto
                if all(isinstance(item.get("impacto"), (int, float)) for item in items):
                    passed_checks += 1

        # Verificar probabilidade em oportunidades e ameaças
        for quadrant in ["oportunidades", "ameacas"]:
            items = data.get(quadrant, [])
            if items:
                total_checks += 1
                if all(isinstance(item.get("probabilidade"), (int, float)) for item in items):
                    passed_checks += 1

        # Verificar análise cruzada
        if "analise_cruzada" in data:
            total_checks += 1
            strategies_count = sum(
                len(data["analise_cruzada"].get(key, []))
                for key in ["so_strategies", "wo_strategies", "st_strategies", "wt_strategies"]
            )
            if strategies_count >= 4:  # Pelo menos 1 estratégia por tipo
                passed_checks += 1

        # Verificar matriz de priorização
        if "matriz_priorizacao" in data:
            total_checks += 1
            has_actions = all(
                len(data["matriz_priorizacao"].get(prazo, [])) > 0
                for prazo in ["acoes_curto_prazo", "acoes_medio_prazo", "acoes_longo_prazo"]
            )
            if has_actions:
                passed_checks += 1

        # Verificar score estratégico
        if "score_estrategico" in data:
            total_checks += 1
            has_scores = all(
                isinstance(data["score_estrategico"].get(metric), (int, float))
                for metric in ["posicao_competitiva", "potencial_crescimento", "nivel_risco"]
            )
            if has_scores and data["score_estrategico"].get("recomendacao_geral"):
                passed_checks += 1

        # Verificar qualidade das descrições (comprimento mínimo)
        total_checks += 1
        quality_items = 0
        total_items = 0

        for quadrant in ["forcas", "fraquezas", "oportunidades", "ameacas"]:
            for item in data.get(quadrant, []):
                total_items += 1
                if len(item.get("descricao", "")) > 50:
                    quality_items += 1

        if total_items > 0 and (quality_items / total_items) > 0.8:
            passed_checks += 1

        return passed_checks / total_checks if total_checks > 0 else 0.0

    def _get_empty_result(self) -> Dict[str, Any]:
        """Retorna estrutura vazia quando não há dados."""
        return {
            "forcas": [],
            "fraquezas": [],
            "oportunidades": [],
            "ameacas": [],
            "analise_cruzada": {
                "so_strategies": [],
                "wo_strategies": [],
                "st_strategies": [],
                "wt_strategies": []
            },
            "matriz_priorizacao": {
                "acoes_curto_prazo": [],
                "acoes_medio_prazo": [],
                "acoes_longo_prazo": []
            },
            "score_estrategico": {
                "posicao_competitiva": 0,
                "potencial_crescimento": 0,
                "nivel_risco": 0,
                "recomendacao_geral": "Dados insuficientes para análise"
            }
        }

    def _get_default_value_for_key(self, key: str) -> Any:
        """Retorna valor padrão para campos ausentes."""
        defaults = {
            "forcas": [],
            "fraquezas": [],
            "oportunidades": [],
            "ameacas": [],
            "analise_cruzada": {
                "so_strategies": [],
                "wo_strategies": [],
                "st_strategies": [],
                "wt_strategies": []
            },
            "matriz_priorizacao": {
                "acoes_curto_prazo": [],
                "acoes_medio_prazo": [],
                "acoes_longo_prazo": []
            },
            "score_estrategico": {
                "posicao_competitiva": 0,
                "potencial_crescimento": 0,
                "nivel_risco": 0,
                "recomendacao_geral": "Análise não disponível"
            }
        }
        return defaults.get(key, [])
