"""
Testes para o Channels Reviews Service
"""
import json
import pytest
from decimal import Decimal
from datetime import datetime, timezone
from unittest.mock import Mock, AsyncMock, patch

from app.services.research.channels_reviews_service import ChannelsReviewsService
from app.core import ResearchRequest, ResearchResult


@pytest.fixture
def mock_provider():
    """Mock do provider para testes."""
    provider = Mock()
    provider.search = AsyncMock()
    provider._mock_mode = False
    return provider


@pytest.fixture
def service(mock_provider):
    """Instância do serviço para testes."""
    return ChannelsReviewsService(mock_provider)


@pytest.fixture
def valid_request():
    """Request válido para testes."""
    return ResearchRequest(
        client_id="test-client-123",
        company_name="Tech Solutions BR",
        company_url="https://techsolutions.com.br",
        additional_context={
            "industry": "SaaS",
            "company_size": "50-200",
            "target_market": "B2B",
            "primary_channel": "digital"
        }
    )


class TestChannelsReviewsService:
    """Testes para o ChannelsReviewsService."""

    def test_service_metadata(self, service):
        """Testa metadados do serviço."""
        assert service.get_name() == "channels_reviews"
        assert service.get_version() == "1.0.0"
        assert service.get_cost() == Decimal("0.005")
        assert "canais de comunicação" in service.get_description()
        assert service.get_required_fields() == ["company_name", "company_url"]
        assert "industry" in service.get_optional_fields()

    @pytest.mark.asyncio
    async def test_validate_request_valid(self, service, valid_request):
        """Testa validação com request válido."""
        result = await service.validate_request(valid_request)
        assert result is True

    @pytest.mark.asyncio
    async def test_validate_request_missing_name(self, service):
        """Testa validação sem nome da empresa."""
        request = ResearchRequest(
            client_id="test-client-123",
            company_name="",
            company_url="https://example.com"
        )
        result = await service.validate_request(request)
        assert result is False

    @pytest.mark.asyncio
    async def test_validate_request_invalid_url(self, service):
        """Testa validação com URL inválida."""
        request = ResearchRequest(
            client_id="test-client-123",
            company_name="Test Company",
            company_url="invalid-url"
        )
        result = await service.validate_request(request)
        assert result is False

    @pytest.mark.asyncio
    async def test_execute_success(self, service, mock_provider, valid_request):
        """Testa execução bem-sucedida do serviço."""
        # Mock da resposta do provider
        mock_response = {
            "communication_channels": {
                "website": {
                    "status": "active",
                    "effectiveness": "high",
                    "features": ["Responsive", "Live chat"],
                    "issues": []
                },
                "social_media": {
                    "platforms": {
                        "linkedin": {"followers": "10K", "engagement": "high", "posts_per_week": 5}
                    },
                    "overall_presence": "strong",
                    "content_strategy": "Educational content"
                }
            },
            "sales_channels": {
                "primary_channels": [
                    {"type": "direct_sales", "contribution": "60%",
                        "effectiveness": "high"}
                ]
            },
            "customer_reviews": {
                "google_reviews": {
                    "rating": 4.5,
                    "total_reviews": 150,
                    "recent_trend": "improving",
                    "response_rate": "90%"
                }
            },
            "feedback_analysis": {
                "sentiment_distribution": {
                    "positive": "70%",
                    "neutral": "20%",
                    "negative": "10%"
                }
            }
        }
        mock_provider.search.return_value = mock_response

        result = await service.execute(valid_request)

        assert result.success is True
        assert result.service_name == "channels_reviews"
        assert result.cost == Decimal("0.005")
        assert "communication_channels" in result.data
        assert "recommendations" in result.data
        assert result.confidence_score > 0

    @pytest.mark.asyncio
    async def test_execute_with_mock_mode(self, service, valid_request):
        """Testa execução em modo mock."""
        # Ativar modo mock
        service.provider._mock_mode = True

        result = await service.execute(valid_request)

        assert result.success is True
        assert result.data is not None
        assert "communication_channels" in result.data
        assert "sales_channels" in result.data
        assert "customer_reviews" in result.data
        assert "feedback_analysis" in result.data

    @pytest.mark.asyncio
    async def test_execute_json_decode_error(self, service, mock_provider, valid_request):
        """Testa tratamento de erro JSON."""
        mock_provider.search.return_value = "invalid json {{"

        result = await service.execute(valid_request)

        assert result.success is False
        assert result.error == "Erro ao processar resposta do modelo"
        assert result.cost == Decimal("0")

    @pytest.mark.asyncio
    async def test_execute_provider_exception(self, service, mock_provider, valid_request):
        """Testa tratamento de exceção do provider."""
        mock_provider.search.side_effect = Exception("Provider error")

        result = await service.execute(valid_request)

        assert result.success is False
        assert "Provider error" in result.error
        assert result.cost == Decimal("0")

    def test_get_sample_output_structure(self, service):
        """Testa estrutura do sample output."""
        sample = service.get_sample_output()

        # Verificar estrutura principal
        assert "communication_channels" in sample
        assert "sales_channels" in sample
        assert "customer_reviews" in sample
        assert "feedback_analysis" in sample
        assert "metadata" in sample
        assert "recommendations" in sample

        # Verificar subseções
        comm_channels = sample["communication_channels"]
        assert all(k in comm_channels for k in [
                   "website", "social_media", "email", "phone", "chat"])

        # Verificar scores
        metadata = sample["metadata"]
        assert "channel_effectiveness_score" in metadata
        assert "online_reputation_score" in metadata

    def test_calculate_confidence_score(self, service):
        """Testa cálculo do score de confiança."""
        # Dados completos
        complete_data = {
            "communication_channels": {
                "website": {"status": "active"},
                "social_media": {"platforms": {"linkedin": {}}},
                "email": {"newsletter": True},
                "phone": {"available": True},
                "chat": {"live_chat": True}
            },
            "sales_channels": {
                "primary_channels": [{"type": "direct"}]
            },
            "customer_reviews": {
                "google_reviews": {"rating": 4.5},
                "trustpilot": {"rating": 4.0}
            },
            "feedback_analysis": {
                "sentiment_distribution": {"positive": "70%"},
                "common_praises": ["Good service"]
            }
        }
        score = service._calculate_confidence_score(complete_data)
        assert score > 0.8

        # Dados vazios
        empty_data = {}
        score = service._calculate_confidence_score(empty_data)
        assert score == 0.5

    def test_calculate_channel_effectiveness_score(self, service):
        """Testa cálculo do score de efetividade dos canais."""
        # Alta efetividade
        high_effectiveness_data = {
            "communication_channels": {
                "website": {"effectiveness": "high", "status": "active"},
                "social_media": {
                    "overall_presence": "strong",
                    "platforms": {
                        "linkedin": {"followers": "10K"},
                        "instagram": {"followers": "5K"}
                    }
                },
                "email": {"newsletter": True},
                "phone": {"available": True},
                "chat": {"live_chat": True}
            },
            "sales_channels": {
                "primary_channels": [
                    {"type": "direct"},
                    {"type": "online"},
                    {"type": "partners"}
                ],
                "e_commerce": {"has_online_store": True},
                "marketplaces": {"present_in": ["Amazon", "ML"]},
                "sales_team": {"inside_sales": True}
            },
            "feedback_analysis": {
                "company_responsiveness": {
                    "average_response_time": "within 24 hours",
                    "response_quality": "professional and helpful"
                }
            }
        }
        score = service._calculate_channel_effectiveness_score(
            high_effectiveness_data)
        assert score > 0.8

        # Baixa efetividade
        low_effectiveness_data = {
            "communication_channels": {
                "website": {"effectiveness": "low"},
                "social_media": {"overall_presence": "weak"}
            },
            "sales_channels": {
                "primary_channels": []
            }
        }
        score = service._calculate_channel_effectiveness_score(
            low_effectiveness_data)
        assert score < 0.5

    def test_calculate_online_reputation_score(self, service):
        """Testa cálculo do score de reputação online."""
        # Alta reputação
        high_reputation_data = {
            "customer_reviews": {
                "google_reviews": {
                    "rating": 4.8,
                    "total_reviews": 500,
                    "response_rate": "95%"
                },
                "trustpilot": {"rating": 4.5, "total_reviews": 300},
                "reclame_aqui": {
                    "rating": 8.5,
                    "response_rate": "90%",
                    "resolution_rate": "85%"
                }
            },
            "feedback_analysis": {
                "sentiment_distribution": {
                    "positive": "80%",
                    "neutral": "15%",
                    "negative": "5%"
                }
            }
        }
        score = service._calculate_online_reputation_score(
            high_reputation_data)
        assert score > 0.8

        # Baixa reputação
        low_reputation_data = {
            "customer_reviews": {
                "google_reviews": {"rating": 2.5, "total_reviews": 10}
            },
            "feedback_analysis": {
                "sentiment_distribution": {
                    "positive": "20%",
                    "neutral": "30%",
                    "negative": "50%"
                }
            }
        }
        score = service._calculate_online_reputation_score(low_reputation_data)
        assert score < 0.5

    def test_generate_recommendations(self, service):
        """Testa geração de recomendações."""
        data_with_gaps = {
            "communication_channels": {
                "website": {"effectiveness": "low", "issues": ["Slow load time"]},
                "social_media": {
                    "overall_presence": "weak",
                    "platforms": {
                        "facebook": {"engagement": "low"}
                    }
                },
                "chat": {"live_chat": False, "chatbot": False}
            },
            "sales_channels": {
                "e_commerce": {"has_online_store": False}
            },
            "customer_reviews": {
                "google_reviews": {
                    "total_reviews": 20,
                    "response_rate": "50%"
                }
            },
            "feedback_analysis": {
                "sentiment_distribution": {"negative": "25%"},
                "common_complaints": ["High prices", "Slow support"]
            }
        }

        recommendations = service._generate_recommendations(
            data_with_gaps, "SaaS", "B2C"
        )

        assert len(recommendations) > 0
        assert all("area" in rec for rec in recommendations)
        assert all("suggestion" in rec for rec in recommendations)
        assert all("priority" in rec for rec in recommendations)

        # Verificar se recomendações específicas foram geradas
        areas = [rec["area"] for rec in recommendations]
        assert any("website" in area for area in areas)
        assert any("review" in area for area in areas)

    def test_normalize_sentiment_distribution(self, service):
        """Testa normalização da distribuição de sentimento."""
        # Distribuição que não soma 100%
        invalid_sentiment = {
            "sentiment_distribution": {
                "positive": "50%",
                "neutral": "30%",
                "negative": "30%"  # Soma 110%
            }
        }

        normalized = service._normalize_feedback_analysis(invalid_sentiment)

        # Deve retornar distribuição padrão
        assert normalized["sentiment_distribution"]["positive"] == "33%"
        assert normalized["sentiment_distribution"]["neutral"] == "34%"
        assert normalized["sentiment_distribution"]["negative"] == "33%"

    def test_process_result_invalid_json(self, service):
        """Testa processamento de resultado com JSON inválido."""
        # Agora o método deve lançar uma exceção para JSON inválido
        with pytest.raises(json.JSONDecodeError):
            service._process_result("not a json", "Test Company")

    def test_get_mock_data_selection(self, service):
        """Testa seleção correta de dados mock."""
        # E-commerce
        ecommerce_data = service._get_mock_data(
            "Shop Online", "e-commerce", "B2C", "50-200"
        )
        assert "mobile_app" in str(ecommerce_data["sales_channels"])

        # B2B Enterprise
        b2b_data = service._get_mock_data(
            "Enterprise Corp", "SaaS", "B2B", "500+"
        )
        assert "enterprise_sales" in str(b2b_data["sales_channels"])

        # Default
        default_data = service._get_mock_data(
            "Small Biz", "Services", "B2B", "10-50"
        )
        assert "direct_sales" in str(default_data["sales_channels"])

    def test_recommendations_priority_sorting(self, service):
        """Testa ordenação de recomendações por prioridade."""
        data = {
            "communication_channels": {
                "website": {"effectiveness": "low"},
                "chat": {"live_chat": False}
            },
            "customer_reviews": {
                "google_reviews": {"total_reviews": 10}
            },
            "feedback_analysis": {
                "sentiment_distribution": {"negative": "30%"},
                "common_complaints": ["Bad service"]
            }
        }

        recommendations = service._generate_recommendations(
            data, "SaaS", "B2B")

        # Verificar que recomendações de alta prioridade vêm primeiro
        if len(recommendations) > 1:
            first_priority = recommendations[0].get("priority")
            last_priority = recommendations[-1].get("priority")

            priority_order = {"high": 3, "medium": 2, "low": 1}
            assert priority_order.get(
                first_priority, 0) >= priority_order.get(last_priority, 0)

    def test_error_result_creation(self, service):
        """Testa criação de resultado de erro."""
        error_result = service._create_error_result("Test error", 1.5)

        assert error_result.success is False
        assert error_result.error == "Test error"
        assert error_result.cost == Decimal("0")
        assert error_result.processing_time_seconds == 1.5
        assert error_result.confidence_score == 0.0

    @pytest.mark.asyncio
    async def test_execute_with_optional_fields(self, service, mock_provider):
        """Testa execução com campos opcionais não preenchidos."""
        request = ResearchRequest(
            client_id="test-client-123",
            company_name="Simple Company",
            company_url="https://simple.com"
        )

        mock_provider.search.return_value = "{}"

        result = await service.execute(request)

        # Deve executar sem erros mesmo sem campos opcionais
        assert result.success is True
        assert result.data["metadata"]["industry"] == "não especificada"
