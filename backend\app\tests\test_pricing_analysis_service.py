"""
Testes para PricingAnalysisService.

Testa a análise de estratégias de precificação incluindo:
- Validação de requisições
- Processamento de resultados  
- Cálculo de confidence score e pricing maturity score
- Geração de recomendações
- Tratamento de erros
- Normalização de dados
- Mock data para diferentes indústrias
"""
import pytest
import json
from unittest.mock import Mock, AsyncMock, patch
from decimal import Decimal
from datetime import datetime

from app.services.research import PricingAnalysisService
from app.core import ResearchRequest, ResearchResult


class TestPricingAnalysisService:
    """Testes para o serviço de análise de pricing."""

    @pytest.fixture
    def mock_provider(self):
        """Mock do provider para testes."""
        provider = Mock()
        provider.search = AsyncMock()
        return provider

    @pytest.fixture
    def service(self, mock_provider):
        """Instância do serviço para testes."""
        return PricingAnalysisService(mock_provider)

    @pytest.fixture
    def valid_request(self):
        """Request válido para testes."""
        return ResearchRequest(
            client_id="test-123",
            company_name="PricingCorp",
            company_url="https://pricingcorp.com",
            additional_context={
                "industry": "SaaS",
                "target_market": "B2B"
            }
        )

    @pytest.fixture
    def sample_pricing_response(self):
        """Resposta de exemplo do provider."""
        return {
            "pricing_models": {
                "primary_model": "subscription",
                "models_used": ["subscription", "usage-based", "freemium"],
                "pricing_tiers": [
                    {
                        "name": "Free",
                        "price": 0,
                        "currency": "USD",
                        "billing_cycle": "monthly",
                        "features": ["Basic features", "Community support"],
                        "limitations": ["10 users", "1GB storage"]
                    },
                    {
                        "name": "Pro",
                        "price": 49,
                        "currency": "USD",
                        "billing_cycle": "monthly",
                        "features": ["All features", "Priority support", "API access"],
                        "limitations": ["100 users", "100GB storage"]
                    },
                    {
                        "name": "Enterprise",
                        "price": "custom",
                        "currency": "USD",
                        "billing_cycle": "annual",
                        "features": ["Custom features", "Dedicated support", "SLA"],
                        "limitations": []
                    }
                ]
            },
            "competitive_analysis": {
                "market_position": "premium",
                "price_comparison": {
                    "vs_market_average": "+25%",
                    "vs_direct_competitors": "+15%",
                    "justification": "Superior features and support"
                },
                "competitors": [
                    {
                        "name": "Competitor A",
                        "pricing_range": "$29-$199/month",
                        "model": "subscription"
                    },
                    {
                        "name": "Competitor B",
                        "pricing_range": "$39-$299/month",
                        "model": "subscription + usage"
                    }
                ]
            },
            "pricing_strategy": {
                "elasticity": "moderate",
                "value_proposition": "premium quality and features",
                "monetization_strategies": [
                    "Tiered pricing for different segments",
                    "Usage-based pricing for enterprise",
                    "Freemium for user acquisition"
                ],
                "upsell_opportunities": [
                    "Free to Pro conversion",
                    "Pro to Enterprise expansion",
                    "Add-on services"
                ]
            },
            "revenue_analysis": {
                "arpu_estimate": "$156/month",
                "ltv_estimate": "$3,744",
                "cac_payback_period": "6 months",
                "revenue_growth_potential": "high"
            }
        }

    def test_service_metadata(self, service):
        """Testa metadados do serviço."""
        assert service.get_name() == "pricing_analysis"
        assert service.get_version() == "1.0.0"
        assert service.get_cost() == Decimal("0.005")
        assert "precificação" in service.get_description()

    def test_required_and_optional_fields(self, service):
        """Testa campos obrigatórios e opcionais."""
        required = service.get_required_fields()
        assert "company_name" in required
        assert "company_url" in required
        assert len(required) == 2

        optional = service.get_optional_fields()
        assert "industry" in optional
        assert "target_market" in optional
        assert "company_size" in optional
        assert "competitors" in optional

    @pytest.mark.asyncio
    async def test_validate_request_success(self, service, valid_request):
        """Testa validação de request válido."""
        assert await service.validate_request(valid_request) is True

    @pytest.mark.asyncio
    async def test_validate_request_missing_name(self, service):
        """Testa validação com nome faltando."""
        request = ResearchRequest(
            client_id="test",
            company_name="",
            company_url="https://test.com"
        )
        assert await service.validate_request(request) is False

    @pytest.mark.asyncio
    async def test_validate_request_missing_url(self, service):
        """Testa validação com URL faltando."""
        request = ResearchRequest(
            client_id="test",
            company_name="Test Corp",
            company_url=""
        )
        assert await service.validate_request(request) is False

    @pytest.mark.asyncio
    async def test_validate_request_invalid_url(self, service):
        """Testa validação com URL inválida."""
        request = ResearchRequest(
            client_id="test",
            company_name="Test Corp",
            company_url="invalid-url"
        )
        assert await service.validate_request(request) is False

    @pytest.mark.asyncio
    async def test_execute_success(self, service, mock_provider, valid_request, sample_pricing_response):
        """Testa execução bem-sucedida."""
        # Configurar mock
        mock_provider.search.return_value = sample_pricing_response

        # Executar
        result = await service.execute(valid_request)

        # Verificar resultado
        assert result.success is True
        assert result.service_name == "pricing_analysis"
        assert result.cost == Decimal("0.005")
        assert result.confidence_score > 0.7
        assert "pricing_models" in result.data
        assert "competitive_analysis" in result.data
        assert "metadata" in result.data
        assert "recommendations" in result.data

        # Verificar metadados
        metadata = result.data["metadata"]
        assert metadata["company_url"] == "https://pricingcorp.com"
        assert metadata["industry"] == "SaaS"
        assert metadata["target_market"] == "B2B"
        assert "pricing_maturity_score" in metadata

    @pytest.mark.asyncio
    async def test_execute_json_string_response(self, service, mock_provider, valid_request, sample_pricing_response):
        """Testa com resposta como string JSON."""
        # Configurar mock para retornar string JSON
        mock_provider.search.return_value = json.dumps(sample_pricing_response)

        # Executar
        result = await service.execute(valid_request)

        # Verificar resultado
        assert result.success is True
        assert "subscription" in result.data["pricing_models"]["models_used"]
        assert result.data["pricing_models"]["primary_model"] == "subscription"

    @pytest.mark.asyncio
    async def test_execute_invalid_json(self, service, mock_provider, valid_request):
        """Testa com resposta JSON inválida."""
        # REMOVER MOCK MODE para evitar dados mock
        if hasattr(service.provider, '_mock_mode'):
            delattr(service.provider, '_mock_mode')

        # Configurar mock para retornar JSON inválido
        mock_provider.search.return_value = "invalid json {{"

        # Executar
        result = await service.execute(valid_request)

        # Verificar resultado - deve retornar estrutura vazia mas com sucesso
        assert result.success is True
        assert result.data["pricing_models"]["primary_model"] == "unknown"
        assert result.data["pricing_models"]["models_used"] == []

    @pytest.mark.asyncio
    async def test_execute_exception(self, service, mock_provider, valid_request):
        """Testa tratamento de exceção."""
        # REMOVER MOCK MODE para evitar dados mock
        if hasattr(service.provider, '_mock_mode'):
            delattr(service.provider, '_mock_mode')

        # Configurar mock para lançar exceção
        mock_provider.search.side_effect = Exception("API Error")

        # Executar
        result = await service.execute(valid_request)

        # Verificar resultado
        assert result.success is False
        assert result.error == "API Error"
        assert result.cost == Decimal("0")

    @pytest.mark.asyncio
    async def test_execute_with_mock_mode(self, service, valid_request):
        """Testa execução em modo mock."""
        # Configurar provider com mock mode
        service.provider._mock_mode = True

        # Executar
        result = await service.execute(valid_request)

        # Verificar resultado
        assert result.success is True
        assert len(result.data["pricing_models"]["pricing_tiers"]) > 0
        assert result.data["competitive_analysis"]["competitors"] != []

    def test_get_sample_output(self, service):
        """Testa estrutura do output de exemplo."""
        sample = service.get_sample_output()

        # Verificar estrutura
        assert "pricing_models" in sample
        assert "competitive_analysis" in sample
        assert "pricing_strategy" in sample
        assert "revenue_analysis" in sample
        assert "metadata" in sample
        assert "recommendations" in sample

        # Verificar conteúdo
        assert sample["pricing_models"]["primary_model"] == "subscription"
        assert len(sample["pricing_models"]["pricing_tiers"]) == 3
        assert sample["metadata"]["confidence_score"] == 0.85

    def test_build_prompts(self, service):
        """Testa construção de prompts."""
        system_prompt = service._build_system_prompt()
        assert "especialista em estratégia de precificação" in system_prompt
        assert "monetização" in system_prompt

        user_prompt = service._build_user_prompt(
            "PricingCorp", "https://pricingcorp.com", "SaaS", "B2B"
        )
        assert "PricingCorp" in user_prompt
        assert "https://pricingcorp.com" in user_prompt
        assert "SaaS" in user_prompt
        assert "B2B" in user_prompt
        assert "JSON" in user_prompt

    def test_process_result_valid_data(self, service, sample_pricing_response):
        """Testa processamento de dados válidos."""
        result = service._process_result(
            sample_pricing_response, "PricingCorp")

        # Verificar estrutura processada
        assert result["pricing_models"]["primary_model"] == "subscription"
        assert len(result["pricing_models"]["pricing_tiers"]) == 3
        assert result["competitive_analysis"]["market_position"] == "premium"

    def test_process_result_missing_sections(self, service):
        """Testa processamento com seções faltando."""
        incomplete_data = {
            "pricing_models": {
                "primary_model": "subscription",
                "models_used": ["subscription"]
            }
            # Faltam outras seções
        }

        result = service._process_result(incomplete_data, "PricingCorp")

        # Deve ter todas as seções, mesmo vazias
        assert "pricing_models" in result
        assert "competitive_analysis" in result
        assert result["pricing_models"]["primary_model"] == "subscription"
        assert result["competitive_analysis"]["market_position"] == "unknown"

    def test_normalize_pricing_tiers(self, service):
        """Testa normalização de tiers de preço."""
        tiers = [
            {
                "name": "Basic",
                "price": 10
                # Faltam campos
            },
            {
                "name": "Pro",
                "price": 50,
                "currency": "USD",
                "billing_cycle": "monthly",
                "features": ["Feature 1", "Feature 2"],
                "limitations": ["Limit 1"]
            }
        ]

        normalized = service._normalize_pricing_tiers(tiers)

        assert len(normalized) == 2
        assert normalized[0]["currency"] == "USD"  # Valor padrão
        assert normalized[0]["billing_cycle"] == "monthly"  # Valor padrão
        assert normalized[0]["features"] == []  # Lista vazia padrão
        assert normalized[1]["features"] == ["Feature 1", "Feature 2"]

    def test_calculate_confidence_score_high(self, service, sample_pricing_response):
        """Testa cálculo de confidence score alto."""
        score = service._calculate_confidence_score(sample_pricing_response)
        assert score > 0.8  # Dados completos devem ter score alto

    def test_calculate_confidence_score_low(self, service):
        """Testa cálculo de confidence score baixo."""
        empty_data = service._get_empty_pricing_analysis()
        score = service._calculate_confidence_score(empty_data)
        assert score == 0.0  # Dados vazios devem ter score zero

    def test_calculate_confidence_score_medium(self, service):
        """Testa cálculo de confidence score médio."""
        partial_data = {
            "pricing_models": {
                "primary_model": "subscription",
                "models_used": ["subscription"],
                "pricing_tiers": []
            },
            "competitive_analysis": {
                "market_position": "unknown",
                "competitors": []
            },
            "pricing_strategy": {
                "elasticity": "moderate",
                "monetization_strategies": ["Strategy 1"]
            },
            "revenue_analysis": {
                "arpu_estimate": "N/A",
                "revenue_growth_potential": "unknown"
            }
        }

        score = service._calculate_confidence_score(partial_data)
        assert 0.2 <= score <= 0.5  # Score médio para dados parciais

    def test_calculate_pricing_maturity_score(self, service, sample_pricing_response):
        """Testa cálculo de pricing maturity score."""
        score = service._calculate_pricing_maturity_score(
            sample_pricing_response)

        # Com 3 modelos, 3 tiers, 2 competidores e 3 estratégias, deve ter score alto
        assert score > 0.7

    def test_calculate_pricing_maturity_score_low(self, service):
        """Testa cálculo de pricing maturity score baixo."""
        basic_data = {
            "pricing_models": {
                "models_used": ["subscription"],
                "pricing_tiers": [{"name": "Basic", "price": 10}]
            },
            "competitive_analysis": {
                "competitors": []
            },
            "pricing_strategy": {
                "monetization_strategies": ["Basic strategy"]
            }
        }

        score = service._calculate_pricing_maturity_score(basic_data)
        assert score < 0.5  # Score baixo para estratégia básica

    def test_generate_recommendations_few_tiers(self, service):
        """Testa geração de recomendações com poucos tiers."""
        data = {
            "pricing_models": {
                "pricing_tiers": [
                    {"name": "Basic", "price": 10, "billing_cycle": "monthly"}
                ]
            },
            "pricing_strategy": {
                "elasticity": "moderate"
            }
        }

        recommendations = service._generate_recommendations(data, "SaaS")

        assert len(recommendations) >= 2
        assert any(r["category"] == "expansion" for r in recommendations)
        assert any("billing anual" in r["suggestion"] for r in recommendations)

    def test_generate_recommendations_high_elasticity(self, service):
        """Testa recomendações para alta elasticidade."""
        data = {
            "pricing_models": {
                "pricing_tiers": [
                    {"name": "Tier1", "billing_cycle": "monthly"},
                    {"name": "Tier2", "billing_cycle": "monthly"},
                    {"name": "Tier3", "billing_cycle": "annual"}
                ]
            },
            "pricing_strategy": {
                "elasticity": "high"
            }
        }

        recommendations = service._generate_recommendations(data, "E-commerce")

        assert any("A/B testing" in r["suggestion"] for r in recommendations)

    def test_get_empty_pricing_analysis_structure(self, service):
        """Testa estrutura vazia de análise de pricing."""
        empty = service._get_empty_pricing_analysis()

        # Verificar todas as seções
        assert all(section in empty for section in [
            "pricing_models", "competitive_analysis", "pricing_strategy", "revenue_analysis"
        ])

        # Verificar valores padrão
        assert empty["pricing_models"]["primary_model"] == "unknown"
        assert empty["pricing_models"]["models_used"] == []
        assert empty["revenue_analysis"]["arpu_estimate"] == "N/A"

    def test_get_mock_data_saas(self, service):
        """Testa mock data para indústria SaaS."""
        mock_data = service._get_mock_data("TestCorp", "SaaS")

        assert mock_data["pricing_models"]["primary_model"] == "subscription"
        assert len(mock_data["pricing_models"]["models_used"]) == 3
        assert "Starter" in [tier["name"]
                             for tier in mock_data["pricing_models"]["pricing_tiers"]]

    def test_get_mock_data_ecommerce(self, service):
        """Testa mock data para indústria e-commerce."""
        mock_data = service._get_mock_data("TestShop", "e-commerce")

        assert mock_data["pricing_models"]["primary_model"] == "marketplace"
        assert "transaction-based" in mock_data["pricing_models"]["models_used"]
        assert any("transaction fee" in str(
            tier["features"]) for tier in mock_data["pricing_models"]["pricing_tiers"])

    def test_get_mock_data_default(self, service):
        """Testa mock data padrão."""
        mock_data = service._get_mock_data("GenericCorp", "manufacturing")

        assert mock_data["pricing_models"]["primary_model"] == "subscription"
        assert "Free" in [tier["name"]
                          for tier in mock_data["pricing_models"]["pricing_tiers"]]

    def test_create_error_result(self, service):
        """Testa criação de resultado de erro."""
        error_result = service._create_error_result("Test error", 1.5)

        assert error_result.success is False
        assert error_result.error == "Test error"
        assert error_result.cost == Decimal("0")
        assert error_result.processing_time_seconds == 1.5
        assert error_result.data == service._get_empty_pricing_analysis()

    @pytest.mark.asyncio
    async def test_execute_without_target_market(self, service, mock_provider):
        """Testa execução sem campo target_market."""
        request = ResearchRequest(
            client_id="test",
            company_name="PricingCorp",
            company_url="https://pricingcorp.com",
            additional_context={"industry": "SaaS"}
            # Sem target_market
        )

        mock_provider.search.return_value = {
            "pricing_models": {"primary_model": "subscription", "models_used": []},
            "competitive_analysis": {},
            "pricing_strategy": {},
            "revenue_analysis": {}
        }

        result = await service.execute(request)

        assert result.success is True
        # Valor padrão
        assert result.data["metadata"]["target_market"] == "B2B/B2C"
