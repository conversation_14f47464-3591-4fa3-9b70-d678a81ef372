"""
Tech Diagnostic Service - Serviço para análise técnica completa de websites.

Este serviço realiza diagnóstico técnico detalhado incluindo:
- Performance (Core Web Vitals, loading time, optimizations)
- Segurança (HTTPS, security headers, vulnerabilities)
- Acessibilidade (WCAG 2.1 compliance, contraste, navegação)
- SEO Técnico (meta tags, structured data, sitemap)
- Melhores Práticas (cache, compression, image optimization)

Custo: $0.010 por execução
"""
import json
import logging
from typing import Dict, Any, List, Optional
from decimal import Decimal
from datetime import datetime, UTC
import random

from app.core import (
    IResearchService,
    ResearchRequest,
    ResearchResult,
    ResearchProviderError,
    Timer
)

logger = logging.getLogger(__name__)


class TechDiagnosticService(IResearchService):
    """
    Serviço para análise técnica completa de websites.

    Utiliza Perplexity API para coletar informações sobre performance,
    segurança, acessibilidade, SEO e melhores práticas técnicas.
    """

    def __init__(self, perplexity_provider):
        """
        Args:
            perplexity_provider: Instância do provider Perplexity
        """
        self.provider = perplexity_provider
        self._version = "1.0.0"
        self._cost = Decimal("0.010")

    def get_name(self) -> str:
        return "tech_diagnostic"

    def get_version(self) -> str:
        return self._version

    def get_description(self) -> str:
        return (
            "Realiza diagnóstico técnico completo incluindo performance "
            "(Core Web Vitals), segurança (HTTPS, headers), acessibilidade "
            "(WCAG 2.1), SEO técnico e melhores práticas de desenvolvimento."
        )

    def get_cost(self) -> Decimal:
        return self._cost

    def get_required_fields(self) -> List[str]:
        return ["company_name", "company_url"]

    def get_optional_fields(self) -> List[str]:
        return ["mobile_priority", "industry_type", "target_audience"]

    async def validate_request(self, request: ResearchRequest) -> bool:
        """Valida se a requisição tem os campos necessários."""
        # Verifica campos obrigatórios
        if not request.company_name or not request.company_url:
            return False

        # URL deve ser válida
        url = request.company_url.lower()
        if not (url.startswith("http://") or url.startswith("https://")):
            return False

        return True

    async def execute(self, request: ResearchRequest) -> ResearchResult:
        """
        Executa a análise técnica completa.

        Args:
            request: Requisição com dados da empresa

        Returns:
            ResearchResult com diagnóstico técnico completo
        """
        with Timer() as timer:
            try:
                # Por enquanto, usando mock data
                # TODO: Implementar integração real com Perplexity
                processed_data = self._get_mock_diagnostic_data(
                    request.company_name,
                    request.company_url
                )

                # Calcular score de confiança baseado na completude dos dados
                confidence_score = self._calculate_confidence_score(
                    processed_data)

                return ResearchResult(
                    service_name=self.get_name(),
                    service_version=self.get_version(),
                    timestamp=datetime.now(UTC),
                    cost=self._cost,
                    success=True,
                    data=processed_data,
                    processing_time_seconds=timer.elapsed,
                    confidence_score=confidence_score
                )

            except Exception as e:
                logger.error(f"Erro ao executar diagnóstico técnico: {str(e)}")
                return ResearchResult(
                    service_name=self.get_name(),
                    service_version=self.get_version(),
                    timestamp=datetime.now(UTC),
                    cost=Decimal("0"),  # Não cobra em caso de erro
                    success=False,
                    error=str(e),
                    processing_time_seconds=timer.elapsed
                )

    def get_sample_output(self) -> Dict[str, Any]:
        """Retorna exemplo de saída do serviço."""
        return {
            "performance": {
                "score": 85,
                "core_web_vitals": {
                    "lcp": {"value": 2.5, "score": "good", "unit": "seconds"},
                    "fid": {"value": 100, "score": "good", "unit": "milliseconds"},
                    "cls": {"value": 0.1, "score": "good", "unit": "score"}
                },
                "metrics": {
                    "first_contentful_paint": 1.8,
                    "speed_index": 3.4,
                    "time_to_interactive": 3.9,
                    "total_blocking_time": 150
                },
                "opportunities": [
                    {
                        "title": "Eliminate render-blocking resources",
                        "impact": "high",
                        "estimated_savings": "1.2s"
                    }
                ]
            },
            "security": {
                "score": 90,
                "https_enabled": True,
                "ssl_certificate": {
                    "valid": True,
                    "issuer": "Let's Encrypt",
                    "expires_in_days": 45
                },
                "security_headers": {
                    "content_security_policy": True,
                    "x_frame_options": True,
                    "x_content_type_options": True,
                    "strict_transport_security": True
                },
                "vulnerabilities": []
            },
            "accessibility": {
                "score": 78,
                "wcag_level": "AA",
                "issues": [
                    {
                        "severity": "moderate",
                        "type": "color-contrast",
                        "count": 5,
                        "description": "Elementos com contraste insuficiente"
                    }
                ]
            },
            "seo": {
                "score": 82,
                "meta_tags": {
                    "title": True,
                    "description": True,
                    "og_tags": True,
                    "schema_markup": False
                },
                "sitemap": True,
                "robots_txt": True
            },
            "best_practices": {
                "score": 88,
                "uses_https": True,
                "uses_http2": True,
                "image_optimization": "partial",
                "caching_strategy": "good",
                "minification": True
            },
            "overall_score": 84.6,
            "recommendations": [
                {
                    "category": "performance",
                    "priority": "high",
                    "title": "Otimizar Core Web Vitals",
                    "description": "Melhorar LCP reduzindo tamanho de recursos críticos",
                    "estimated_impact": "+10 pontos no score"
                }
            ]
        }

    def _get_mock_diagnostic_data(self, company_name: str, company_url: str) -> Dict[str, Any]:
        """
        Retorna dados mock realistas para desenvolvimento.

        TODO: Remover quando implementar integração real com Perplexity
        """
        # Gerar scores variados mas realistas
        perf_score = random.randint(60, 95)
        sec_score = random.randint(70, 100)
        a11y_score = random.randint(55, 90)
        seo_score = random.randint(65, 95)
        bp_score = random.randint(70, 92)

        # Core Web Vitals realistas
        lcp = round(random.uniform(1.5, 4.5), 1)
        fid = random.randint(20, 300)
        cls = round(random.uniform(0.0, 0.25), 3)

        return {
            "company_name": company_name,
            "company_url": company_url,
            "analysis_timestamp": datetime.now(UTC).isoformat(),
            "performance": {
                "score": perf_score,
                "core_web_vitals": {
                    "lcp": {
                        "value": lcp,
                        "score": "good" if lcp <= 2.5 else "needs-improvement" if lcp <= 4 else "poor",
                        "unit": "seconds",
                        "description": "Largest Contentful Paint"
                    },
                    "fid": {
                        "value": fid,
                        "score": "good" if fid <= 100 else "needs-improvement" if fid <= 300 else "poor",
                        "unit": "milliseconds",
                        "description": "First Input Delay"
                    },
                    "cls": {
                        "value": cls,
                        "score": "good" if cls <= 0.1 else "needs-improvement" if cls <= 0.25 else "poor",
                        "unit": "score",
                        "description": "Cumulative Layout Shift"
                    }
                },
                "metrics": {
                    "first_contentful_paint": round(random.uniform(0.8, 3.2), 1),
                    "speed_index": round(random.uniform(2.0, 5.5), 1),
                    "time_to_interactive": round(random.uniform(2.5, 6.0), 1),
                    "total_blocking_time": random.randint(50, 500),
                    "largest_contentful_paint": lcp,
                    "server_response_time": random.randint(100, 800)
                },
                "opportunities": self._generate_performance_opportunities(perf_score),
                "diagnostics": [
                    "JavaScript execution time: 2.3s",
                    "Main thread work: 4.1s",
                    "DOM size: 1,456 elements"
                ]
            },
            "security": {
                "score": sec_score,
                "https_enabled": sec_score > 70,
                "ssl_certificate": {
                    "valid": sec_score > 70,
                    "issuer": "Let's Encrypt" if sec_score > 70 else "None",
                    "expires_in_days": random.randint(30, 90) if sec_score > 70 else 0,
                    "protocol": "TLS 1.3" if sec_score > 85 else "TLS 1.2"
                },
                "security_headers": {
                    "content_security_policy": sec_score > 80,
                    "x_frame_options": sec_score > 70,
                    "x_content_type_options": sec_score > 75,
                    "strict_transport_security": sec_score > 70,
                    "referrer_policy": sec_score > 85,
                    "permissions_policy": sec_score > 90
                },
                "vulnerabilities": self._generate_vulnerabilities(sec_score),
                "recommendations": self._generate_security_recommendations(sec_score)
            },
            "accessibility": {
                "score": a11y_score,
                "wcag_level": "AAA" if a11y_score > 85 else "AA" if a11y_score > 70 else "A",
                "issues": self._generate_accessibility_issues(a11y_score),
                "passed_audits": [
                    "ARIA attributes are valid",
                    "Buttons have accessible names",
                    "Document has a title",
                    "HTML has lang attribute"
                ],
                "manual_checks_needed": [
                    "Logical tab order",
                    "Custom controls have ARIA labels",
                    "Visual order matches DOM order"
                ]
            },
            "seo": {
                "score": seo_score,
                "meta_tags": {
                    "title": True,
                    "title_length": random.randint(30, 60),
                    "description": seo_score > 60,
                    "description_length": random.randint(120, 160) if seo_score > 60 else 0,
                    "og_tags": seo_score > 70,
                    "twitter_cards": seo_score > 75,
                    "canonical_url": seo_score > 65,
                    "schema_markup": seo_score > 80
                },
                "structured_data": {
                    "found": seo_score > 80,
                    "types": ["Organization", "WebSite"] if seo_score > 80 else []
                },
                "sitemap": seo_score > 65,
                "robots_txt": seo_score > 60,
                "crawlability": {
                    "indexable": True,
                    "blocked_resources": random.randint(0, 5)
                },
                "recommendations": self._generate_seo_recommendations(seo_score)
            },
            "best_practices": {
                "score": bp_score,
                "uses_https": bp_score > 70,
                "uses_http2": bp_score > 75,
                "uses_http3": bp_score > 85,
                "image_optimization": "excellent" if bp_score > 85 else "good" if bp_score > 70 else "needs work",
                "caching_strategy": "aggressive" if bp_score > 80 else "moderate" if bp_score > 65 else "minimal",
                "minification": {
                    "html": bp_score > 70,
                    "css": bp_score > 75,
                    "javascript": bp_score > 75
                },
                "compression": {
                    "gzip": bp_score > 65,
                    "brotli": bp_score > 80
                },
                "pwa_ready": bp_score > 85,
                "recommendations": self._generate_best_practices_recommendations(bp_score)
            },
            "overall_score": round((perf_score + sec_score + a11y_score + seo_score + bp_score) / 5, 1),
            "top_recommendations": self._generate_top_recommendations(
                perf_score, sec_score, a11y_score, seo_score, bp_score
            ),
            "technology_stack": {
                "detected": [
                    "React" if random.random() > 0.5 else "Vue.js",
                    "Node.js" if random.random() > 0.6 else "PHP",
                    "Nginx" if random.random() > 0.5 else "Apache"
                ],
                "cdn": "Cloudflare" if random.random() > 0.6 else "None",
                "analytics": ["Google Analytics", "Hotjar"] if random.random() > 0.5 else ["Google Analytics"]
            }
        }

    def _generate_performance_opportunities(self, score: int) -> List[Dict[str, Any]]:
        """Gera oportunidades de melhoria de performance baseado no score."""
        opportunities = []

        if score < 90:
            opportunities.append({
                "title": "Eliminate render-blocking resources",
                "impact": "high",
                "estimated_savings": "1.2s",
                "description": "Recursos CSS e JavaScript bloqueiam a renderização inicial"
            })

        if score < 80:
            opportunities.append({
                "title": "Optimize images",
                "impact": "high",
                "estimated_savings": "2.5s",
                "description": "Imagens não otimizadas aumentam tempo de carregamento"
            })

        if score < 70:
            opportunities.append({
                "title": "Reduce JavaScript execution time",
                "impact": "medium",
                "estimated_savings": "0.8s",
                "description": "Scripts pesados impactam interatividade"
            })

        return opportunities

    def _generate_vulnerabilities(self, score: int) -> List[Dict[str, Any]]:
        """Gera lista de vulnerabilidades baseado no score de segurança."""
        vulnerabilities = []

        if score < 80:
            vulnerabilities.append({
                "severity": "medium",
                "type": "missing-security-headers",
                "description": "Headers de segurança importantes não configurados"
            })

        if score < 70:
            vulnerabilities.append({
                "severity": "high",
                "type": "outdated-dependencies",
                "description": "Bibliotecas JavaScript desatualizadas com vulnerabilidades conhecidas"
            })

        return vulnerabilities

    def _generate_accessibility_issues(self, score: int) -> List[Dict[str, Any]]:
        """Gera problemas de acessibilidade baseado no score."""
        issues = []

        if score < 90:
            issues.append({
                "severity": "moderate",
                "type": "color-contrast",
                "count": random.randint(3, 8),
                "description": "Elementos com contraste insuficiente entre texto e fundo"
            })

        if score < 75:
            issues.append({
                "severity": "serious",
                "type": "missing-alt-text",
                "count": random.randint(5, 15),
                "description": "Imagens sem texto alternativo"
            })

        if score < 60:
            issues.append({
                "severity": "critical",
                "type": "keyboard-navigation",
                "count": random.randint(2, 5),
                "description": "Elementos interativos não acessíveis via teclado"
            })

        return issues

    def _generate_security_recommendations(self, score: int) -> List[str]:
        """Gera recomendações de segurança."""
        recommendations = []

        if score < 90:
            recommendations.append(
                "Implementar Content Security Policy (CSP) restritiva")
        if score < 80:
            recommendations.append("Configurar HSTS com tempo mínimo de 1 ano")
        if score < 70:
            recommendations.append(
                "Atualizar para TLS 1.3 e desabilitar protocolos antigos")

        return recommendations

    def _generate_seo_recommendations(self, score: int) -> List[str]:
        """Gera recomendações de SEO."""
        recommendations = []

        if score < 85:
            recommendations.append(
                "Implementar dados estruturados (Schema.org)")
        if score < 75:
            recommendations.append(
                "Otimizar meta descriptions para todas as páginas")
        if score < 65:
            recommendations.append("Criar e submeter sitemap XML atualizado")

        return recommendations

    def _generate_best_practices_recommendations(self, score: int) -> List[str]:
        """Gera recomendações de melhores práticas."""
        recommendations = []

        if score < 85:
            recommendations.append(
                "Implementar HTTP/3 para melhor performance")
        if score < 75:
            recommendations.append("Configurar cache headers apropriados")
        if score < 65:
            recommendations.append(
                "Habilitar compressão Brotli para todos os recursos")

        return recommendations

    def _generate_top_recommendations(self, perf: int, sec: int, a11y: int, seo: int, bp: int) -> List[Dict[str, Any]]:
        """Gera as principais recomendações priorizadas."""
        recommendations = []

        # Encontrar a área mais fraca
        scores = {
            "performance": perf,
            "security": sec,
            "accessibility": a11y,
            "seo": seo,
            "best_practices": bp
        }

        # Ordenar áreas por score (mais fraco primeiro)
        sorted_areas = sorted(scores.items(), key=lambda x: x[1])

        # Adicionar recomendação para a área mais fraca
        weakest_area, weakest_score = sorted_areas[0]

        if weakest_area == "performance" and weakest_score < 80:
            recommendations.append({
                "category": "performance",
                "priority": "critical",
                "title": "Otimizar Core Web Vitals",
                "description": "Melhorar métricas LCP, FID e CLS para melhor experiência do usuário",
                "estimated_impact": f"+{80 - weakest_score} pontos no score de performance",
                "effort": "medium"
            })
        elif weakest_area == "security" and weakest_score < 90:
            recommendations.append({
                "category": "security",
                "priority": "high",
                "title": "Fortalecer headers de segurança",
                "description": "Implementar CSP, HSTS e outros headers de proteção",
                "estimated_impact": "Redução significativa de vulnerabilidades",
                "effort": "low"
            })
        elif weakest_area == "accessibility" and weakest_score < 75:
            recommendations.append({
                "category": "accessibility",
                "priority": "high",
                "title": "Melhorar acessibilidade WCAG 2.1",
                "description": "Corrigir contrastes, adicionar ARIA labels e melhorar navegação por teclado",
                "estimated_impact": f"+{75 - weakest_score} pontos no score de acessibilidade",
                "effort": "medium"
            })
        elif weakest_area == "seo" and weakest_score < 85:
            recommendations.append({
                "category": "seo",
                "priority": "high",
                "title": "Otimizar SEO técnico",
                "description": "Implementar structured data, otimizar meta tags e melhorar crawlability",
                "estimated_impact": f"+{85 - weakest_score} pontos no score de SEO",
                "effort": "low"
            })
        elif weakest_area == "best_practices":
            recommendations.append({
                "category": "best_practices",
                "priority": "medium",
                "title": "Implementar melhores práticas",
                "description": "Modernizar stack técnico com HTTP/3, Brotli e otimizações avançadas",
                "estimated_impact": f"+{90 - weakest_score} pontos no score de práticas",
                "effort": "medium"
            })

            # Adicionar recomendações para outras áreas fracas
        for i in range(1, len(sorted_areas)):
            if len(recommendations) >= 2:
                break

            area, score = sorted_areas[i]

            if area == "performance" and score < 90:
                recommendations.append({
                    "category": "performance",
                    "priority": "high",
                    "title": "Melhorar performance geral",
                    "description": "Otimizar recursos, reduzir JavaScript e melhorar cache",
                    "estimated_impact": "+10-15 pontos no score de performance",
                    "effort": "medium"
                })
            elif area == "security" and score < 95:
                recommendations.append({
                    "category": "security",
                    "priority": "medium",
                    "title": "Aprimorar segurança",
                    "description": "Adicionar headers avançados e monitoramento de vulnerabilidades",
                    "estimated_impact": "Proteção adicional contra ataques",
                    "effort": "low"
                })
            elif area == "accessibility" and score < 85:
                recommendations.append({
                    "category": "accessibility",
                    "priority": "medium",
                    "title": "Aprimorar acessibilidade",
                    "description": "Melhorar experiência para usuários com deficiência",
                    "estimated_impact": f"+{85 - score} pontos no score de acessibilidade",
                    "effort": "medium"
                })
            elif area == "seo" and score < 90:
                recommendations.append({
                    "category": "seo",
                    "priority": "medium",
                    "title": "Aprimorar SEO",
                    "description": "Melhorar visibilidade nos motores de busca",
                    "estimated_impact": f"+{90 - score} pontos no score de SEO",
                    "effort": "low"
                })
            elif area == "best_practices" and score < 95:
                recommendations.append({
                    "category": "best_practices",
                    "priority": "low",
                    "title": "Refinar práticas de desenvolvimento",
                    "description": "Adotar padrões modernos e otimizações avançadas",
                    "estimated_impact": f"+{95 - score} pontos no score de práticas",
                    "effort": "low"
                })

        # Sempre incluir uma recomendação de quick win
        recommendations.append({
            "category": "quick-win",
            "priority": "medium",
            "title": "Otimizações rápidas de impacto",
            "description": "Comprimir imagens, minificar recursos e habilitar cache browser",
            "estimated_impact": "+5-10 pontos no score geral",
            "effort": "low"
        })

        return recommendations[:3]  # Retornar top 3 recomendações

    def _calculate_confidence_score(self, data: Dict[str, Any]) -> float:
        """
        Calcula score de confiança baseado na completude dos dados.

        Returns:
            Score entre 0.0 e 1.0
        """
        # Para mock data, retornar sempre alta confiança
        # TODO: Implementar cálculo real quando integrar com Perplexity
        return 0.95

    def _build_system_prompt(self) -> str:
        """Constrói o prompt do sistema para análise técnica via Perplexity."""
        return (
            "Você é um especialista em análise técnica de websites e performance web. "
            "Analise o site fornecido e retorne um diagnóstico técnico completo em JSON incluindo: "
            "1) Performance e Core Web Vitals (LCP, FID, CLS) "
            "2) Segurança (HTTPS, headers, vulnerabilidades) "
            "3) Acessibilidade (WCAG 2.1 compliance) "
            "4) SEO técnico (meta tags, structured data) "
            "5) Melhores práticas de desenvolvimento. "
            "Seja específico com números, scores e recomendações acionáveis."
        )

    def _build_user_prompt(self, company_name: str, company_url: str) -> str:
        """Constrói o prompt do usuário para análise técnica."""
        return (
            f"Realize uma análise técnica completa do website {company_url} da empresa {company_name}. "
            "Avalie performance (Core Web Vitals), segurança, acessibilidade, SEO técnico e melhores práticas. "
            "Retorne scores de 0-100 para cada categoria e recomendações específicas de melhorias."
        )
