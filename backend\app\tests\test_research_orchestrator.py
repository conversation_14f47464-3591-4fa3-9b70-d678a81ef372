"""
Testes unitários para o ResearchOrchestrator.
"""
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from decimal import Decimal
from datetime import datetime

from app.services import ResearchOrchestrator, ServiceRegistry
from app.core import (
    IResearchService,
    ResearchRequest,
    ResearchResult,
    ResearchServiceNotFoundError,
    ResearchValidationError
)


class MockResearchService(IResearchService):
    """Mock de serviço de pesquisa para testes."""

    def __init__(self, name: str, cost: Decimal, success: bool = True):
        self.name = name
        self.cost = cost
        self.success = success
        self.execute_called = False

    def get_name(self) -> str:
        return self.name

    def get_version(self) -> str:
        return "1.0.0"

    def get_description(self) -> str:
        return f"Mock service {self.name}"

    def get_cost(self) -> Decimal:
        return self.cost

    def get_required_fields(self) -> list:
        return ["company_name"]

    def get_optional_fields(self) -> list:
        return ["city"]

    async def validate_request(self, request: ResearchRequest) -> bool:
        return bool(request.company_name)

    async def execute(self, request: ResearchRequest) -> ResearchResult:
        self.execute_called = True
        await asyncio.sleep(0.1)  # Simula processamento

        return ResearchResult(
            service_name=self.name,
            service_version="1.0.0",
            timestamp=datetime.utcnow(),
            cost=self.cost,
            success=self.success,
            data={"result": f"Data from {self.name}"} if self.success else None,
            error=None if self.success else f"Error in {self.name}",
            processing_time_seconds=0.1,
            confidence_score=0.9 if self.success else None
        )

    def get_sample_output(self) -> dict:
        return {"sample": "output"}


class TestServiceRegistry:
    """Testes para ServiceRegistry."""

    @pytest.fixture
    def registry(self):
        return ServiceRegistry()

    def test_register_service(self, registry):
        """Testa registro de serviço."""
        service = MockResearchService("test_service", Decimal("0.01"))
        registry.register(service)

        assert "test_service" in registry._services
        assert registry._services["test_service"] == service

    def test_get_service(self, registry):
        """Testa obtenção de serviço."""
        service = MockResearchService("test_service", Decimal("0.01"))
        registry.register(service)

        retrieved = registry.get("test_service")
        assert retrieved == service

    def test_get_nonexistent_service(self, registry):
        """Testa obtenção de serviço inexistente."""
        with pytest.raises(ResearchServiceNotFoundError):
            registry.get("nonexistent")

    def test_list_services(self, registry):
        """Testa listagem de serviços."""
        service1 = MockResearchService("service1", Decimal("0.01"))
        service2 = MockResearchService("service2", Decimal("0.02"))

        registry.register(service1)
        registry.register(service2)

        services = registry.list_services()
        assert len(services) == 2

        # Verifica estrutura
        for service_info in services:
            assert "name" in service_info
            assert "version" in service_info
            assert "description" in service_info
            assert "cost" in service_info
            assert "required_fields" in service_info
            assert "optional_fields" in service_info

    def test_register_duplicate_overwrites(self, registry, caplog):
        """Testa que registrar duplicata sobrescreve com warning."""
        service1 = MockResearchService("test", Decimal("0.01"))
        service2 = MockResearchService("test", Decimal("0.02"))

        registry.register(service1)
        registry.register(service2)

        # Verifica que foi sobrescrito
        assert registry.get("test") == service2

        # Verifica warning no log
        assert "já registrado" in caplog.text


class TestResearchOrchestrator:
    """Testes para ResearchOrchestrator."""

    @pytest.fixture
    def registry(self):
        reg = ServiceRegistry()
        # Registrar alguns serviços mock
        reg.register(MockResearchService("service1", Decimal("0.01")))
        reg.register(MockResearchService("service2", Decimal("0.02")))
        reg.register(MockResearchService(
            "service_fail", Decimal("0.03"), success=False))
        return reg

    @pytest.fixture
    def orchestrator(self, registry):
        return ResearchOrchestrator(registry, default_timeout=5)

    @pytest.fixture
    def valid_request(self):
        return ResearchRequest(
            client_id="test123",
            company_name="Test Company",
            company_url="https://test.com"
        )

    @pytest.mark.asyncio
    async def test_execute_single_service(self, orchestrator, valid_request):
        """Testa execução de um único serviço."""
        result = await orchestrator.execute_services(
            valid_request,
            ["service1"]
        )

        # Verifica estrutura do resultado
        assert "summary" in result
        assert "services" in result
        assert "errors" in result

        # Verifica summary
        summary = result["summary"]
        assert summary["total_services"] == 1
        assert summary["successful"] == 1
        assert summary["failed"] == 0
        assert summary["total_cost"] == 0.01

        # Verifica dados do serviço
        assert "service1" in result["services"]
        service_data = result["services"]["service1"]
        assert service_data["success"] is True
        assert service_data["data"]["result"] == "Data from service1"

    @pytest.mark.asyncio
    async def test_execute_multiple_services_parallel(self, orchestrator, valid_request):
        """Testa execução paralela de múltiplos serviços."""
        result = await orchestrator.execute_services(
            valid_request,
            ["service1", "service2"],
            parallel=True
        )

        # Verifica resultados
        assert result["summary"]["total_services"] == 2
        assert result["summary"]["successful"] == 2
        assert result["summary"]["total_cost"] == 0.03  # 0.01 + 0.02

        assert "service1" in result["services"]
        assert "service2" in result["services"]

    @pytest.mark.asyncio
    async def test_execute_multiple_services_sequential(self, orchestrator, valid_request):
        """Testa execução sequencial de múltiplos serviços."""
        result = await orchestrator.execute_services(
            valid_request,
            ["service1", "service2"],
            parallel=False
        )

        # Verifica resultados
        assert result["summary"]["total_services"] == 2
        assert result["summary"]["successful"] == 2

        # Tempo sequencial deve ser maior que paralelo
        assert result["summary"]["total_processing_time"] >= 0.2  # 2x 0.1s

    @pytest.mark.asyncio
    async def test_execute_with_failing_service(self, orchestrator, valid_request):
        """Testa execução com serviço que falha."""
        result = await orchestrator.execute_services(
            valid_request,
            ["service1", "service_fail"]
        )

        # Verifica contagens
        assert result["summary"]["successful"] == 1
        assert result["summary"]["failed"] == 1
        # Só cobra sucessos
        assert result["summary"]["successful_cost"] == 0.01

        # Verifica erro
        assert len(result["errors"]) == 1
        assert result["errors"][0]["service"] == "service_fail"
        assert "Error in service_fail" in result["errors"][0]["error"]

    @pytest.mark.asyncio
    async def test_execute_nonexistent_service(self, orchestrator, valid_request):
        """Testa execução com serviço inexistente."""
        with pytest.raises(ResearchServiceNotFoundError):
            await orchestrator.execute_services(
                valid_request,
                ["nonexistent_service"]
            )

    @pytest.mark.asyncio
    async def test_validation_failure(self, orchestrator):
        """Testa falha de validação."""
        invalid_request = ResearchRequest(
            client_id="test",
            company_name="",  # Nome vazio vai falhar validação
            company_url="https://test.com"
        )

        with pytest.raises(ResearchValidationError) as exc_info:
            await orchestrator.execute_services(
                invalid_request,
                ["service1"]
            )

        assert "validação falhou" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_timeout_handling(self, orchestrator, valid_request):
        """Testa tratamento de timeout."""
        # Criar serviço que demora muito
        slow_service = Mock(spec=IResearchService)
        slow_service.get_name.return_value = "slow_service"
        slow_service.get_version.return_value = "1.0.0"
        slow_service.validate_request = AsyncMock(return_value=True)

        # Execute vai demorar mais que o timeout
        async def slow_execute(request):
            await asyncio.sleep(10)  # Mais que o timeout de 5s

        slow_service.execute = slow_execute

        # Registrar serviço lento
        orchestrator.registry.register(slow_service)

        # Executar com timeout baixo
        result = await orchestrator.execute_services(
            valid_request,
            ["slow_service"],
            timeout_per_service=1  # 1 segundo de timeout
        )

        # Verifica que falhou por timeout
        assert result["summary"]["failed"] == 1
        assert len(result["errors"]) == 1
        assert "Timeout" in result["errors"][0]["error"]

    @pytest.mark.asyncio
    async def test_estimate_cost(self, orchestrator):
        """Testa estimativa de custo."""
        cost = await orchestrator.estimate_cost(["service1", "service2"])
        assert cost == Decimal("0.03")  # 0.01 + 0.02

    def test_get_available_services(self, orchestrator):
        """Testa listagem de serviços disponíveis."""
        services = orchestrator.get_available_services()

        assert len(services) == 3  # service1, service2, service_fail
        service_names = [s["name"] for s in services]
        assert "service1" in service_names
        assert "service2" in service_names
        assert "service_fail" in service_names

    @pytest.mark.asyncio
    async def test_consolidate_results(self, orchestrator):
        """Testa consolidação de resultados."""
        # Criar resultados mock
        results = [
            ResearchResult(
                service_name="service1",
                service_version="1.0.0",
                timestamp=datetime.utcnow(),
                cost=Decimal("0.01"),
                success=True,
                data={"test": "data1"},
                processing_time_seconds=0.5,
                confidence_score=0.9
            ),
            ResearchResult(
                service_name="service2",
                service_version="1.0.0",
                timestamp=datetime.utcnow(),
                cost=Decimal("0.02"),
                success=False,
                error="Test error",
                processing_time_seconds=0.3
            )
        ]

        # Consolidar
        consolidated = orchestrator._consolidate_results(results, 1.0)

        # Verificar summary
        assert consolidated["summary"]["total_services"] == 2
        assert consolidated["summary"]["successful"] == 1
        assert consolidated["summary"]["failed"] == 1
        assert consolidated["summary"]["total_cost"] == 0.03
        assert consolidated["summary"]["successful_cost"] == 0.01
        assert consolidated["summary"]["total_processing_time"] == 1.0

        # Verificar serviços
        assert consolidated["services"]["service1"]["success"] is True
        assert consolidated["services"]["service1"]["confidence_score"] == 0.9
        assert consolidated["services"]["service2"]["success"] is False

        # Verificar erros
        assert len(consolidated["errors"]) == 1
        assert consolidated["errors"][0]["service"] == "service2"


class TestOrchestratorSingleton:
    """Testa o padrão singleton do orchestrator."""

    @patch('app.services.research_orchestrator._auto_register_services')
    def test_get_orchestrator_singleton(self, mock_auto_register):
        """Testa que get_orchestrator retorna sempre a mesma instância."""
        from app.services.research_orchestrator import get_orchestrator, _orchestrator

        # Resetar singleton para teste
        import app.services.research_orchestrator
        app.services.research_orchestrator._orchestrator = None

        # Primeira chamada cria instância
        orch1 = get_orchestrator()
        assert orch1 is not None
        mock_auto_register.assert_called_once()

        # Segunda chamada retorna mesma instância
        orch2 = get_orchestrator()
        assert orch2 is orch1

        # Auto-register só é chamado uma vez
        assert mock_auto_register.call_count == 1
