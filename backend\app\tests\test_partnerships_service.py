"""
Unit tests for Partnership Analysis Service
"""

import pytest
from datetime import datetime, UTC
from unittest.mock import Mock, AsyncMock, patch
import json

from app.services.research.partnerships_service import (
    PartnershipService,
    PartnershipAnalysis
)
from app.core.exceptions import ResearchException


class TestPartnershipService:
    """Test suite for PartnershipService"""

    @pytest.fixture
    def service(self):
        """Create service instance"""
        return PartnershipService()

    @pytest.fixture
    def mock_provider(self):
        """Create mock provider"""
        provider = Mock()
        provider.search = AsyncMock()
        return provider

    @pytest.fixture
    def service_with_provider(self, mock_provider):
        """Create service with mock provider"""
        return PartnershipService(provider=mock_provider)

    @pytest.fixture
    def sample_data(self):
        """Sample data for testing"""
        return {
            # Commercial partnerships
            "distributors": [
                {
                    "name": "Tech Distributor LATAM",
                    "regions": ["Brazil", "Argentina", "Chile"],
                    "since": "2021",
                    "exclusive": False,
                    "performance": "excellent",
                    "revenue_contribution": "20-25%"
                }
            ],
            "resellers": [
                {
                    "name": "Cloud Solutions Partner",
                    "type": "VAR",
                    "tier": "Gold",
                    "certifications": ["Sales Expert", "Tech Pro"],
                    "annual_sales": "$3M"
                }
            ],
            "suppliers": [
                {
                    "name": "AWS",
                    "type": "infrastructure",
                    "critical": True,
                    "dependency_level": "high",
                    "contract_type": "enterprise",
                    "alternatives": ["Azure", "GCP"]
                }
            ],
            "partner_revenue_impact": "25-30%",
            "partner_growth_rate": "15-20%",
            "partner_churn_rate": "5-10%",
            "has_partner_portal": True,
            "has_certification": True,

            # Technology partnerships
            "integrations": [
                {
                    "partner": "Salesforce",
                    "type": "CRM Integration",
                    "api_level": "deep",
                    "users": 5000,
                    "data_sync": "bidirectional",
                    "launched": "2020"
                },
                {
                    "partner": "Slack",
                    "type": "Communication",
                    "api_level": "standard",
                    "users": 3000
                }
            ],
            "certifications": [
                "AWS Advanced Partner",
                "Microsoft Gold Partner",
                "ISO 27001"
            ],
            "marketplace_presence": [
                "AWS Marketplace",
                "Azure Marketplace"
            ],
            "public_apis": 5,
            "partner_apis": 10,
            "developer_count": 200,
            "has_dev_portal": True,

            # Strategic partnerships
            "investors": [
                {
                    "name": "Sequoia Capital",
                    "type": "VC",
                    "round": "Series B",
                    "strategic_value": "high",
                    "board_seat": True,
                    "amount": "$30M",
                    "year": "2023"
                }
            ],
            "joint_ventures": [
                {
                    "partner": "Enterprise Corp",
                    "focus": "Enterprise Solutions",
                    "started": "2022",
                    "regions": ["North America"]
                }
            ],
            "co_marketing": ["Microsoft", "Google"],
            "media_partners": ["TechCrunch", "Forbes"],
            "associations": ["Cloud Alliance", "SaaS Forum"],
            "university_partners": ["MIT", "Stanford"]
        }

    def test_init(self, service):
        """Test service initialization"""
        assert service.cost_per_analysis == 0.005
        assert service.service_name == "partnerships"
        assert service.provider is None

    def test_init_with_provider(self, mock_provider):
        """Test initialization with provider"""
        service = PartnershipService(provider=mock_provider)
        assert service.provider == mock_provider

    @pytest.mark.asyncio
    async def test_analyze_with_mock_data(self, service):
        """Test analyze with mock data"""
        result = await service.analyze("Test Company")

        assert isinstance(result, PartnershipAnalysis)
        assert result.service_name == "partnerships"
        assert result.cost == 0.005
        assert result.partnership_ecosystem_score >= 0
        assert len(result.recommendations) >= 0
        assert result.confidence_score >= 0

    @pytest.mark.asyncio
    async def test_analyze_with_provider(self, service_with_provider, mock_provider, sample_data):
        """Test analyze with provider"""
        mock_provider.search.return_value = sample_data

        result = await service_with_provider.analyze(
            "Test Company",
            {"industry": "SaaS", "website": "https://test.com"}
        )

        assert isinstance(result, PartnershipAnalysis)
        mock_provider.search.assert_called_once()

        # Verify commercial partnerships
        assert len(result.commercial_partnerships["distributors"]) == 1
        assert len(result.commercial_partnerships["resellers"]) == 1
        assert result.commercial_partnerships["revenue_impact"] == "25-30%"

        # Verify technology partnerships
        assert len(result.technology_partnerships["integrations"]) == 2
        assert len(result.technology_partnerships["certifications"]) == 3

        # Verify strategic partnerships
        assert len(result.strategic_partnerships["investors"]) == 1
        assert len(result.strategic_partnerships["joint_ventures"]) == 1

    @pytest.mark.asyncio
    async def test_analyze_error_handling(self, service_with_provider, mock_provider):
        """Test error handling"""
        mock_provider.search.side_effect = Exception("API Error")

        with pytest.raises(ResearchException) as exc_info:
            await service_with_provider.analyze("Test Company")

        assert "Failed to analyze partnerships" in str(exc_info.value)

    def test_build_analysis_query(self, service):
        """Test query building"""
        query = service._build_analysis_query(
            "Test Company",
            {"industry": "SaaS", "website": "https://test.com"}
        )

        assert "Test Company" in query
        assert "SaaS industry" in query
        assert "https://test.com" in query
        assert "Commercial Partnerships" in query
        assert "Technology Partnerships" in query
        assert "Strategic Partnerships" in query

    def test_analyze_commercial_partnerships(self, service, sample_data):
        """Test commercial partnership extraction"""
        commercial = service._analyze_commercial_partnerships(
            sample_data, None)

        assert len(commercial["distributors"]) == 1
        assert commercial["distributors"][0]["name"] == "Tech Distributor LATAM"
        assert len(commercial["resellers"]) == 1
        assert len(commercial["suppliers"]) == 1
        assert commercial["revenue_impact"] == "25-30%"
        assert commercial["partner_network_size"] == 3
        assert "Brazil" in commercial["geographic_coverage"]
        assert commercial["channel_strategy"] == "hybrid"

    def test_analyze_technology_partnerships(self, service, sample_data):
        """Test technology partnership analysis"""
        tech = service._analyze_technology_partnerships(sample_data, None)

        assert len(tech["integrations"]) == 2
        assert tech["integrations"][0]["partner"] == "Salesforce"
        assert len(tech["certifications"]) == 3
        assert len(tech["marketplace_presence"]) == 2
        assert tech["api_ecosystem"]["public_apis"] == 5
        # developer_community not in sample data
        assert tech["developer_ecosystem"]["community_size"] == 0
        assert tech["integration_maturity"] == "developing"

    def test_analyze_strategic_partnerships(self, service, sample_data):
        """Test strategic partnership analysis"""
        strategic = service._analyze_strategic_partnerships(sample_data, None)

        assert len(strategic["investors"]) == 1
        assert strategic["investors"][0]["name"] == "Sequoia Capital"
        assert len(strategic["joint_ventures"]) == 1
        assert len(strategic["co_marketing"]) == 2
        assert len(strategic["media_partners"]) == 2
        assert strategic["partnership_stage"] == "early"

    def test_calculate_partnership_ecosystem_score(self, service, sample_data):
        """Test ecosystem score calculation"""
        commercial = service._analyze_commercial_partnerships(
            sample_data, None)
        technology = service._analyze_technology_partnerships(
            sample_data, None)
        strategic = service._analyze_strategic_partnerships(sample_data, None)

        score = service._calculate_partnership_ecosystem_score(
            commercial, technology, strategic
        )

        assert 0 <= score <= 100
        assert score > 30  # With decent sample data

    def test_score_commercial_partnerships(self, service):
        """Test commercial partnership scoring"""
        # Test with good commercial data
        good_commercial = {
            "partner_network_size": 25,
            "geographic_coverage": ["Brazil", "Mexico", "USA", "Canada", "UK"],
            "revenue_impact": "30-35%",
            "channel_strategy": "multi-tier",
            "partner_programs": {
                "has_partner_portal": True,
                "certification_program": True,
                "support_level": "premium"
            }
        }
        score = service._score_commercial_partnerships(good_commercial)
        assert score > 70

        # Test with poor commercial data
        poor_commercial = {
            "partner_network_size": 2,
            "geographic_coverage": ["Brazil"],
            "revenue_impact": "5-10%",
            "channel_strategy": "direct-sales"
        }
        score = service._score_commercial_partnerships(poor_commercial)
        assert score < 30

    def test_score_technology_partnerships(self, service):
        """Test technology partnership scoring"""
        # Test with advanced tech partnerships
        advanced_tech = {
            "integrations": [{"partner": f"Partner{i}"} for i in range(25)],
            "integration_maturity": "advanced",
            "api_ecosystem": {"public_apis": 20, "partner_apis": 15, "developers": 1500},
            "certifications": ["Cert1", "Cert2", "Cert3", "Cert4", "Cert5"],
            "marketplace_presence": ["AWS", "Azure", "Google", "Salesforce", "HubSpot"],
            "developer_ecosystem": {"developer_portal": True, "community_size": 500, "hackathons": 3}
        }
        score = service._score_technology_partnerships(advanced_tech)
        assert score > 80

    def test_generate_recommendations(self, service):
        """Test recommendations generation"""
        # Test with gaps in partnerships
        commercial = {"partner_network_size": 5, "geographic_coverage": [
            "Brazil"], "channel_strategy": "direct-sales"}
        technology = {"integrations": [], "certifications": [],
                      "api_ecosystem": {"developers": 10, "public_apis": 2}}
        strategic = {"investors": [], "joint_ventures": [],
                     "partnership_stage": "nascent"}

        recommendations = service._generate_recommendations(
            commercial, technology, strategic
        )

        assert len(recommendations) > 0
        assert len(recommendations) <= 5
        assert any("parceiros comerciais" in r for r in recommendations)
        assert any("integrações" in r for r in recommendations)

    def test_calculate_confidence_score(self, service, sample_data):
        """Test confidence score calculation"""
        commercial = service._analyze_commercial_partnerships(
            sample_data, None)
        technology = service._analyze_technology_partnerships(
            sample_data, None)
        strategic = service._analyze_strategic_partnerships(sample_data, None)

        confidence = service._calculate_confidence_score(
            commercial, technology, strategic
        )

        assert 0 <= confidence <= 100
        assert confidence > 50  # With reasonable sample data

    def test_to_dict_conversion(self, service, sample_data):
        """Test PartnershipAnalysis to_dict conversion"""
        commercial = service._analyze_commercial_partnerships(
            sample_data, None)
        technology = service._analyze_technology_partnerships(
            sample_data, None)
        strategic = service._analyze_strategic_partnerships(sample_data, None)

        analysis = PartnershipAnalysis(
            commercial_partnerships=commercial,
            technology_partnerships=technology,
            strategic_partnerships=strategic,
            partnership_ecosystem_score=72.5,
            recommendations=["Test recommendation"],
            confidence_score=85.0,
            raw_data=sample_data
        )

        result_dict = analysis.to_dict()

        assert result_dict["service_name"] == "partnerships"
        assert result_dict["cost"] == 0.005
        assert result_dict["partnership_ecosystem_score"] == 72.5
        assert result_dict["confidence_score"] == 85.0
        assert len(result_dict["recommendations"]) == 1
        assert "timestamp" in result_dict

    def test_channel_strategy_determination(self, service):
        """Test channel strategy logic"""
        # Multi-tier
        assert service._determine_channel_strategy(
            [{}] * 6, [{}] * 11
        ) == "multi-tier"

        # Distributor-focused
        assert service._determine_channel_strategy(
            [{}] * 3, []
        ) == "distributor-focused"

        # Direct-reseller
        assert service._determine_channel_strategy(
            [], [{}] * 5
        ) == "direct-reseller"

        # Direct-sales
        assert service._determine_channel_strategy(
            [], []
        ) == "direct-sales"

    def test_mock_data_variations(self, service):
        """Test different mock data scenarios"""
        # Startup mock data
        startup_data = service._get_mock_data("Tech Startup Inc")
        assert len(startup_data["distributors"]) == 0
        assert len(startup_data["investors"]) >= 1

        # Enterprise mock data
        enterprise_data = service._get_mock_data("Enterprise Global Corp")
        assert len(enterprise_data["distributors"]) >= 1
        assert len(enterprise_data["integrations"]) >= 3

        # Default mock data
        default_data = service._get_mock_data("Regular Company")
        assert len(default_data["distributors"]) >= 1
        assert len(default_data["integrations"]) >= 1

    @pytest.mark.asyncio
    async def test_integration_flow(self, service):
        """Test complete integration flow"""
        # Run full analysis
        result = await service.analyze("Integration Test Company")

        # Verify all components are present
        assert hasattr(result, 'commercial_partnerships')
        assert hasattr(result, 'technology_partnerships')
        assert hasattr(result, 'strategic_partnerships')
        assert hasattr(result, 'partnership_ecosystem_score')
        assert hasattr(result, 'recommendations')
        assert hasattr(result, 'confidence_score')

        # Verify relationships between components
        dict_result = result.to_dict()
        assert isinstance(dict_result, dict)
        assert all(key in dict_result for key in [
            "commercial_partnerships", "technology_partnerships",
            "strategic_partnerships", "partnership_ecosystem_score"
        ])

    def test_empty_data_handling(self, service):
        """Test handling of empty/minimal data"""
        empty_data = {}

        commercial = service._analyze_commercial_partnerships(empty_data, None)
        assert commercial["partner_network_size"] == 0
        assert commercial["revenue_impact"] == "20-30%"  # Default

        technology = service._analyze_technology_partnerships(empty_data, None)
        assert len(technology["integrations"]) == 0
        assert technology["integration_maturity"] == "basic"

        strategic = service._analyze_strategic_partnerships(empty_data, None)
        assert strategic["partnership_stage"] == "nascent"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
