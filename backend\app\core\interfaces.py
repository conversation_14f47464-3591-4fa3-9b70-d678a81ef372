"""
Interfaces base para o sistema de pesquisa modular.
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from datetime import datetime
from decimal import Decimal
from pydantic import BaseModel


class ResearchRequest(BaseModel):
    """Modelo base para requisições de pesquisa."""
    client_id: str
    company_url: str
    company_name: str
    additional_context: Optional[Dict[str, Any]] = None


class ResearchResult(BaseModel):
    """Modelo base para resultados de pesquisa."""
    service_name: str
    service_version: str
    timestamp: datetime
    cost: Decimal
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    processing_time_seconds: float
    confidence_score: Optional[float] = None  # 0.0 a 1.0


class IResearchService(ABC):
    """
    Interface base para todos os serviços de pesquisa.

    Cada serviço deve implementar esta interface para garantir
    compatibilidade com o ResearchOrchestrator.
    """

    @abstractmethod
    def get_name(self) -> str:
        """Retorna o nome único do serviço."""
        pass

    @abstractmethod
    def get_version(self) -> str:
        """Retorna a versão do serviço."""
        pass

    @abstractmethod
    def get_description(self) -> str:
        """Retorna uma descrição do que o serviço faz."""
        pass

    @abstractmethod
    def get_cost(self) -> Decimal:
        """Retorna o custo estimado em USD para executar este serviço."""
        pass

    @abstractmethod
    def get_required_fields(self) -> List[str]:
        """Retorna lista de campos obrigatórios no ResearchRequest."""
        pass

    @abstractmethod
    def get_optional_fields(self) -> List[str]:
        """Retorna lista de campos opcionais que o serviço pode usar."""
        pass

    @abstractmethod
    async def validate_request(self, request: ResearchRequest) -> bool:
        """
        Valida se a requisição tem todos os dados necessários.

        Args:
            request: Requisição de pesquisa

        Returns:
            True se válida, False caso contrário
        """
        pass

    @abstractmethod
    async def execute(self, request: ResearchRequest) -> ResearchResult:
        """
        Executa a pesquisa e retorna o resultado.

        Args:
            request: Requisição de pesquisa com dados do cliente

        Returns:
            ResearchResult com os dados coletados ou erro
        """
        pass

    @abstractmethod
    def get_sample_output(self) -> Dict[str, Any]:
        """
        Retorna um exemplo do formato de saída esperado.

        Útil para documentação e testes.
        """
        pass


class IResearchProvider(ABC):
    """
    Interface para provedores externos de pesquisa (Perplexity, SearchSociety, etc).
    """

    @abstractmethod
    def get_provider_name(self) -> str:
        """Nome do provedor (ex: 'perplexity', 'duckduckgo')."""
        pass

    @abstractmethod
    async def search(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Executa uma busca no provedor.

        Args:
            query: Query de busca
            **kwargs: Parâmetros específicos do provedor

        Returns:
            Resultados da busca em formato dict
        """
        pass

    @abstractmethod
    def get_rate_limits(self) -> Dict[str, int]:
        """
        Retorna os rate limits do provedor.

        Returns:
            Dict com 'requests_per_minute', 'requests_per_day', etc
        """
        pass
