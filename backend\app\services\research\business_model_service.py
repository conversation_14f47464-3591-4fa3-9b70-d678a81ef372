"""
Business Model Service - Análise detalhada do modelo de negócio

Este serviço analisa o modelo de negócio da empresa utilizando o framework
Business Model Canvas e avalia maturidade, sustentabilidade e oportunidades.

Custo: $0.006 por execução
"""
import json
import logging
from typing import Dict, Any, List, Optional
from decimal import Decimal
from datetime import datetime, timezone

from app.core import (
    IResearchService,
    ResearchRequest,
    ResearchResult,
    Timer
)

logger = logging.getLogger(__name__)


class BusinessModelService(IResearchService):
    """
    Serviço especializado em análise de modelo de negócio.

    Analisa e avalia:
    - Business Model Canvas (9 blocos)
    - Modelo de distribuição (on-premise, cloud, híbrido)
    - Modelo de licenciamento (proprietário, open source)
    - Audiência-alvo (B2B, B2C, B2B2C)
    - Modelos de receita e monetização
    - Sustentabilidade e escalabilidade
    """

    def __init__(self, provider):
        """
        Args:
            provider: Instância do provider (Perplexity, SearchSociety, etc)
        """
        self.provider = provider
        self._version = "1.0.0"
        self._cost = Decimal("0.006")

    def get_name(self) -> str:
        return "business_model"

    def get_version(self) -> str:
        return self._version

    def get_description(self) -> str:
        return "Análise completa do modelo de negócio incluindo Business Model Canvas, distribuição, receita e sustentabilidade"

    def get_cost(self) -> Decimal:
        return self._cost

    def get_required_fields(self) -> List[str]:
        return ["company_name", "company_url"]

    def get_optional_fields(self) -> List[str]:
        return ["industry", "company_size", "target_market", "business_stage"]

    async def validate_request(self, request: ResearchRequest) -> bool:
        """Valida se a requisição tem os dados necessários."""
        if not request.company_name or not request.company_url:
            logger.error("Nome da empresa e URL são obrigatórios")
            return False

        # Validar formato da URL
        url = request.company_url.lower()
        if not url.startswith(('http://', 'https://')):
            logger.error(f"URL inválida: {request.company_url}")
            return False

        return True

    async def execute(self, request: ResearchRequest) -> ResearchResult:
        """Executa a análise de modelo de negócio."""
        with Timer() as timer:
            try:
                empresa = request.company_name
                site = request.company_url

                # AIDEV-NOTE: Extrair campos opcionais com fallbacks inteligentes
                industria = getattr(request, 'industry', None)
                if industria is None and request.additional_context:
                    industria = request.additional_context.get(
                        'industry', 'não especificada')
                if industria is None:
                    industria = 'não especificada'

                company_size = getattr(
                    request, 'company_size', 'não especificada')
                if request.additional_context:
                    company_size = request.additional_context.get(
                        'company_size', company_size)

                target_market = getattr(request, 'target_market', 'B2B/B2C')
                if request.additional_context:
                    target_market = request.additional_context.get(
                        'target_market', target_market)

                business_stage = getattr(request, 'business_stage', 'growth')
                if request.additional_context:
                    business_stage = request.additional_context.get(
                        'business_stage', business_stage)

                # Mock mode para desenvolvimento
                if hasattr(self.provider, '_mock_mode') and self.provider._mock_mode:
                    logger.info(
                        f"[MOCK] Retornando análise de modelo de negócio mock para {empresa}")
                    processed_data = self._get_mock_data(
                        empresa, industria, target_market)
                else:
                    # AIDEV-NOTE: Construir prompts especializados para análise de modelo de negócio
                    system_prompt = self._build_system_prompt()
                    user_prompt = self._build_user_prompt(
                        empresa, site, industria, target_market, business_stage)

                    logger.info(
                        f"Executando análise de modelo de negócio para {empresa}")

                    # Fazer requisição ao provider
                    raw_result = await self.provider.search(
                        system_prompt=system_prompt,
                        user_prompt=user_prompt,
                        temperature=0.7,
                        max_tokens=2500
                    )

                    # Processar e estruturar resultado
                    processed_data = self._process_result(raw_result, empresa)

                # Calcular scores
                confidence_score = self._calculate_confidence_score(
                    processed_data)
                business_model_maturity_score = self._calculate_business_model_maturity_score(
                    processed_data)

                # Adicionar metadados
                processed_data['metadata'] = {
                    'analysis_date': datetime.now(timezone.utc).isoformat(),
                    'company_url': site,
                    'industry': industria,
                    'company_size': company_size,
                    'target_market': target_market,
                    'business_stage': business_stage,
                    'confidence_score': confidence_score,
                    'business_model_maturity_score': business_model_maturity_score
                }

                # Gerar recomendações
                processed_data['recommendations'] = self._generate_recommendations(
                    processed_data, industria, business_stage
                )

                return ResearchResult(
                    service_name=self.get_name(),
                    service_version=self.get_version(),
                    timestamp=datetime.now(timezone.utc),
                    cost=self._cost,
                    success=True,
                    data=processed_data,
                    processing_time_seconds=timer.elapsed,
                    confidence_score=confidence_score
                )

            except json.JSONDecodeError as e:
                logger.error(f"Erro ao processar JSON: {str(e)}")
                return self._create_error_result(
                    "Erro ao processar resposta do modelo",
                    timer.elapsed
                )
            except Exception as e:
                logger.error(
                    f"Erro na análise de modelo de negócio: {str(e)}", exc_info=True)
                return self._create_error_result(str(e), timer.elapsed)

    def get_sample_output(self) -> Dict[str, Any]:
        """Retorna exemplo de saída do serviço."""
        return {
            "business_model_canvas": {
                "customer_segments": {
                    "primary": ["Pequenas e médias empresas", "Startups"],
                    "secondary": ["Grandes empresas"],
                    "characteristics": ["Tech-savvy", "Buscam eficiência"],
                    "size_estimate": "10K-50K clientes potenciais"
                },
                "value_propositions": {
                    "main_value": "Automatização de processos com IA",
                    "key_benefits": [
                        "Redução de 70% no tempo de análise",
                        "Insights acionáveis em tempo real",
                        "ROI em 6 meses"
                    ],
                    "differentiators": ["IA proprietária", "Interface intuitiva"]
                },
                "channels": {
                    "distribution": ["Online direto", "Partners", "Inside sales"],
                    "marketing": ["Content marketing", "SEO/SEM", "Events"],
                    "support": ["Self-service", "Chat", "Email"]
                },
                "customer_relationships": {
                    "type": "Automated with personal assistance",
                    "acquisition": "Inbound marketing + Sales",
                    "retention": "Customer success + Product-led growth",
                    "growth": "Upsell/Cross-sell + Referrals"
                },
                "revenue_streams": {
                    "primary": ["SaaS subscription", "Professional services"],
                    "secondary": ["Training", "API access"],
                    "pricing_model": "Tiered subscription + Usage-based"
                },
                "key_resources": {
                    "human": ["Desenvolvedores", "Data scientists", "Sales team"],
                    "technological": ["Plataforma cloud", "Modelos de IA", "APIs"],
                    "intellectual": ["Algoritmos proprietários", "Dados", "Brand"]
                },
                "key_activities": {
                    "core": ["Desenvolvimento de produto", "Machine learning", "Sales"],
                    "support": ["Customer success", "Marketing", "Operations"]
                },
                "key_partnerships": {
                    "strategic": ["Cloud providers", "Tech integrators"],
                    "suppliers": ["Data providers", "Infrastructure"],
                    "channels": ["Resellers", "Consultancies"]
                },
                "cost_structure": {
                    "fixed_costs": ["Salários", "Infraestrutura", "Escritório"],
                    "variable_costs": ["Cloud computing", "Marketing", "Sales commissions"],
                    "major_investments": ["R&D", "Product development", "Market expansion"]
                }
            },
            "distribution_model": {
                "type": "cloud",
                "deployment": ["SaaS multi-tenant", "API-first"],
                "advantages": ["Escalabilidade", "Updates automáticos", "Acessibilidade"],
                "challenges": ["Dependência de internet", "Segurança de dados"]
            },
            "licensing_model": {
                "type": "proprietary",
                "approach": "Subscription-based licensing",
                "terms": ["Annual/Monthly", "Per user/seat", "Usage tiers"]
            },
            "target_audience": {
                "model": "B2B",
                "segments": ["SMB", "Mid-market", "Enterprise"],
                "geographic_focus": "Brasil e América Latina",
                "expansion_potential": "Global"
            },
            "revenue_model_analysis": {
                "current_models": ["Subscription", "Professional services"],
                "revenue_mix": {
                    "recurring": "75%",
                    "one_time": "15%",
                    "services": "10%"
                },
                "growth_drivers": ["New customer acquisition", "Expansion revenue", "Retention"],
                "unit_economics": {
                    "cac": "$1,500",
                    "ltv": "$18,000",
                    "ltv_cac_ratio": 12.0,
                    "payback_period": "8 months"
                }
            },
            "sustainability_analysis": {
                "financial_sustainability": "high",
                "competitive_moat": ["Technology", "Network effects", "Switching costs"],
                "scalability": "high",
                "risks": ["Market competition", "Technology changes", "Regulatory"]
            },
            "metadata": {
                "analysis_date": "2025-01-30T10:00:00Z",
                "company_url": "https://example.com",
                "industry": "SaaS",
                "company_size": "50-200",
                "target_market": "B2B",
                "business_stage": "growth",
                "confidence_score": 0.88,
                "business_model_maturity_score": 0.82
            },
            "recommendations": [
                {
                    "area": "revenue_diversification",
                    "suggestion": "Introduzir marketplace de integrações para receita adicional",
                    "impact": "high",
                    "effort": "medium",
                    "timeline": "6-12 months"
                },
                {
                    "area": "customer_expansion",
                    "suggestion": "Desenvolver offering específico para enterprise",
                    "impact": "high",
                    "effort": "high",
                    "timeline": "12-18 months"
                }
            ]
        }

    def _build_system_prompt(self) -> str:
        """Constrói o prompt do sistema para análise de modelo de negócio."""
        return """Você é um especialista em estratégia de negócios e análise de modelos de negócio com experiência em:
- Business Model Canvas e suas 9 dimensões
- Modelos de distribuição de software (on-premise, cloud, híbrido)
- Estratégias de licenciamento e monetização
- Análise de sustentabilidade e escalabilidade
- Unit economics e métricas de negócio
- Estratégias de go-to-market

Sua tarefa é analisar profundamente o modelo de negócio de uma empresa, considerando:
1. Todos os 9 blocos do Business Model Canvas
2. Modelo de distribuição e suas implicações
3. Estratégia de monetização e revenue streams
4. Sustentabilidade e potencial de crescimento
5. Riscos e oportunidades

Você deve fornecer uma análise objetiva e baseada em dados públicos disponíveis."""

    def _build_user_prompt(self, empresa: str, site: str, industria: str,
                           target_market: str, business_stage: str) -> str:
        """Constrói o prompt do usuário com dados específicos."""
        return f"""Analise o modelo de negócio da empresa {empresa} ({site}).
Indústria: {industria}
Mercado-alvo: {target_market}
Estágio do negócio: {business_stage}

Forneça uma análise completa incluindo:

1. **Business Model Canvas** (9 blocos):
   - Customer Segments: Quem são os clientes, características, tamanho do mercado
   - Value Propositions: Proposta de valor principal, benefícios, diferenciadores
   - Channels: Canais de distribuição, marketing e suporte
   - Customer Relationships: Tipo de relacionamento, aquisição, retenção
   - Revenue Streams: Fontes de receita, modelo de pricing
   - Key Resources: Recursos humanos, tecnológicos, intelectuais
   - Key Activities: Atividades principais e de suporte
   - Key Partnerships: Parcerias estratégicas, fornecedores
   - Cost Structure: Custos fixos, variáveis, investimentos

2. **Modelo de Distribuição**:
   - Tipo (on-premise, cloud/SaaS, híbrido, marketplace)
   - Deployment e arquitetura
   - Vantagens e desafios

3. **Modelo de Licenciamento**:
   - Tipo (proprietário, open source, freemium)
   - Abordagem e termos

4. **Audiência-Alvo**:
   - Modelo (B2B, B2C, B2B2C)
   - Segmentos específicos
   - Foco geográfico

5. **Análise do Modelo de Receita**:
   - Modelos atuais
   - Mix de receita (recorrente vs one-time)
   - Growth drivers
   - Unit economics (CAC, LTV, payback)

6. **Análise de Sustentabilidade**:
   - Sustentabilidade financeira
   - Competitive moat
   - Escalabilidade
   - Principais riscos

IMPORTANTE:
- Baseie-se em informações públicas disponíveis
- Use estimativas realistas quando dados exatos não estiverem disponíveis
- Indique o nível de certeza nas análises
- Use "Não disponível" quando não houver informação

Formato de resposta JSON:
{{
    "business_model_canvas": {{
        "customer_segments": {{
            "primary": ["lista de segmentos primários"],
            "secondary": ["lista de segmentos secundários"],
            "characteristics": ["características principais"],
            "size_estimate": "estimativa do tamanho do mercado"
        }},
        "value_propositions": {{
            "main_value": "proposta de valor principal",
            "key_benefits": ["lista de benefícios"],
            "differentiators": ["lista de diferenciadores"]
        }},
        "channels": {{
            "distribution": ["canais de distribuição"],
            "marketing": ["canais de marketing"],
            "support": ["canais de suporte"]
        }},
        "customer_relationships": {{
            "type": "tipo de relacionamento",
            "acquisition": "estratégia de aquisição",
            "retention": "estratégia de retenção",
            "growth": "estratégia de crescimento"
        }},
        "revenue_streams": {{
            "primary": ["fontes primárias"],
            "secondary": ["fontes secundárias"],
            "pricing_model": "modelo de pricing"
        }},
        "key_resources": {{
            "human": ["recursos humanos"],
            "technological": ["recursos tecnológicos"],
            "intellectual": ["propriedade intelectual"]
        }},
        "key_activities": {{
            "core": ["atividades core"],
            "support": ["atividades de suporte"]
        }},
        "key_partnerships": {{
            "strategic": ["parceiros estratégicos"],
            "suppliers": ["fornecedores"],
            "channels": ["parceiros de canal"]
        }},
        "cost_structure": {{
            "fixed_costs": ["custos fixos"],
            "variable_costs": ["custos variáveis"],
            "major_investments": ["investimentos principais"]
        }}
    }},
    "distribution_model": {{
        "type": "tipo do modelo",
        "deployment": ["detalhes de deployment"],
        "advantages": ["vantagens"],
        "challenges": ["desafios"]
    }},
    "licensing_model": {{
        "type": "tipo de licenciamento",
        "approach": "abordagem",
        "terms": ["termos principais"]
    }},
    "target_audience": {{
        "model": "B2B/B2C/B2B2C",
        "segments": ["segmentos"],
        "geographic_focus": "foco geográfico",
        "expansion_potential": "potencial de expansão"
    }},
    "revenue_model_analysis": {{
        "current_models": ["modelos atuais"],
        "revenue_mix": {{
            "recurring": "percentual",
            "one_time": "percentual",
            "services": "percentual"
        }},
        "growth_drivers": ["drivers de crescimento"],
        "unit_economics": {{
            "cac": "custo de aquisição",
            "ltv": "lifetime value",
            "ltv_cac_ratio": ratio_numerico,
            "payback_period": "período"
        }}
    }},
    "sustainability_analysis": {{
        "financial_sustainability": "low/medium/high",
        "competitive_moat": ["lista de moats"],
        "scalability": "low/medium/high",
        "risks": ["principais riscos"]
    }}
}}"""

    def _process_result(self, raw_data: Any, empresa: str) -> Dict[str, Any]:
        """Processa e valida o resultado da análise."""
        # Parse JSON se necessário
        if isinstance(raw_data, str):
            try:
                data = json.loads(raw_data)
            except json.JSONDecodeError:
                logger.warning(f"Resposta não é JSON válido para {empresa}")
                return self._get_empty_business_model_analysis()
        else:
            data = raw_data

        # Validar e normalizar estrutura
        result = self._get_empty_business_model_analysis()

        # AIDEV-NOTE: Processar cada seção garantindo estrutura consistente
        sections = [
            'business_model_canvas', 'distribution_model', 'licensing_model',
            'target_audience', 'revenue_model_analysis', 'sustainability_analysis'
        ]

        for section in sections:
            if section in data and isinstance(data[section], dict):
                result[section] = self._normalize_section(
                    data[section], section)
            else:
                logger.warning(f"Seção {section} não encontrada ou inválida")

        return result

    def _normalize_section(self, section_data: Dict, section_name: str) -> Dict[str, Any]:
        """Normaliza dados de uma seção específica."""
        if section_name == 'business_model_canvas':
            return self._normalize_canvas(section_data)
        elif section_name == 'distribution_model':
            return {
                'type': section_data.get('type', 'unknown'),
                'deployment': section_data.get('deployment', []),
                'advantages': section_data.get('advantages', []),
                'challenges': section_data.get('challenges', [])
            }
        elif section_name == 'licensing_model':
            return {
                'type': section_data.get('type', 'unknown'),
                'approach': section_data.get('approach', ''),
                'terms': section_data.get('terms', [])
            }
        elif section_name == 'target_audience':
            return {
                'model': section_data.get('model', 'unknown'),
                'segments': section_data.get('segments', []),
                'geographic_focus': section_data.get('geographic_focus', ''),
                'expansion_potential': section_data.get('expansion_potential', '')
            }
        elif section_name == 'revenue_model_analysis':
            return self._normalize_revenue_analysis(section_data)
        elif section_name == 'sustainability_analysis':
            return {
                'financial_sustainability': section_data.get('financial_sustainability', 'unknown'),
                'competitive_moat': section_data.get('competitive_moat', []),
                'scalability': section_data.get('scalability', 'unknown'),
                'risks': section_data.get('risks', [])
            }

        return section_data

    def _normalize_canvas(self, canvas_data: Dict) -> Dict[str, Any]:
        """Normaliza dados do Business Model Canvas."""
        normalized = {}

        # Mapear cada bloco do canvas
        canvas_blocks = [
            'customer_segments', 'value_propositions', 'channels',
            'customer_relationships', 'revenue_streams', 'key_resources',
            'key_activities', 'key_partnerships', 'cost_structure'
        ]

        for block in canvas_blocks:
            if block in canvas_data:
                normalized[block] = canvas_data[block]
            else:
                # Fornecer estrutura padrão vazia
                normalized[block] = self._get_default_canvas_block(block)

        return normalized

    def _get_default_canvas_block(self, block_name: str) -> Dict[str, Any]:
        """Retorna estrutura padrão para um bloco do canvas."""
        defaults = {
            'customer_segments': {
                'primary': [], 'secondary': [],
                'characteristics': [], 'size_estimate': 'Não disponível'
            },
            'value_propositions': {
                'main_value': '', 'key_benefits': [], 'differentiators': []
            },
            'channels': {
                'distribution': [], 'marketing': [], 'support': []
            },
            'customer_relationships': {
                'type': '', 'acquisition': '', 'retention': '', 'growth': ''
            },
            'revenue_streams': {
                'primary': [], 'secondary': [], 'pricing_model': ''
            },
            'key_resources': {
                'human': [], 'technological': [], 'intellectual': []
            },
            'key_activities': {
                'core': [], 'support': []
            },
            'key_partnerships': {
                'strategic': [], 'suppliers': [], 'channels': []
            },
            'cost_structure': {
                'fixed_costs': [], 'variable_costs': [], 'major_investments': []
            }
        }

        return defaults.get(block_name, {})

    def _normalize_revenue_analysis(self, revenue_data: Dict) -> Dict[str, Any]:
        """Normaliza dados de análise de receita."""
        normalized = {
            'current_models': revenue_data.get('current_models', []),
            'revenue_mix': revenue_data.get('revenue_mix', {
                'recurring': '0%', 'one_time': '0%', 'services': '0%'
            }),
            'growth_drivers': revenue_data.get('growth_drivers', []),
            'unit_economics': {
                'cac': revenue_data.get('unit_economics', {}).get('cac', 'N/A'),
                'ltv': revenue_data.get('unit_economics', {}).get('ltv', 'N/A'),
                'ltv_cac_ratio': revenue_data.get('unit_economics', {}).get('ltv_cac_ratio', 0),
                'payback_period': revenue_data.get('unit_economics', {}).get('payback_period', 'N/A')
            }
        }

        return normalized

    def _calculate_confidence_score(self, data: Dict[str, Any]) -> float:
        """Calcula score de confiança baseado na completude dos dados."""
        total_checks = 0
        passed_checks = 0

        # Verificar Business Model Canvas (peso maior)
        if 'business_model_canvas' in data:
            canvas = data['business_model_canvas']
            for block in canvas:
                total_checks += 1
                if self._is_canvas_block_complete(canvas[block]):
                    passed_checks += 1

        # Verificar outras seções
        other_sections = [
            'distribution_model', 'licensing_model', 'target_audience',
            'revenue_model_analysis', 'sustainability_analysis'
        ]

        for section in other_sections:
            if section in data:
                total_checks += 1
                if self._is_section_complete(data[section], section):
                    passed_checks += 1

        return round(passed_checks / total_checks if total_checks > 0 else 0.0, 2)

    def _is_canvas_block_complete(self, block_data: Dict) -> bool:
        """Verifica se um bloco do canvas está completo."""
        if not isinstance(block_data, dict):
            return False

        # Verificar se há pelo menos alguns dados significativos
        for key, value in block_data.items():
            if isinstance(value, list) and len(value) > 0:
                return True
            elif isinstance(value, str) and value and value != 'Não disponível':
                return True

        return False

    def _is_section_complete(self, section_data: Dict, section_name: str) -> bool:
        """Verifica se uma seção está completa."""
        if section_name == 'distribution_model':
            return section_data.get('type', 'unknown') != 'unknown'
        elif section_name == 'licensing_model':
            return section_data.get('type', 'unknown') != 'unknown'
        elif section_name == 'target_audience':
            return section_data.get('model', 'unknown') != 'unknown'
        elif section_name == 'revenue_model_analysis':
            return len(section_data.get('current_models', [])) > 0
        elif section_name == 'sustainability_analysis':
            return section_data.get('financial_sustainability', 'unknown') != 'unknown'

        return False

    def _calculate_business_model_maturity_score(self, data: Dict[str, Any]) -> float:
        """
        Calcula score de maturidade do modelo de negócio.

        Considera:
        - Completude do Business Model Canvas (30%)
        - Diversificação de revenue streams (20%)
        - Clareza na proposta de valor (20%)
        - Sustentabilidade e escalabilidade (20%)
        - Unit economics saudáveis (10%)
        """
        score = 0.0

        # Completude do Canvas (30%)
        if 'business_model_canvas' in data:
            canvas_completeness = self._calculate_canvas_completeness(
                data['business_model_canvas']
            )
            score += canvas_completeness * 0.30

        # Diversificação de revenue streams (20%)
        if 'revenue_streams' in data.get('business_model_canvas', {}):
            revenue_streams = data['business_model_canvas']['revenue_streams']
            total_streams = len(revenue_streams.get('primary', [])) + \
                len(revenue_streams.get('secondary', []))
            if total_streams >= 3:
                score += 0.20
            elif total_streams == 2:
                score += 0.15
            elif total_streams == 1:
                score += 0.10

        # Clareza na proposta de valor (20%)
        if 'value_propositions' in data.get('business_model_canvas', {}):
            value_props = data['business_model_canvas']['value_propositions']
            if value_props.get('main_value') and \
               len(value_props.get('key_benefits', [])) >= 2 and \
               len(value_props.get('differentiators', [])) >= 1:
                score += 0.20
            elif value_props.get('main_value'):
                score += 0.10

        # Sustentabilidade e escalabilidade (20%)
        if 'sustainability_analysis' in data:
            sustainability = data['sustainability_analysis']

            # Financial sustainability
            if sustainability.get('financial_sustainability') == 'high':
                score += 0.10
            elif sustainability.get('financial_sustainability') == 'medium':
                score += 0.05

            # Scalability
            if sustainability.get('scalability') == 'high':
                score += 0.10
            elif sustainability.get('scalability') == 'medium':
                score += 0.05

        # Unit economics (10%)
        if 'unit_economics' in data.get('revenue_model_analysis', {}):
            unit_econ = data['revenue_model_analysis']['unit_economics']
            ltv_cac_ratio = unit_econ.get('ltv_cac_ratio', 0)

            if ltv_cac_ratio >= 3:
                score += 0.10
            elif ltv_cac_ratio >= 2:
                score += 0.07
            elif ltv_cac_ratio >= 1:
                score += 0.04

        return round(score, 2)

    def _calculate_canvas_completeness(self, canvas: Dict) -> float:
        """Calcula o nível de completude do Business Model Canvas."""
        total_blocks = 9
        complete_blocks = 0

        for block_name, block_data in canvas.items():
            if self._is_canvas_block_complete(block_data):
                complete_blocks += 1

        return complete_blocks / total_blocks

    def _generate_recommendations(self, data: Dict[str, Any],
                                  industria: str, business_stage: str) -> List[Dict[str, Any]]:
        """Gera recomendações baseadas na análise."""
        recommendations = []

        # AIDEV-NOTE: Análise de gaps no modelo de negócio
        canvas = data.get('business_model_canvas', {})

        # Verificar diversificação de receita
        revenue_streams = canvas.get('revenue_streams', {})
        total_streams = len(revenue_streams.get('primary', [])) + \
            len(revenue_streams.get('secondary', []))

        if total_streams < 2:
            recommendations.append({
                'area': 'revenue_diversification',
                'suggestion': 'Considere adicionar fontes de receita complementares como marketplace, API monetizada ou serviços profissionais',
                'impact': 'high',
                'effort': 'medium',
                'timeline': '6-12 months'
            })

        # Verificar modelo de distribuição
        dist_model = data.get('distribution_model', {})
        if dist_model.get('type') == 'on-premise' and business_stage in ['growth', 'scale']:
            recommendations.append({
                'area': 'distribution_evolution',
                'suggestion': 'Migre para modelo SaaS ou híbrido para aumentar escalabilidade e receita recorrente',
                'impact': 'high',
                'effort': 'high',
                'timeline': '12-18 months'
            })

        # Verificar expansão de mercado
        target_audience = data.get('target_audience', {})
        if target_audience.get('model') == 'B2B' and len(target_audience.get('segments', [])) < 3:
            recommendations.append({
                'area': 'market_expansion',
                'suggestion': 'Desenvolva offerings específicos para novos segmentos de mercado (enterprise, governo, educação)',
                'impact': 'high',
                'effort': 'medium',
                'timeline': '6-9 months'
            })

        # Verificar unit economics
        unit_econ = data.get('revenue_model_analysis', {}
                             ).get('unit_economics', {})
        ltv_cac_ratio = unit_econ.get('ltv_cac_ratio', 0)

        if 0 < ltv_cac_ratio < 3:
            recommendations.append({
                'area': 'unit_economics_optimization',
                'suggestion': 'Otimize CAC através de marketing de conteúdo e product-led growth, ou aumente LTV com upsell/cross-sell',
                'impact': 'high',
                'effort': 'medium',
                'timeline': '3-6 months'
            })

        # Verificar partnerships
        partnerships = canvas.get('key_partnerships', {})
        if len(partnerships.get('strategic', [])) < 2:
            recommendations.append({
                'area': 'strategic_partnerships',
                'suggestion': 'Estabeleça parcerias estratégicas com integradores, consultoras ou plataformas complementares',
                'impact': 'medium',
                'effort': 'low',
                'timeline': '3-6 months'
            })

        # Se não houver recomendações específicas, adicionar genéricas
        if not recommendations:
            recommendations.append({
                'area': 'continuous_improvement',
                'suggestion': 'Continue monitorando métricas de negócio e ajustando o modelo conforme feedback do mercado',
                'impact': 'medium',
                'effort': 'low',
                'timeline': 'ongoing'
            })

        # Ordenar por impacto (high > medium > low)
        impact_order = {'high': 0, 'medium': 1, 'low': 2}
        recommendations.sort(key=lambda x: impact_order.get(x['impact'], 3))

        return recommendations[:5]  # Retornar top 5 recomendações

    def _get_empty_business_model_analysis(self) -> Dict[str, Any]:
        """Retorna estrutura vazia para análise de modelo de negócio."""
        return {
            'business_model_canvas': {
                'customer_segments': self._get_default_canvas_block('customer_segments'),
                'value_propositions': self._get_default_canvas_block('value_propositions'),
                'channels': self._get_default_canvas_block('channels'),
                'customer_relationships': self._get_default_canvas_block('customer_relationships'),
                'revenue_streams': self._get_default_canvas_block('revenue_streams'),
                'key_resources': self._get_default_canvas_block('key_resources'),
                'key_activities': self._get_default_canvas_block('key_activities'),
                'key_partnerships': self._get_default_canvas_block('key_partnerships'),
                'cost_structure': self._get_default_canvas_block('cost_structure')
            },
            'distribution_model': {
                'type': 'unknown',
                'deployment': [],
                'advantages': [],
                'challenges': []
            },
            'licensing_model': {
                'type': 'unknown',
                'approach': '',
                'terms': []
            },
            'target_audience': {
                'model': 'unknown',
                'segments': [],
                'geographic_focus': '',
                'expansion_potential': ''
            },
            'revenue_model_analysis': {
                'current_models': [],
                'revenue_mix': {
                    'recurring': '0%',
                    'one_time': '0%',
                    'services': '0%'
                },
                'growth_drivers': [],
                'unit_economics': {
                    'cac': 'N/A',
                    'ltv': 'N/A',
                    'ltv_cac_ratio': 0,
                    'payback_period': 'N/A'
                }
            },
            'sustainability_analysis': {
                'financial_sustainability': 'unknown',
                'competitive_moat': [],
                'scalability': 'unknown',
                'risks': []
            }
        }

    def _get_mock_data(self, empresa: str, industria: str, target_market: str) -> Dict[str, Any]:
        """Retorna dados mock para desenvolvimento."""
        # AIDEV-NOTE: Mock data rico para diferentes cenários
        mock_scenarios = {
            'default': self._get_saas_b2b_mock(),
            'marketplace': self._get_marketplace_mock(),
            'enterprise': self._get_enterprise_mock()
        }

        # Selecionar cenário baseado no tipo de empresa
        if 'marketplace' in empresa.lower() or 'market' in industria.lower():
            return mock_scenarios['marketplace']
        elif 'enterprise' in empresa.lower() or target_market == 'B2B Enterprise':
            return mock_scenarios['enterprise']
        else:
            return mock_scenarios['default']

    def _get_saas_b2b_mock(self) -> Dict[str, Any]:
        """Mock para empresa SaaS B2B típica."""
        return {
            'business_model_canvas': {
                'customer_segments': {
                    'primary': ['Pequenas e médias empresas (10-500 funcionários)', 'Startups em crescimento'],
                    'secondary': ['Empresas enterprise (500+ funcionários)', 'Agências digitais'],
                    'characteristics': [
                        'Empresas que buscam transformação digital',
                        'Equipes técnicas e não-técnicas',
                        'Orçamento entre R$500-R$5000/mês para software'
                    ],
                    'size_estimate': '15.000 empresas no Brasil, 150.000 na América Latina'
                },
                'value_propositions': {
                    'main_value': 'Plataforma all-in-one de gestão empresarial com IA que reduz complexidade operacional em 70%',
                    'key_benefits': [
                        'Economia de 30h/mês em tarefas administrativas',
                        'Redução de 50% em erros operacionais',
                        'ROI positivo em 3 meses',
                        'Integração com 100+ ferramentas'
                    ],
                    'differentiators': [
                        'IA proprietária para automação inteligente',
                        'Interface no-code para customização',
                        'Suporte em português 24/7',
                        'Compliance com LGPD'
                    ]
                },
                'channels': {
                    'distribution': [
                        'Venda direta online (self-service)',
                        'Inside sales para mid-market',
                        'Partners e revendedores'
                    ],
                    'marketing': [
                        'Content marketing e SEO',
                        'Webinars e eventos online',
                        'Referral program',
                        'Social selling no LinkedIn'
                    ],
                    'support': [
                        'Central de ajuda self-service',
                        'Chat com IA + humano',
                        'Customer Success dedicado (planos Pro+)',
                        'Comunidade de usuários'
                    ]
                },
                'customer_relationships': {
                    'type': 'Automated with personal assistance para contas estratégicas',
                    'acquisition': 'Product-led growth com trial gratuito + Inside sales',
                    'retention': 'Customer Success proativo + Product stickiness',
                    'growth': 'Expansion revenue através de upsell de features e seats'
                },
                'revenue_streams': {
                    'primary': [
                        'Assinatura SaaS mensal/anual',
                        'Upsell de módulos adicionais',
                        'Cobrança por usuário adicional'
                    ],
                    'secondary': [
                        'Serviços de implementação',
                        'Treinamento e certificação',
                        'API e integrações customizadas'
                    ],
                    'pricing_model': 'Tiered subscription (Starter $49, Pro $199, Enterprise custom) + Usage-based para API'
                },
                'key_resources': {
                    'human': [
                        'Time de engenharia (15 devs)',
                        'Customer Success (8 pessoas)',
                        'Sales & Marketing (10 pessoas)',
                        'Product & Design (5 pessoas)'
                    ],
                    'technological': [
                        'Plataforma cloud AWS',
                        'Modelos de ML proprietários',
                        'APIs e integrações',
                        'Infraestrutura de dados'
                    ],
                    'intellectual': [
                        'Algoritmos de otimização patenteados',
                        'Base de conhecimento',
                        'Brand e reputação',
                        'Dados de uso para ML'
                    ]
                },
                'key_activities': {
                    'core': [
                        'Desenvolvimento de produto',
                        'Manutenção e evolução da plataforma',
                        'Vendas e aquisição de clientes',
                        'Customer Success e retenção'
                    ],
                    'support': [
                        'Marketing e geração de demanda',
                        'Suporte técnico',
                        'Gestão de infraestrutura',
                        'Compliance e segurança'
                    ]
                },
                'key_partnerships': {
                    'strategic': [
                        'AWS (infraestrutura cloud)',
                        'Integradores e consultoras',
                        'Associações de classe'
                    ],
                    'suppliers': [
                        'Provedores de dados',
                        'Ferramentas de desenvolvimento',
                        'Serviços de segurança'
                    ],
                    'channels': [
                        'Agências de implementação',
                        'Marketplaces (AWS, Azure)',
                        'Parceiros de co-marketing'
                    ]
                },
                'cost_structure': {
                    'fixed_costs': [
                        'Folha de pagamento (65% dos custos)',
                        'Escritório e infraestrutura',
                        'Ferramentas e licenças'
                    ],
                    'variable_costs': [
                        'Infraestrutura cloud (AWS)',
                        'Comissões de vendas',
                        'Marketing e aquisição',
                        'Suporte terceirizado'
                    ],
                    'major_investments': [
                        'P&D de novas features',
                        'Expansão internacional',
                        'Aquisição de talentos'
                    ]
                }
            },
            'distribution_model': {
                'type': 'cloud',
                'deployment': [
                    'SaaS multi-tenant na AWS',
                    'API-first architecture',
                    'Mobile-responsive web app'
                ],
                'advantages': [
                    'Escalabilidade infinita',
                    'Updates automáticos sem downtime',
                    'Acessível de qualquer lugar',
                    'Baixo custo de manutenção'
                ],
                'challenges': [
                    'Dependência de conectividade',
                    'Preocupações com segurança de dados',
                    'Latência para usuários distantes'
                ]
            },
            'licensing_model': {
                'type': 'proprietary',
                'approach': 'Subscription-based com tiers',
                'terms': [
                    'Licença por usuário/seat',
                    'Contratos anuais com desconto',
                    'Fair use policy para API',
                    'SLA 99.9% uptime'
                ]
            },
            'target_audience': {
                'model': 'B2B',
                'segments': [
                    'SMB (10-100 funcionários)',
                    'Mid-market (100-500)',
                    'Enterprise (500+)'
                ],
                'geographic_focus': 'Brasil com expansão para América Latina',
                'expansion_potential': 'Alto - mercado LATAM de $5B crescendo 25% aa'
            },
            'revenue_model_analysis': {
                'current_models': [
                    'Subscription (SaaS)',
                    'Usage-based (API)',
                    'Professional services'
                ],
                'revenue_mix': {
                    'recurring': '85%',
                    'one_time': '10%',
                    'services': '5%'
                },
                'growth_drivers': [
                    'New logo acquisition (40% do crescimento)',
                    'Expansion revenue - upsell/cross-sell (35%)',
                    'Redução de churn (25%)'
                ],
                'unit_economics': {
                    'cac': 'R$ 2.500',
                    'ltv': 'R$ 35.000',
                    'ltv_cac_ratio': 14.0,
                    'payback_period': '7 meses'
                }
            },
            'sustainability_analysis': {
                'financial_sustainability': 'high',
                'competitive_moat': [
                    'Network effects - quanto mais clientes, melhor a IA',
                    'High switching costs - integrações e dados',
                    'Brand e reputação no mercado',
                    'Economia de escala na infraestrutura'
                ],
                'scalability': 'high',
                'risks': [
                    'Competição de big techs entrando no mercado',
                    'Mudanças regulatórias (LGPD, IA)',
                    'Dependência de poucos clientes grandes',
                    'Dificuldade de internacionalização'
                ]
            }
        }

    def _get_marketplace_mock(self) -> Dict[str, Any]:
        """Mock para empresa marketplace."""
        return {
            'business_model_canvas': {
                'customer_segments': {
                    'primary': ['Vendedores/Prestadores de serviço', 'Compradores/Consumidores'],
                    'secondary': ['Empresas parceiras', 'Anunciantes'],
                    'characteristics': [
                        'Dois lados do mercado com necessidades distintas',
                        'Vendedores buscam alcance e ferramentas',
                        'Compradores buscam variedade e conveniência'
                    ],
                    'size_estimate': '500K vendedores ativos, 10M compradores mensais'
                },
                'value_propositions': {
                    'main_value': 'Conectar oferta e demanda com confiança, conveniência e melhores preços',
                    'key_benefits': [
                        'Para vendedores: Acesso a milhões de clientes',
                        'Para compradores: Variedade e preços competitivos',
                        'Pagamento seguro e proteção ao consumidor',
                        'Logística integrada'
                    ],
                    'differentiators': [
                        'Maior variedade de produtos/serviços',
                        'Sistema de reputação confiável',
                        'Fulfillment próprio',
                        'Programa de fidelidade'
                    ]
                },
                'channels': {
                    'distribution': ['App mobile', 'Website', 'API para grandes vendedores'],
                    'marketing': ['Performance marketing', 'TV e mídia offline', 'Influencers'],
                    'support': ['FAQ e chatbot', 'Central de resolução', 'Suporte vendedor']
                },
                'customer_relationships': {
                    'type': 'Automated platform com suporte quando necessário',
                    'acquisition': 'SEO/SEM para compradores, Outbound para vendedores',
                    'retention': 'Programa de fidelidade, Qualidade do matching',
                    'growth': 'Network effects, Referral programs'
                },
                'revenue_streams': {
                    'primary': ['Comissão sobre transações (5-15%)', 'Assinatura para vendedores Pro'],
                    'secondary': ['Anúncios e destaque', 'Serviços financeiros', 'Logística'],
                    'pricing_model': 'Transaction-based + Subscription + Advertising'
                },
                'key_resources': {
                    'human': ['Tech team', 'Operations', 'Marketing', 'Trust & Safety'],
                    'technological': ['Plataforma marketplace', 'Sistema de pagamentos', 'Algoritmos de matching'],
                    'intellectual': ['Brand', 'Dados de transações', 'Algoritmos de recomendação']
                },
                'key_activities': {
                    'core': ['Desenvolvimento da plataforma', 'Aquisição dois lados', 'Trust & Safety'],
                    'support': ['Marketing', 'Atendimento', 'Compliance']
                },
                'key_partnerships': {
                    'strategic': ['Meios de pagamento', 'Empresas de logística', 'Grandes vendedores'],
                    'suppliers': ['Infraestrutura cloud', 'Antifraude'],
                    'channels': ['Afiliados', 'Comparadores de preço']
                },
                'cost_structure': {
                    'fixed_costs': ['Tecnologia e produto', 'Overhead corporativo'],
                    'variable_costs': ['Marketing (CAC)', 'Processamento pagamentos', 'Atendimento'],
                    'major_investments': ['Expansão categorias', 'Fulfillment centers', 'Internacional']
                }
            },
            'distribution_model': {
                'type': 'cloud',
                'deployment': ['Multi-sided platform', 'Mobile-first', 'API ecosystem'],
                'advantages': ['Network effects', 'Escalabilidade', 'Asset-light'],
                'challenges': ['Chicken-egg problem', 'Qualidade e fraude', 'Regulação']
            },
            'licensing_model': {
                'type': 'proprietary',
                'approach': 'Platform as a Service',
                'terms': ['Termos de uso para ambos lados', 'Políticas de listagem', 'Revenue sharing']
            },
            'target_audience': {
                'model': 'B2B2C',
                'segments': ['Vendedores profissionais', 'Vendedores casuais', 'Consumidores finais'],
                'geographic_focus': 'Nacional com potencial regional',
                'expansion_potential': 'Alto - replicável em outros mercados'
            },
            'revenue_model_analysis': {
                'current_models': ['Transaction fees', 'Subscription', 'Advertising', 'Value-added services'],
                'revenue_mix': {
                    'recurring': '20%',
                    'one_time': '70%',
                    'services': '10%'
                },
                'growth_drivers': ['GMV growth', 'Take rate optimization', 'New verticals'],
                'unit_economics': {
                    'cac': 'R$ 25 (comprador), R$ 500 (vendedor)',
                    'ltv': 'R$ 300 (comprador), R$ 15.000 (vendedor)',
                    'ltv_cac_ratio': 12.0,
                    'payback_period': '2 meses'
                }
            },
            'sustainability_analysis': {
                'financial_sustainability': 'high',
                'competitive_moat': ['Network effects fortes', 'Brand recognition', 'Dados e ML'],
                'scalability': 'high',
                'risks': ['Desintermediação', 'Regulação', 'Competição global']
            }
        }

    def _get_enterprise_mock(self) -> Dict[str, Any]:
        """Mock para empresa enterprise/tradicional."""
        return {
            'business_model_canvas': {
                'customer_segments': {
                    'primary': ['Grandes corporações (1000+ funcionários)', 'Governo e setor público'],
                    'secondary': ['Multinacionais', 'Instituições financeiras'],
                    'characteristics': [
                        'Processos complexos de compra',
                        'Necessidade de customização',
                        'Compliance e segurança críticos',
                        'Orçamentos de TI significativos'
                    ],
                    'size_estimate': '2.000 empresas enterprise no Brasil'
                },
                'value_propositions': {
                    'main_value': 'Solução enterprise completa com segurança, compliance e customização total',
                    'key_benefits': [
                        'Redução de 40% em custos operacionais',
                        'Compliance com todas regulações',
                        'Integração com sistemas legados',
                        'SLA garantido e suporte dedicado'
                    ],
                    'differentiators': [
                        '30 anos de experiência no mercado',
                        'Certificações ISO, SOC2, LGPD',
                        'Deployment on-premise ou private cloud',
                        'Customização ilimitada'
                    ]
                },
                'channels': {
                    'distribution': ['Venda direta enterprise', 'Partners certificados', 'Licitações'],
                    'marketing': ['Account-based marketing', 'Eventos executivos', 'Gartner/IDC'],
                    'support': ['Suporte dedicado 24/7', 'Professional services', 'TAM']
                },
                'customer_relationships': {
                    'type': 'Dedicado e personalizado com executive sponsors',
                    'acquisition': 'Enterprise sales com ciclo de 6-18 meses',
                    'retention': 'Strategic account management e QBRs',
                    'growth': 'Land and expand dentro da conta'
                },
                'revenue_streams': {
                    'primary': ['Licenças perpétuas', 'Manutenção anual (20% do license fee)'],
                    'secondary': ['Professional services', 'Treinamento', 'Customização'],
                    'pricing_model': 'Perpetual license + Maintenance + Services'
                },
                'key_resources': {
                    'human': ['Consultores especializados', 'Arquitetos de solução', 'Engenheiros sênior'],
                    'technological': ['Plataforma enterprise', 'Data centers próprios', 'Ferramentas proprietárias'],
                    'intellectual': ['Metodologias proprietárias', 'Best practices', 'Certificações']
                },
                'key_activities': {
                    'core': ['Consultoria e implementação', 'Desenvolvimento customizado', 'Suporte mission-critical'],
                    'support': ['Treinamento e certificação', 'Account management', 'R&D']
                },
                'key_partnerships': {
                    'strategic': ['Big 4 consulting', 'System integrators', 'Technology vendors'],
                    'suppliers': ['Hardware providers', 'Data center operators'],
                    'channels': ['VARs especializados', 'Global SIs']
                },
                'cost_structure': {
                    'fixed_costs': ['Pessoal altamente especializado', 'P&D', 'Infraestrutura'],
                    'variable_costs': ['Comissões de venda', 'Custos de implementação', 'Suporte'],
                    'major_investments': ['Modernização da plataforma', 'Aquisições', 'Expansão global']
                }
            },
            'distribution_model': {
                'type': 'hybrid',
                'deployment': ['On-premise', 'Private cloud', 'Managed services'],
                'advantages': ['Controle total', 'Customização', 'Segurança máxima'],
                'challenges': ['Alto custo de implementação', 'Upgrades complexos', 'Time to value longo']
            },
            'licensing_model': {
                'type': 'proprietary',
                'approach': 'Perpetual + Subscription hybrid',
                'terms': ['Per processor/core', 'Named user', 'Enterprise unlimited']
            },
            'target_audience': {
                'model': 'B2B Enterprise',
                'segments': ['Fortune 500', 'Governo Federal', 'Bancos e Seguradoras'],
                'geographic_focus': 'Brasil e América Latina',
                'expansion_potential': 'Moderado - vendas complexas limitam velocidade'
            },
            'revenue_model_analysis': {
                'current_models': ['License', 'Maintenance', 'Services'],
                'revenue_mix': {
                    'recurring': '40%',
                    'one_time': '35%',
                    'services': '25%'
                },
                'growth_drivers': ['Modernização de legado', 'Digital transformation', 'Compliance'],
                'unit_economics': {
                    'cac': 'R$ 150.000',
                    'ltv': 'R$ 5.000.000',
                    'ltv_cac_ratio': 33.3,
                    'payback_period': '18 meses'
                }
            },
            'sustainability_analysis': {
                'financial_sustainability': 'medium',
                'competitive_moat': ['Switching costs altíssimos', 'Conhecimento do cliente', 'Compliance'],
                'scalability': 'low',
                'risks': ['Disrupção por cloud natives', 'Ciclos longos de venda', 'Dependência de grandes contratos']
            }
        }

    def _create_error_result(self, error_message: str, elapsed_time: float) -> ResearchResult:
        """Cria resultado de erro."""
        return ResearchResult(
            service_name=self.get_name(),
            service_version=self.get_version(),
            timestamp=datetime.now(timezone.utc),
            cost=Decimal("0"),
            success=False,
            error=error_message,
            processing_time_seconds=elapsed_time,
            confidence_score=0.0
        )
