"""
Basic Dossier Service - Serviço para gerar dossiê básico de empresas.

Este serviço gera informações gerais sobre a empresa incluindo:
- Informações gerais e executivos
- Histórico da empresa
- Produtos e serviços
- Concorrentes principais
- Cases de sucesso
- Saúde financeira
- Análise SWOT básica
- Oportunidades de novos produtos
- Melhorias futuras

Custo: $0.006 por execução
"""
import json
import logging
from typing import Dict, Any, List, Optional
from decimal import Decimal
from datetime import datetime, UTC

from app.core import (
    IResearchService,
    ResearchRequest,
    ResearchResult,
    ResearchProviderError,
    Timer
)

logger = logging.getLogger(__name__)


class BasicDossierService(IResearchService):
    """
    Serviço para gerar dossiê básico de empresas.

    Utiliza Perplexity API para coletar informações fundamentais
    sobre a empresa, incluindo concorrentes, histórico e análise SWOT.
    """

    def __init__(self, perplexity_provider):
        """
        Args:
            perplexity_provider: Instância do provider Perplexity
        """
        self.provider = perplexity_provider
        self._version = "1.0.0"
        self._cost = Decimal("0.006")

    def get_name(self) -> str:
        return "basic_dossier"

    def get_version(self) -> str:
        return self._version

    def get_description(self) -> str:
        return (
            "Gera dossiê básico da empresa incluindo informações gerais, "
            "histórico, produtos/serviços, concorrentes, cases de sucesso, "
            "saúde financeira e análise SWOT básica."
        )

    def get_cost(self) -> Decimal:
        return self._cost

    def get_required_fields(self) -> List[str]:
        return ["company_name", "company_url"]

    def get_optional_fields(self) -> List[str]:
        return ["city", "state", "country"]

    async def validate_request(self, request: ResearchRequest) -> bool:
        """Valida se a requisição tem os campos necessários."""
        # Verifica campos obrigatórios
        if not request.company_name or not request.company_url:
            return False

        # Nome da empresa deve ter pelo menos 2 caracteres
        if len(request.company_name.strip()) < 2:
            return False

        return True

    async def execute(self, request: ResearchRequest) -> ResearchResult:
        """
        Executa a pesquisa de dossiê básico.

        Args:
            request: Requisição com dados da empresa

        Returns:
            ResearchResult com o dossiê básico
        """
        with Timer() as timer:
            try:
                # Preparar dados
                empresa = request.company_name
                site = request.company_url

                # Extrair cidade e estado do contexto adicional se disponível
                cidade = None
                estado = None
                if request.additional_context:
                    cidade = request.additional_context.get("city")
                    estado = request.additional_context.get("state")

                # Construir prompt do sistema
                system_content = self._build_system_prompt()

                # Construir prompt do usuário
                user_content = self._build_user_prompt(
                    empresa=empresa,
                    site=site,
                    cidade=cidade,
                    estado=estado
                )

                # Fazer requisição ao provider
                logger.info(f"Gerando dossiê básico para {empresa}")
                result_data = await self.provider.search(
                    system_prompt=system_content,
                    user_prompt=user_content,
                    temperature=0.7
                )

                # Processar resultado
                processed_data = self._process_result(result_data)

                # Calcular score de confiança baseado na completude dos dados
                confidence_score = self._calculate_confidence_score(
                    processed_data)

                return ResearchResult(
                    service_name=self.get_name(),
                    service_version=self.get_version(),
                    timestamp=datetime.now(UTC),
                    cost=self._cost,
                    success=True,
                    data=processed_data,
                    processing_time_seconds=timer.elapsed,
                    confidence_score=confidence_score
                )

            except Exception as e:
                logger.error(f"Erro ao gerar dossiê básico: {str(e)}")
                return ResearchResult(
                    service_name=self.get_name(),
                    service_version=self.get_version(),
                    timestamp=datetime.now(UTC),
                    cost=Decimal("0"),  # Não cobra em caso de erro
                    success=False,
                    error=str(e),
                    processing_time_seconds=timer.elapsed
                )

    def get_sample_output(self) -> Dict[str, Any]:
        """Retorna exemplo de saída do serviço."""
        return {
            "informacoes_gerais": {
                "nome": "Empresa Exemplo S.A.",
                "descricao": "Líder em soluções tecnológicas para o mercado B2B",
                "executivos": ["João Silva - CEO", "Maria Santos - CTO"]
            },
            "historico": "Fundada em 2010, a empresa começou como startup...",
            "produtos_servicos": ["Plataforma SaaS", "Consultoria técnica"],
            "concorrentes": {
                "principais": ["Concorrente A", "Concorrente B", "Concorrente C"],
                "diferenciais_concorrentes": "Melhor atendimento e preço competitivo"
            },
            "cases_sucesso": {
                "case_1": "Implementação bem-sucedida no Cliente X",
                "case_2": "Redução de 40% nos custos do Cliente Y"
            },
            "saude_financeira": {
                "faturamento_historico": "R$ 5M (2021) → R$ 12M (2023)",
                "projecao_2024": "R$ 18M esperados",
                "crescimento": "140% nos últimos 2 anos"
            },
            "analise_swot": {
                "forcas": ["Tecnologia proprietária", "Equipe experiente"],
                "fraquezas": ["Dependência de poucos clientes", "Marketing limitado"],
                "oportunidades": ["Expansão internacional", "Novos verticais"],
                "ameacas": ["Concorrência global", "Mudanças regulatórias"]
            },
            "oportunidades_novos_produtos": ["IA integrada", "Mobile app"],
            "melhorias_futuras": ["UX/UI", "Integrações com terceiros"]
        }

    def _build_system_prompt(self) -> str:
        """Constrói o prompt do sistema para o provider."""
        return (
            "Você é um analista sênior de inteligência de mercado especializado em análise competitiva. "
            "Sua missão é gerar dossiês completos e detalhados sobre empresas, sempre respondendo APENAS em JSON válido, "
            "sem comentários ou texto fora do objeto JSON. "
            "OBRIGATÓRIO: Inclua sempre uma seção detalhada de concorrentes principais com nomes reais e específicos. "
            "Seja descritivo, profissional e use dados atualizados, exemplos concretos, nomes, números e fatos verificáveis. "
            "Se algum dado não for encontrado, use 'Dado não encontrado'. "
            "O texto deve estar em português do Brasil, tom profissional, pronto para uso em relatório corporativo."
        )

    def _build_user_prompt(
        self,
        empresa: str,
        site: str,
        cidade: Optional[str] = None,
        estado: Optional[str] = None
    ) -> str:
        """Constrói o prompt do usuário com os dados da empresa."""
        # Preparar localização
        localizacao = ""
        if cidade or estado:
            partes = []
            if cidade:
                partes.append(cidade)
            if estado:
                partes.append(estado)
            partes.append("Brasil")
            localizacao = f", sediada em {', '.join(partes)}"

        return (
            f"Elabore um dossiê completo e analítico sobre a empresa {empresa}{localizacao}, site {site}. "
            "IMPORTANTE: Inclua OBRIGATORIAMENTE uma análise detalhada dos principais concorrentes diretos e indiretos, "
            "com nomes específicos de empresas. "
            "O dossiê deve seguir EXATAMENTE esta estrutura JSON:\n"
            "{\n"
            "  \"informacoes_gerais\": {\n"
            "    \"nome\": \"Nome da empresa\",\n"
            "    \"descricao\": \"Descrição detalhada\",\n"
            "    \"executivos\": [\"Nome 1 - Cargo\", \"Nome 2 - Cargo\"]\n"
            "  },\n"
            "  \"historico\": \"Histórico detalhado da empresa\",\n"
            "  \"produtos_servicos\": [\"Produto/Serviço 1\", \"Produto/Serviço 2\"],\n"
            "  \"concorrentes\": {\n"
            "    \"principais\": [\"Nome Concorrente 1\", \"Nome Concorrente 2\", \"Nome Concorrente 3\"],\n"
            "    \"diferenciais_concorrentes\": \"O que os concorrentes fazem melhor\"\n"
            "  },\n"
            "  \"cases_sucesso\": {\n"
            "    \"case_1\": \"Descrição detalhada do case 1\",\n"
            "    \"case_2\": \"Descrição detalhada do case 2\"\n"
            "  },\n"
            "  \"saude_financeira\": {\n"
            "    \"faturamento_historico\": \"Dados financeiros históricos\",\n"
            "    \"projecao_2024\": \"Projeção para 2024\",\n"
            "    \"crescimento\": \"Análise de crescimento\"\n"
            "  },\n"
            "  \"analise_swot\": {\n"
            "    \"forcas\": [\"Força 1\", \"Força 2\"],\n"
            "    \"fraquezas\": [\"Fraqueza 1\", \"Fraqueza 2\"],\n"
            "    \"oportunidades\": [\"Oportunidade 1\", \"Oportunidade 2\"],\n"
            "    \"ameacas\": [\"Ameaça 1\", \"Ameaça 2\"]\n"
            "  },\n"
            "  \"oportunidades_novos_produtos\": [\"Produto 1\", \"Produto 2\"],\n"
            "  \"melhorias_futuras\": [\"Melhoria 1\", \"Melhoria 2\"]\n"
            "}\n\n"
            "CRÍTICO: A seção 'concorrentes' deve conter nomes reais e específicos de empresas que competem no mesmo mercado. "
            "Evite respostas genéricas. Pesquise e identifique competidores diretos e indiretos da empresa. "
            "Seja detalhado em todas as seções, especialmente em concorrentes, cases de sucesso e análise financeira."
        )

    def _process_result(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """Processa e valida o resultado do provider."""
        # Se o provider retornou um string JSON, fazer parse
        if isinstance(raw_data, str):
            try:
                data = json.loads(raw_data)
            except json.JSONDecodeError:
                logger.error("Resposta não é JSON válido")
                return self._get_empty_result()
        else:
            data = raw_data

        # Validar estrutura esperada
        required_keys = [
            "informacoes_gerais", "historico", "produtos_servicos",
            "concorrentes", "cases_sucesso", "saude_financeira",
            "analise_swot", "oportunidades_novos_produtos", "melhorias_futuras"
        ]

        for key in required_keys:
            if key not in data:
                logger.warning(f"Campo obrigatório ausente: {key}")
                data[key] = self._get_default_value_for_key(key)

        return data

    def _calculate_confidence_score(self, data: Dict[str, Any]) -> float:
        """
        Calcula score de confiança baseado na completude dos dados.

        Returns:
            Score entre 0.0 e 1.0
        """
        total_fields = 0
        filled_fields = 0

        # Verificar campos principais
        checks = [
            ("informacoes_gerais.descricao", lambda d: d.get("informacoes_gerais", {}).get("descricao") and
             d["informacoes_gerais"]["descricao"] != "Dado não encontrado"),
            ("informacoes_gerais.executivos", lambda d: len(
                d.get("informacoes_gerais", {}).get("executivos", [])) > 0),
            ("historico", lambda d: d.get("historico")
             and len(d["historico"]) > 50),
            ("produtos_servicos", lambda d: len(
                d.get("produtos_servicos", [])) > 0),
            ("concorrentes.principais", lambda d: len(
                d.get("concorrentes", {}).get("principais", [])) >= 2),
            ("cases_sucesso", lambda d: bool(d.get("cases_sucesso", {}))),
            ("saude_financeira.faturamento_historico", lambda d: d.get("saude_financeira", {}).get("faturamento_historico") and
             d["saude_financeira"]["faturamento_historico"] != "Dado não encontrado"),
            ("analise_swot", lambda d: all(len(d.get("analise_swot", {}).get(k, [])) > 0
                                           for k in ["forcas", "fraquezas", "oportunidades", "ameacas"]))
        ]

        for check_func in checks:
            total_fields += 1
            try:
                if check_func(data):
                    filled_fields += 1
            except Exception:
                pass

        return filled_fields / total_fields if total_fields > 0 else 0.0

    def _get_empty_result(self) -> Dict[str, Any]:
        """Retorna estrutura vazia quando não há dados."""
        return {
            "informacoes_gerais": {
                "nome": "Dado não encontrado",
                "descricao": "Dado não encontrado",
                "executivos": []
            },
            "historico": "Dado não encontrado",
            "produtos_servicos": [],
            "concorrentes": {
                "principais": [],
                "diferenciais_concorrentes": "Dado não encontrado"
            },
            "cases_sucesso": {},
            "saude_financeira": {
                "faturamento_historico": "Dado não encontrado",
                "projecao_2024": "Dado não encontrado",
                "crescimento": "Dado não encontrado"
            },
            "analise_swot": {
                "forcas": [],
                "fraquezas": [],
                "oportunidades": [],
                "ameacas": []
            },
            "oportunidades_novos_produtos": [],
            "melhorias_futuras": []
        }

    def _get_default_value_for_key(self, key: str) -> Any:
        """Retorna valor padrão para campos ausentes."""
        defaults = {
            "informacoes_gerais": {"nome": "Dado não encontrado", "descricao": "Dado não encontrado", "executivos": []},
            "historico": "Dado não encontrado",
            "produtos_servicos": [],
            "concorrentes": {"principais": [], "diferenciais_concorrentes": "Dado não encontrado"},
            "cases_sucesso": {},
            "saude_financeira": {
                "faturamento_historico": "Dado não encontrado",
                "projecao_2024": "Dado não encontrado",
                "crescimento": "Dado não encontrado"
            },
            "analise_swot": {"forcas": [], "fraquezas": [], "oportunidades": [], "ameacas": []},
            "oportunidades_novos_produtos": [],
            "melhorias_futuras": []
        }
        return defaults.get(key, "Dado não encontrado")
