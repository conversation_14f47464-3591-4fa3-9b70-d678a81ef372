# [TASK-006] Technical Diagnostics Production Integration

**Status**: 📝 TODO
**Prioridade**: P1 (CRÍTICO)
**Estimativa**: 24h
**Due**: 2025-02-03
**Tags**: [production-ready, diagnostics, integration, lighthouse, visual-analysis]
**Blocked by**: None

## 🎯 Goal

Integrar completamente os serviços de diagnóstico técnico REAIS (Lighthouse, Playwright, VisualAnalyzer) no `tech_diagnostic_service.py`, removendo TODOS os dados mock e garantindo funcionamento production-ready.

## 📋 Context

- `tech_diagnostic_service.py` atualmente usa dados mock
- Já existem implementações production-ready em `/tools/diagnostics/`
- Princípio: **JAMAIS USAR MOCK - PRODUCTION READY**
- Aumentar max_recommendations de 3 para 7

## 🚀 Implementation Plan

### FASE 1: Atualização do Tech Diagnostic Service (8h)

#### MT-1.1: Remover Mock Data e Aumentar Recommendations (2h)
**Goal**: Limpar todo código mock e configurar max_recommendations=7
**Files Affected**: 
- `backend/app/services/research/tech_diagnostic_service.py`
**Outline**:
1. Remover método `_get_mock_diagnostic_data` completo
2. Remover todos os métodos `_generate_*` que geram dados fake
3. Alterar `return recommendations[:3]` para `return recommendations[:7]`
4. Adicionar imports dos serviços reais de `/tools/diagnostics/`

#### MT-1.2: Criar Diagnostic Service Wrapper (2h)
**Goal**: Criar wrapper para adaptar serviços existentes ao padrão IResearchService
**Files Affected**:
- `backend/app/services/research/diagnostic_wrapper.py` (NOVO)
**Outline**:
```python
class DiagnosticServiceWrapper:
    def __init__(self):
        self.lighthouse = LighthouseAnalyzer()
        self.screenshots = PlaywrightScreenshots()
        self.visual_analyzer = VisualAnalyzer(api_key)
        self.consolidator = DiagnosticConsolidator()
```

#### MT-1.3: Implementar Execute Method Real (4h)
**Goal**: Substituir execute() com implementação real
**Files Affected**:
- `backend/app/services/research/tech_diagnostic_service.py`
**Outline**:
1. Integrar ParallelDiagnostics para execução paralela
2. Processar resultados reais do Lighthouse
3. Capturar screenshots reais com Playwright
4. Analisar com VisualAnalyzer (OpenAI Vision API)
5. Consolidar com DiagnosticConsolidator

### FASE 2: Integração de Serviços Auxiliares (8h)

#### MT-2.1: Integrar LighthouseAnalyzer (2h)
**Goal**: Configurar e integrar análise Lighthouse real
**Files Affected**:
- `backend/app/services/research/tech_diagnostic_service.py`
- `backend/tools/diagnostics/lighthouse_analyzer.py`
**Outline**:
1. Verificar instalação do Lighthouse (`npm install -g lighthouse`)
2. Configurar chrome path e flags
3. Implementar chamada assíncrona
4. Processar resultados estruturados

#### MT-2.2: Integrar PlaywrightScreenshots (2h)
**Goal**: Configurar captura real de screenshots
**Files Affected**:
- `backend/app/services/research/tech_diagnostic_service.py`
- `backend/tools/diagnostics/playwright_screenshots.py`
**Outline**:
1. Configurar Playwright browsers
2. Implementar captura desktop/mobile
3. Gerenciar armazenamento de screenshots
4. Cleanup de arquivos antigos

#### MT-2.3: Integrar VisualAnalyzer com OpenAI (2h)
**Goal**: Configurar análise visual real com IA
**Files Affected**:
- `backend/app/services/research/tech_diagnostic_service.py`
- `backend/tools/diagnostics/visual_analyzer.py`
**Outline**:
1. Configurar OpenAI API key (não Perplexity!)
2. Implementar análise de screenshots
3. Processar insights visuais
4. Fallback técnico se API indisponível

#### MT-2.4: Configurar DiagnosticConsolidator (2h)
**Goal**: Consolidar todos os resultados em relatório final
**Files Affected**:
- `backend/app/services/research/tech_diagnostic_service.py`
- `backend/tools/diagnostics/consolidator.py`
**Outline**:
1. Processar dados de todas as fontes
2. Calcular scores consolidados
3. Gerar recomendações priorizadas (7 no máximo)
4. Criar timeline de implementação

### FASE 3: Error Handling e Robustez (4h)

#### MT-3.1: Implementar Error Handling Robusto (2h)
**Goal**: Garantir que falhas parciais não quebrem o serviço
**Files Affected**:
- `backend/app/services/research/tech_diagnostic_service.py`
**Outline**:
1. Try-catch para cada serviço individual
2. Fallbacks para dados técnicos se IA falhar
3. Logging detalhado de erros
4. Retry logic para APIs externas

#### MT-3.2: Adicionar Circuit Breaker Pattern (2h)
**Goal**: Evitar cascata de falhas em serviços externos
**Files Affected**:
- `backend/app/core/patterns/circuit_breaker.py` (NOVO)
- `backend/app/services/research/tech_diagnostic_service.py`
**Outline**:
1. Implementar circuit breaker genérico
2. Aplicar em chamadas ao Lighthouse
3. Aplicar em chamadas à OpenAI API
4. Configurar thresholds e timeouts

### FASE 4: Testes e Validação (4h)

#### MT-4.1: Criar Testes de Integração (2h)
**Goal**: Garantir funcionamento end-to-end
**Files Affected**:
- `backend/tests/services/test_tech_diagnostic_integration.py` (NOVO)
**Outline**:
1. Mock de APIs externas para testes
2. Testar fluxo completo
3. Testar cenários de falha
4. Validar estrutura de resposta

#### MT-4.2: Testes de Performance (2h)
**Goal**: Garantir SLA de 2-3 minutos
**Files Affected**:
- `backend/tests/performance/test_diagnostics_performance.py` (NOVO)
**Outline**:
1. Medir tempo de execução paralela
2. Identificar gargalos
3. Otimizar se necessário
4. Documentar benchmarks

## 📊 Success Criteria

- [ ] ZERO dados mock no código
- [ ] Lighthouse executando análises reais
- [ ] Screenshots sendo capturados corretamente
- [ ] Análise visual com OpenAI funcionando
- [ ] 7 recomendações sendo retornadas
- [ ] Tempo de execução < 3 minutos
- [ ] Error handling robusto
- [ ] Testes com cobertura > 80%

## 🔧 Technical Requirements

### Dependências Obrigatórias
```bash
# Sistema
npm install -g lighthouse
playwright install chromium

# Python
pip install playwright
pip install aiohttp
pip install openai
```

### Variáveis de Ambiente
```env
OPENAI_API_KEY=sk-xxx  # Para VisualAnalyzer
LIGHTHOUSE_CHROMIUM_PATH=/usr/bin/google-chrome-stable
PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome-stable
```

### Estrutura de Resposta Esperada
```python
{
    "service_name": "tech_diagnostic",
    "success": True,
    "data": {
        "performance": {
            "score": 85,
            "core_web_vitals": {...},
            "opportunities": [...],
            "diagnostics": [...]
        },
        "security": {
            "score": 90,
            "https_enabled": True,
            "security_headers": {...},
            "vulnerabilities": []
        },
        "accessibility": {
            "score": 78,
            "wcag_level": "AA",
            "issues": [...]
        },
        "seo": {
            "score": 82,
            "meta_tags": {...},
            "structured_data": {...}
        },
        "visual_analysis": {
            "layout_quality": "good",
            "user_experience": "...",
            "visual_hierarchy": "...",
            "responsive_design": "..."
        },
        "top_recommendations": [
            # 7 recomendações priorizadas
        ]
    },
    "cost": 0.010,
    "processing_time_seconds": 120.5,
    "confidence_score": 0.95
}
```

## 🚨 Riscos e Mitigações

| Risco | Impacto | Mitigação |
|-------|---------|-----------|
| Lighthouse falhar | Alto | Fallback para análise básica HTTP |
| OpenAI API down | Médio | Análise técnica sem visual |
| Timeout em sites lentos | Médio | Configurar timeouts generosos |
| Screenshots grandes | Baixo | Compressão e cleanup automático |

## 📝 Notas de Implementação

### Prioridade de Execução
1. **Crítico**: Remover mocks e integrar Lighthouse
2. **Alto**: Screenshots e consolidação  
3. **Médio**: Análise visual com IA
4. **Baixo**: Otimizações de performance

### Padrões a Seguir
- AIDEV-NOTE em código complexo
- Async/await para todas as operações I/O
- Type hints em todos os métodos
- Docstrings detalhadas
- Logging estruturado

### Integração com Orchestrator
```python
# Em research_orchestrator.py
from app.services.research import TechDiagnosticService

# Registrar com provider real
tech_service = TechDiagnosticService(perplexity_provider=None)  # Não usa Perplexity!
registry.register(tech_service)
```

## 🎯 Definition of Done

- [ ] Código sem nenhum mock implementado
- [ ] Todas as integrações testadas manualmente
- [ ] Documentação atualizada
- [ ] PR aprovado com 0 comentários pendentes
- [ ] Deploy em staging validado
- [ ] Monitoramento configurado
- [ ] SLA de 3 minutos cumprido
- [ ] Score de qualidade ≥ 90%

---

**Criado em**: 2025-01-30 17:15  
**Criado por**: AI Assistant  
**Última atualização**: 2025-01-30 17:15 