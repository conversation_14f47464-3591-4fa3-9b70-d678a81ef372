"""
Tech Stack Analysis Service - Análise detalhada da stack tecnológica

Este serviço analisa e identifica todas as tecnologias utilizadas por uma empresa,
incluindo frontend, backend, banco de dados, infraestrutura, analytics, e ferramentas.

Custo: $0.005 por execução
"""
import json
import logging
from typing import Dict, Any, List, Optional
from decimal import Decimal
from datetime import datetime, timezone

from app.core import (
    IResearchService,
    ResearchRequest,
    ResearchResult,
    Timer
)

logger = logging.getLogger(__name__)


class TechStackService(IResearchService):
    """
    Serviço especializado em análise de stack tecnológica.

    Identifica e categoriza todas as tecnologias utilizadas pela empresa:
    - Frontend: Frameworks, bibliotecas, ferramentas de build
    - Backend: Linguagens, frameworks, APIs
    - Banco de Dados: SQL, NoSQL, cache
    - Infraestrutura: Cloud providers, containers, CI/CD
    - Analytics: Ferramentas de monitoramento, BI, tracking
    - Outras ferramentas: CMS, e-commerce, marketing, etc.
    """

    def __init__(self, provider):
        """
        Args:
            provider: Instância do provider (Perplexity, SearchSociety, etc)
        """
        self.provider = provider
        self._version = "1.0.0"
        self._cost = Decimal("0.005")

    def get_name(self) -> str:
        return "tech_stack"

    def get_version(self) -> str:
        return self._version

    def get_description(self) -> str:
        return "Análise completa da stack tecnológica incluindo frontend, backend, banco de dados, infraestrutura e ferramentas"

    def get_cost(self) -> Decimal:
        return self._cost

    def get_required_fields(self) -> List[str]:
        return ["company_name", "company_url"]

    def get_optional_fields(self) -> List[str]:
        return ["industry", "company_size", "additional_urls"]

    async def validate_request(self, request: ResearchRequest) -> bool:
        """Valida se a requisição tem os dados necessários."""
        if not request.company_name or not request.company_url:
            logger.error("Nome da empresa e URL são obrigatórios")
            return False

        # Validar formato da URL
        url = request.company_url.lower()
        if not url.startswith(('http://', 'https://')):
            logger.error(f"URL inválida: {request.company_url}")
            return False

        return True

    async def execute(self, request: ResearchRequest) -> ResearchResult:
        """Executa a análise de tech stack."""
        with Timer() as timer:
            try:
                empresa = request.company_name
                site = request.company_url
                # AIDEV-NOTE: industry pode vir em additional_context ou como atributo direto
                industria = getattr(request, 'industry', None)
                if industria is None and request.additional_context:
                    industria = request.additional_context.get(
                        'industry', 'não especificada')
                if industria is None:
                    industria = 'não especificada'

                # AIDEV-NOTE: Construir prompts especializados para análise técnica
                system_prompt = self._build_system_prompt()
                user_prompt = self._build_user_prompt(empresa, site, industria)

                logger.info(f"Executando análise de tech stack para {empresa}")

                # Fazer requisição ao provider
                raw_result = await self.provider.search(
                    system_prompt=system_prompt,
                    user_prompt=user_prompt,
                    temperature=0.7,
                    max_tokens=2000
                )

                # Processar e estruturar resultado
                processed_data = self._process_result(raw_result, empresa)

                # Calcular confidence score
                confidence_score = self._calculate_confidence_score(
                    processed_data)

                # Adicionar metadados
                processed_data['metadata'] = {
                    'analysis_date': datetime.now(timezone.utc).isoformat(),
                    'company_url': site,
                    'industry': industria,
                    'confidence_score': confidence_score,
                    'total_technologies_found': self._count_technologies(processed_data)
                }

                return ResearchResult(
                    service_name=self.get_name(),
                    service_version=self.get_version(),
                    timestamp=datetime.now(timezone.utc),
                    cost=self._cost,
                    success=True,
                    data=processed_data,
                    processing_time_seconds=timer.elapsed,
                    confidence_score=confidence_score
                )

            except json.JSONDecodeError as e:
                logger.error(f"Erro ao processar JSON: {str(e)}")
                return self._create_error_result(
                    "Erro ao processar resposta do modelo",
                    timer.elapsed
                )
            except Exception as e:
                logger.error(
                    f"Erro na análise de tech stack: {str(e)}", exc_info=True)
                return self._create_error_result(str(e), timer.elapsed)

    def get_sample_output(self) -> Dict[str, Any]:
        """Retorna exemplo de saída do serviço."""
        return {
            "frontend": {
                "frameworks": ["React", "Next.js"],
                "languages": ["TypeScript", "JavaScript"],
                "styling": ["Tailwind CSS", "Styled Components"],
                "build_tools": ["Webpack", "Vite"],
                "testing": ["Jest", "React Testing Library"],
                "state_management": ["Redux", "Context API"]
            },
            "backend": {
                "languages": ["Python", "Node.js"],
                "frameworks": ["FastAPI", "Express"],
                "apis": ["REST", "GraphQL"],
                "authentication": ["JWT", "OAuth2"],
                "testing": ["Pytest", "Jest"]
            },
            "database": {
                "sql": ["PostgreSQL", "MySQL"],
                "nosql": ["MongoDB", "Redis"],
                "orm": ["SQLAlchemy", "Prisma"],
                "cache": ["Redis", "Memcached"]
            },
            "infrastructure": {
                "cloud": ["AWS", "Google Cloud"],
                "containers": ["Docker", "Kubernetes"],
                "ci_cd": ["GitHub Actions", "Jenkins"],
                "monitoring": ["Datadog", "New Relic"],
                "cdn": ["Cloudflare", "AWS CloudFront"]
            },
            "analytics": {
                "tracking": ["Google Analytics", "Mixpanel"],
                "business_intelligence": ["Tableau", "Power BI"],
                "error_tracking": ["Sentry", "Rollbar"],
                "performance": ["Lighthouse", "WebPageTest"]
            },
            "tools": {
                "cms": ["WordPress", "Strapi"],
                "ecommerce": ["Shopify", "WooCommerce"],
                "communication": ["Slack", "Discord"],
                "project_management": ["Jira", "Trello"],
                "version_control": ["Git", "GitHub"]
            },
            "metadata": {
                "analysis_date": "2025-01-30T10:00:00Z",
                "company_url": "https://example.com",
                "industry": "tecnologia",
                "confidence_score": 0.85,
                "total_technologies_found": 42
            }
        }

    def _build_system_prompt(self) -> str:
        """Constrói o prompt do sistema para análise técnica."""
        return """Você é um especialista em análise de stack tecnológica com profundo conhecimento em:
- Desenvolvimento Frontend e Backend
- Bancos de dados e arquitetura de sistemas
- Infraestrutura cloud e DevOps
- Ferramentas de analytics e monitoramento
- Tecnologias web modernas

Sua tarefa é identificar TODAS as tecnologias utilizadas por uma empresa através da análise de:
1. Código fonte visível (HTML, CSS, JavaScript)
2. Headers HTTP e metadados
3. Padrões de URL e estrutura do site
4. Integrações visíveis (analytics, chat, etc)
5. Informações públicas sobre a empresa

Você deve categorizar as tecnologias encontradas e fornecer uma análise completa e estruturada."""

    def _build_user_prompt(self, empresa: str, site: str, industria: str) -> str:
        """Constrói o prompt do usuário com dados específicos."""
        return f"""Analise a stack tecnológica completa da empresa {empresa} ({site}).
Indústria: {industria}

Identifique e categorize TODAS as tecnologias detectáveis:

1. **Frontend**: Frameworks, bibliotecas, linguagens, ferramentas de build, styling
2. **Backend**: Linguagens, frameworks, APIs, servidores
3. **Database**: Bancos SQL/NoSQL, cache, ORMs
4. **Infrastructure**: Cloud providers, containers, CI/CD, monitoring
5. **Analytics**: Tracking, BI, error tracking, performance
6. **Tools**: CMS, e-commerce, comunicação, gestão

Para cada tecnologia encontrada, indique:
- Nome exato da tecnologia
- Versão (se detectável)
- Nível de certeza (alta/média/baixa)

IMPORTANTE:
- Baseie-se apenas em evidências concretas
- Se não tiver certeza, indique "possível" ou "provável"
- Use "Não detectado" para categorias sem informação
- Forneça a resposta em JSON estruturado

Formato de resposta:
{{
    "frontend": {{
        "frameworks": ["Nome Framework"],
        "languages": ["JavaScript", "TypeScript"],
        "styling": ["CSS Framework"],
        "build_tools": ["Tool"],
        "testing": ["Framework de teste"],
        "state_management": ["Redux", "MobX"]
    }},
    "backend": {{
        "languages": ["Python", "Node.js"],
        "frameworks": ["FastAPI", "Express"],
        "apis": ["REST", "GraphQL"],
        "authentication": ["JWT", "OAuth"],
        "testing": ["Pytest"]
    }},
    "database": {{
        "sql": ["PostgreSQL"],
        "nosql": ["MongoDB"],
        "orm": ["SQLAlchemy"],
        "cache": ["Redis"]
    }},
    "infrastructure": {{
        "cloud": ["AWS", "GCP"],
        "containers": ["Docker"],
        "ci_cd": ["GitHub Actions"],
        "monitoring": ["Datadog"],
        "cdn": ["Cloudflare"]
    }},
    "analytics": {{
        "tracking": ["Google Analytics"],
        "business_intelligence": ["Tableau"],
        "error_tracking": ["Sentry"],
        "performance": ["Lighthouse"]
    }},
    "tools": {{
        "cms": ["WordPress"],
        "ecommerce": ["Shopify"],
        "communication": ["Slack"],
        "project_management": ["Jira"],
        "version_control": ["Git"]
    }}
}}"""

    def _process_result(self, raw_data: Any, empresa: str) -> Dict[str, Any]:
        """Processa e valida o resultado da análise."""
        # Parse JSON se necessário
        if isinstance(raw_data, str):
            try:
                data = json.loads(raw_data)
            except json.JSONDecodeError:
                logger.warning(f"Resposta não é JSON válido para {empresa}")
                return self._get_empty_tech_stack()
        else:
            data = raw_data

        # Validar e normalizar estrutura
        result = self._get_empty_tech_stack()

        # AIDEV-NOTE: Mapear categorias principais garantindo estrutura consistente
        categories = ['frontend', 'backend', 'database',
                      'infrastructure', 'analytics', 'tools']

        for category in categories:
            if category in data and isinstance(data[category], dict):
                result[category] = self._normalize_category(
                    data[category], category)
            else:
                logger.warning(
                    f"Categoria {category} não encontrada ou inválida")

        return result

    def _normalize_category(self, category_data: Dict, category_name: str) -> Dict[str, List[str]]:
        """Normaliza dados de uma categoria específica."""
        normalized = {}

        # Definir subcategorias esperadas por categoria
        expected_subcategories = {
            'frontend': ['frameworks', 'languages', 'styling', 'build_tools', 'testing', 'state_management'],
            'backend': ['languages', 'frameworks', 'apis', 'authentication', 'testing'],
            'database': ['sql', 'nosql', 'orm', 'cache'],
            'infrastructure': ['cloud', 'containers', 'ci_cd', 'monitoring', 'cdn'],
            'analytics': ['tracking', 'business_intelligence', 'error_tracking', 'performance'],
            'tools': ['cms', 'ecommerce', 'communication', 'project_management', 'version_control']
        }

        for subcat in expected_subcategories.get(category_name, []):
            if subcat in category_data:
                # Garantir que é uma lista de strings
                value = category_data[subcat]
                if isinstance(value, list):
                    normalized[subcat] = [
                        str(item) for item in value if item and str(item) != "Não detectado"]
                elif isinstance(value, str) and value != "Não detectado":
                    normalized[subcat] = [value]
                else:
                    normalized[subcat] = []
            else:
                normalized[subcat] = []

        return normalized

    def _calculate_confidence_score(self, data: Dict[str, Any]) -> float:
        """
        Calcula score de confiança baseado na completude e qualidade dos dados.
        """
        total_checks = 0
        passed_checks = 0

        # Verificar cada categoria principal
        categories = ['frontend', 'backend', 'database',
                      'infrastructure', 'analytics', 'tools']

        for category in categories:
            if category in data and isinstance(data[category], dict):
                cat_data = data[category]

                # Verificar se tem pelo menos uma tecnologia identificada
                has_tech = any(
                    isinstance(v, list) and len(v) > 0
                    for v in cat_data.values()
                )

                total_checks += 1
                if has_tech:
                    passed_checks += 1

                # Verificação adicional para categorias críticas
                if category in ['frontend', 'backend']:
                    # Deve ter pelo menos linguagem ou framework
                    critical_keys = ['languages', 'frameworks']
                    for key in critical_keys:
                        total_checks += 0.5
                        if key in cat_data and len(cat_data.get(key, [])) > 0:
                            passed_checks += 0.5

                # Calcular score final
        base_score = passed_checks / total_checks if total_checks > 0 else 0.0

        # Ajustar baseado no total de tecnologias encontradas
        total_tech = self._count_technologies(data)
        if total_tech > 20:
            base_score = min(1.0, base_score + 0.1)
        elif total_tech < 5 and total_tech > 0:
            # Reduzir menos drasticamente para tecnologias parciais
            base_score = max(0.3, base_score - 0.1)

        return round(base_score, 2)

    def _count_technologies(self, data: Dict[str, Any]) -> int:
        """Conta o total de tecnologias identificadas."""
        total = 0

        for category in data.values():
            if isinstance(category, dict):
                for tech_list in category.values():
                    if isinstance(tech_list, list):
                        total += len(tech_list)

        return total

    def _get_empty_tech_stack(self) -> Dict[str, Any]:
        """Retorna estrutura vazia de tech stack."""
        return {
            "frontend": {
                "frameworks": [],
                "languages": [],
                "styling": [],
                "build_tools": [],
                "testing": [],
                "state_management": []
            },
            "backend": {
                "languages": [],
                "frameworks": [],
                "apis": [],
                "authentication": [],
                "testing": []
            },
            "database": {
                "sql": [],
                "nosql": [],
                "orm": [],
                "cache": []
            },
            "infrastructure": {
                "cloud": [],
                "containers": [],
                "ci_cd": [],
                "monitoring": [],
                "cdn": []
            },
            "analytics": {
                "tracking": [],
                "business_intelligence": [],
                "error_tracking": [],
                "performance": []
            },
            "tools": {
                "cms": [],
                "ecommerce": [],
                "communication": [],
                "project_management": [],
                "version_control": []
            }
        }

    def _create_error_result(self, error_message: str, elapsed_time: float) -> ResearchResult:
        """Cria resultado de erro padronizado."""
        return ResearchResult(
            service_name=self.get_name(),
            service_version=self.get_version(),
            timestamp=datetime.now(timezone.utc),
            cost=Decimal("0"),
            success=False,
            error=error_message,
            processing_time_seconds=elapsed_time,
            data=self._get_empty_tech_stack()
        )
