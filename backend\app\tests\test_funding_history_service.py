"""
Testes para o Funding History Service.
"""
import pytest
from decimal import Decimal
from datetime import datetime, UTC
from unittest.mock import Mock, AsyncMock

from app.services.research.funding_history_service import FundingHistoryService
from app.core import ResearchRequest, ResearchResult


@pytest.fixture
def mock_provider():
    """Mock do provider de pesquisa."""
    provider = Mock()
    provider.search = AsyncMock()
    return provider


@pytest.fixture
def funding_service(mock_provider):
    """Instância do serviço com provider mockado."""
    return FundingHistoryService(mock_provider)


@pytest.fixture
def valid_request():
    """Request válido para testes."""
    return ResearchRequest(
        client_id="test_client_123",
        company_url="https://example.com",
        company_name="Example Tech",
        additional_context={
            "include_competitor_comparison": True,
            "include_investor_analysis": True,
            "target_currency": "both"
        }
    )


@pytest.fixture
def sample_provider_response():
    """Resposta simulada do provider."""
    return {
        "funding_rounds": [
            {
                "round_type": "seed",
                "amount_usd": 1000000,
                "amount_brl": 5000000,
                "date": "2023-06",
                "lead_investors": ["Investor A"],
                "all_investors": ["Investor A", "Investor B"],
                "valuation_usd": 10000000,
                "valuation_brl": 50000000
            },
            {
                "round_type": "series_a",
                "amount_usd": 5000000,
                "amount_brl": 25000000,
                "date": "2024-01",
                "lead_investors": ["Investor C"],
                "all_investors": ["Investor C", "Investor A", "Investor D"],
                "valuation_usd": 30000000,
                "valuation_brl": *********
            }
        ],
        "key_investors": [
            {
                "name": "Investor A",
                "tier": "tier_1",
                "portfolio_companies": ["Company X", "Company Y"],
                "notable_exits": ["Exit 1"],
                "focus_areas": ["SaaS", "Fintech"],
                "geographic_focus": ["Brazil", "Latin America"]
            }
        ],
        "metrics": {
            "total_raised_usd": 6000000,
            "total_raised_brl": 30000000,
            "number_of_rounds": 2,
            "average_round_size_usd": 3000000,
            "average_time_between_rounds_months": 7,
            "valuation_growth_rate": 3.0,
            "last_round_date": "2024-01",
            "months_since_last_round": 11,
            "total_investors": 4,
            "tier_1_investors": 1,
            "repeat_investors": ["Investor A"],
            "international_investors": 1,
            "funding_vs_competitors": "Above average",
            "funding_velocity": "accelerating",
            "estimated_runway_months": 18,
            "burn_rate_estimate": "R$ 500k/month",
            "funding_efficiency_score": 7.5
        },
        "analysis": {
            "strengths": [
                "Strong tier-1 investor backing",
                "Consistent funding momentum"
            ],
            "concerns": [
                "Long time since last round",
                "High burn rate"
            ],
            "investor_quality_assessment": "High quality mix of tier-1 VCs and strategic investors",
            "funding_momentum": "Strong momentum with accelerating round sizes",
            "next_round_prediction": "Series B likely in Q2 2025, targeting $15-20M",
            "strategic_recommendations": [
                "Focus on revenue growth to justify Series B valuation",
                "Consider strategic investors for next round"
            ]
        },
        "market_context": {
            "market_funding_trend": "Growing",
            "average_round_sizes": {"series_a": 5000000},
            "competitor_funding": {"Competitor A": 10000000}
        },
        "data_sources": ["Public records", "News articles", "Company website"],
        "data_gaps": []
    }


class TestFundingHistoryService:
    """Testes para o serviço de análise de funding."""

    def test_service_metadata(self, funding_service):
        """Testa metadados do serviço."""
        assert funding_service.get_name() == "funding_history"
        assert funding_service.get_version() == "1.0.0"
        assert funding_service.get_cost() == Decimal("0.008")
        assert "captação" in funding_service.get_description().lower()
        assert "investimento" in funding_service.get_description().lower()

    def test_required_fields(self, funding_service):
        """Testa campos obrigatórios."""
        required = funding_service.get_required_fields()
        assert "company_name" in required
        assert "company_url" in required
        assert len(required) == 2

    def test_optional_fields(self, funding_service):
        """Testa campos opcionais."""
        optional = funding_service.get_optional_fields()
        assert "city" in optional
        assert "state" in optional
        assert "country" in optional
        assert "include_competitor_comparison" in optional
        assert "include_investor_analysis" in optional

    @pytest.mark.asyncio
    async def test_validate_request_valid(self, funding_service, valid_request):
        """Testa validação com request válido."""
        assert await funding_service.validate_request(valid_request) is True

    @pytest.mark.asyncio
    async def test_validate_request_missing_name(self, funding_service):
        """Testa validação sem nome da empresa."""
        request = ResearchRequest(
            client_id="test_123",
            company_url="https://example.com",
            company_name=""
        )
        assert await funding_service.validate_request(request) is False

    @pytest.mark.asyncio
    async def test_validate_request_missing_url(self, funding_service):
        """Testa validação sem URL."""
        request = ResearchRequest(
            client_id="test_123",
            company_url="",
            company_name="Example Tech"
        )
        assert await funding_service.validate_request(request) is False

    @pytest.mark.asyncio
    async def test_validate_request_short_name(self, funding_service):
        """Testa validação com nome muito curto."""
        request = ResearchRequest(
            client_id="test_123",
            company_url="https://example.com",
            company_name="A"
        )
        assert await funding_service.validate_request(request) is False

    @pytest.mark.asyncio
    async def test_execute_success(self, funding_service, mock_provider, valid_request, sample_provider_response):
        """Testa execução bem-sucedida."""
        mock_provider.search.return_value = sample_provider_response

        result = await funding_service.execute(valid_request)

        assert result.success is True
        assert result.service_name == "funding_history"
        assert result.cost == Decimal("0.008")
        assert result.error is None
        assert result.data is not None

        # Verifica estrutura dos dados
        assert "funding_rounds" in result.data
        assert "metrics" in result.data
        assert "analysis" in result.data
        assert len(result.data["funding_rounds"]) == 2
        assert result.data["metrics"]["total_raised_usd"] == 6000000

    @pytest.mark.asyncio
    async def test_execute_with_json_string(self, funding_service, mock_provider, valid_request, sample_provider_response):
        """Testa execução com resposta JSON string."""
        import json
        mock_provider.search.return_value = json.dumps(
            sample_provider_response)

        result = await funding_service.execute(valid_request)

        assert result.success is True
        assert result.data["funding_rounds"][0]["round_type"] == "seed"

    @pytest.mark.asyncio
    async def test_execute_with_error(self, funding_service, mock_provider, valid_request):
        """Testa execução com erro."""
        mock_provider.search.side_effect = Exception("Provider error")

        result = await funding_service.execute(valid_request)

        assert result.success is False
        assert result.error == "Provider error"
        assert result.cost == Decimal("0")  # Não cobra em caso de erro

    @pytest.mark.asyncio
    async def test_execute_with_invalid_json(self, funding_service, mock_provider, valid_request):
        """Testa execução com JSON inválido."""
        mock_provider.search.return_value = "Invalid JSON"

        result = await funding_service.execute(valid_request)

        assert result.success is True
        assert result.data is not None
        # Deve retornar estrutura vazia padrão
        assert result.data["funding_rounds"] == []
        assert result.data["data_gaps"] == ["Dados de funding não encontrados"]

    @pytest.mark.asyncio
    async def test_execute_with_partial_data(self, funding_service, mock_provider, valid_request):
        """Testa execução com dados parciais."""
        partial_response = {
            "funding_rounds": [
                {
                    "round_type": "seed",
                    "amount_usd": 1000000,
                    "date": "2023-06"
                }
            ],
            "metrics": {
                "total_raised_usd": 1000000
            }
            # Faltam analysis, data_sources, data_gaps
        }
        mock_provider.search.return_value = partial_response

        result = await funding_service.execute(valid_request)

        assert result.success is True
        # Campos faltantes devem ter valores padrão
        assert "analysis" in result.data
        assert "data_sources" in result.data
        assert "data_gaps" in result.data

    @pytest.mark.asyncio
    async def test_confidence_score_calculation(self, funding_service, mock_provider, valid_request, sample_provider_response):
        """Testa cálculo do score de confiança."""
        mock_provider.search.return_value = sample_provider_response

        result = await funding_service.execute(valid_request)

        assert result.confidence_score is not None
        assert 0 <= result.confidence_score <= 1
        # Com dados completos, score deve ser alto
        assert result.confidence_score > 0.8

    @pytest.mark.asyncio
    async def test_confidence_score_with_gaps(self, funding_service, mock_provider, valid_request):
        """Testa score de confiança com gaps de dados."""
        response_with_gaps = {
            "funding_rounds": [],
            "metrics": {},
            "analysis": {},
            "data_sources": [],
            "data_gaps": ["No funding data found", "No investor information"]
        }
        mock_provider.search.return_value = response_with_gaps

        result = await funding_service.execute(valid_request)

        # Com muitos gaps, score deve ser baixo
        assert result.confidence_score < 0.5

    def test_sample_output_structure(self, funding_service):
        """Testa estrutura do exemplo de saída."""
        sample = funding_service.get_sample_output()

        assert "funding_rounds" in sample
        assert "key_investors" in sample
        assert "metrics" in sample
        assert "analysis" in sample
        assert "market_context" in sample
        assert "data_sources" in sample
        assert "data_gaps" in sample

        # Verifica estrutura dos rounds
        assert len(sample["funding_rounds"]) > 0
        round_example = sample["funding_rounds"][0]
        assert "round_type" in round_example
        assert "amount_usd" in round_example
        assert "date" in round_example
        assert "lead_investors" in round_example

    @pytest.mark.asyncio
    async def test_currency_options(self, funding_service, mock_provider, valid_request):
        """Testa opções de moeda."""
        # Testa com USD apenas
        valid_request.additional_context["target_currency"] = "usd"
        mock_provider.search.return_value = {"funding_rounds": [], "metrics": {
        }, "analysis": {}, "data_sources": [], "data_gaps": []}

        await funding_service.execute(valid_request)

        # Verifica se o prompt foi construído corretamente
        call_args = mock_provider.search.call_args
        user_prompt = call_args.kwargs["user_prompt"]
        assert "valores em USD" in user_prompt

    @pytest.mark.asyncio
    async def test_round_type_normalization(self, funding_service, mock_provider, valid_request):
        """Testa normalização de tipos de round."""
        response_with_variations = {
            "funding_rounds": [
                {"round_type": "Serie A", "amount_usd": 1000000},
                {"round_type": "pre-seed", "amount_usd": 500000},
                {"round_type": "SEED", "amount_usd": 750000}
            ],
            "metrics": {},
            "analysis": {},
            "data_sources": [],
            "data_gaps": []
        }
        mock_provider.search.return_value = response_with_variations

        result = await funding_service.execute(valid_request)

        # Verifica normalização
        rounds = result.data["funding_rounds"]
        assert rounds[0]["round_type"] == "series_a"
        assert rounds[1]["round_type"] == "pre_seed"
        assert rounds[2]["round_type"] == "seed"

    @pytest.mark.asyncio
    async def test_processing_time(self, funding_service, mock_provider, valid_request, sample_provider_response):
        """Testa registro do tempo de processamento."""
        mock_provider.search.return_value = sample_provider_response

        result = await funding_service.execute(valid_request)

        assert result.processing_time_seconds >= 0
        assert isinstance(result.processing_time_seconds, float)

    @pytest.mark.asyncio
    async def test_exclude_competitors(self, funding_service, mock_provider):
        """Testa exclusão de análise de competidores."""
        request = ResearchRequest(
            client_id="test_123",
            company_url="https://example.com",
            company_name="Example Tech",
            additional_context={
                "include_competitor_comparison": False,
                "include_investor_analysis": True
            }
        )
        mock_provider.search.return_value = {"funding_rounds": [], "metrics": {
        }, "analysis": {}, "data_sources": [], "data_gaps": []}

        await funding_service.execute(request)

        # Verifica se o prompt foi construído sem seção de competidores
        call_args = mock_provider.search.call_args
        user_prompt = call_args.kwargs["user_prompt"]
        assert "funding_vs_competitors" not in user_prompt

    @pytest.mark.asyncio
    async def test_exclude_investors(self, funding_service, mock_provider):
        """Testa exclusão de análise de investidores."""
        request = ResearchRequest(
            client_id="test_123",
            company_url="https://example.com",
            company_name="Example Tech",
            additional_context={
                "include_competitor_comparison": True,
                "include_investor_analysis": False
            }
        )
        mock_provider.search.return_value = {"funding_rounds": [], "metrics": {
        }, "analysis": {}, "data_sources": [], "data_gaps": []}

        await funding_service.execute(request)

        # Verifica se o prompt foi construído sem seção de investidores
        call_args = mock_provider.search.call_args
        user_prompt = call_args.kwargs["user_prompt"]
        assert "key_investors" not in user_prompt
