"""
Interfaces e tipos para serviços de diagnóstico técnico
"""

from typing import Dict, Any, List, Optional, TypedDict, Literal
from datetime import datetime
from bson import ObjectId


class ViewportConfig(TypedDict):
    """Configuração de viewport para screenshots"""
    width: int
    height: int


class ScreenshotMetadata(TypedDict):
    """Metadata de screenshot"""
    timestamp: datetime
    url: str
    browser_type: str
    viewport: ViewportConfig
    capture_settings: Dict[str, Any]


class ScreenshotData(TypedDict):
    """Dados de screenshot armazenados"""
    filename: str
    grid_fs_id: ObjectId
    metadata: ScreenshotMetadata
    size_bytes: int
    mime_type: str


class LighthouseMetrics(TypedDict):
    """Métricas Core Web Vitals do Lighthouse"""
    fcp: int  # First Contentful Paint (ms)
    lcp: int  # Largest Contentful Paint (ms)
    cls: float  # Cumulative Layout Shift
    fid: int  # First Input Delay (ms)
    ttfb: int  # Time to First Byte (ms)
    speed_index: Optional[int]
    time_to_interactive: Optional[int]
    total_blocking_time: Optional[int]


class LighthouseCategory(TypedDict):
    """Categoria de análise do Lighthouse"""
    score: int  # 0-100
    issues: Optional[List[str]]
    recommendations: Optional[List[str]]


class LighthouseData(TypedDict):
    """Dados completos da análise Lighthouse"""
    performance: Dict[str, Any]
    accessibility: Dict[str, Any]
    seo_tecnico: Dict[str, Any]
    best_practices: Dict[str, Any]
    _real_data: bool
    _lighthouse_version: str


class VisualAnalysisResult(TypedDict):
    """Resultado da análise visual por IA"""
    layout_quality: Literal["excelente", "boa", "regular", "ruim"]
    user_experience: str
    visual_hierarchy: str
    responsive_design: str
    recomendacoes_design: List[str]
    _metadata: Optional[Dict[str, Any]]


class DiagnosticResult(TypedDict):
    """Resultado completo do diagnóstico técnico"""
    _id: Optional[ObjectId]
    client_id: ObjectId
    url: str
    timestamp: datetime

    # Dados do Lighthouse
    lighthouse_data: LighthouseData

    # Screenshots
    screenshots: Dict[str, ScreenshotData]

    # Análise visual
    visual_analysis: VisualAnalysisResult

    # Relatório consolidado
    consolidated_report: Dict[str, Any]

    # Status e metadata
    status: Literal["processing", "completed", "error"]
    error_message: Optional[str]
    processing_time_ms: Optional[int]


class DiagnosticRequest(TypedDict):
    """Requisição de diagnóstico técnico"""
    client_id: str
    url: str
    company_name: str
    force_refresh: Optional[bool]
    include_screenshots: Optional[bool]
    browser_types: Optional[List[str]]
