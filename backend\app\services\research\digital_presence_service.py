"""
Digital Presence Analysis Service

This service analyzes a company's digital presence including SEO metrics,
social media engagement, website performance, content strategy, and online reputation.

Cost: $0.006 per analysis
"""

from typing import Dict, Any, Optional, List
from datetime import datetime, UTC
from decimal import Decimal
import logging
import json

from ...core.interfaces import IResearchService, IResearchProvider, ResearchRequest, ResearchResult
from ...core.exceptions import ResearchException

logger = logging.getLogger(__name__)


class DigitalPresenceAnalysis:
    """Represents the result of a digital presence analysis"""

    def __init__(
        self,
        seo_metrics: Dict[str, Any],
        social_media: Dict[str, Any],
        website_analysis: Dict[str, Any],
        content_strategy: Dict[str, Any],
        online_reputation: Dict[str, Any],
        digital_maturity_score: float,
        recommendations: List[str],
        confidence_score: float,
        raw_data: Optional[Dict[str, Any]] = None
    ):
        self.seo_metrics = seo_metrics
        self.social_media = social_media
        self.website_analysis = website_analysis
        self.content_strategy = content_strategy
        self.online_reputation = online_reputation
        self.digital_maturity_score = digital_maturity_score
        self.recommendations = recommendations
        self.confidence_score = confidence_score
        self.raw_data = raw_data or {}
        self.timestamp = datetime.now(UTC)
        self.service_name = "digital_presence"
        self.cost = 0.006

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format"""
        return {
            "service_name": self.service_name,
            "timestamp": self.timestamp.isoformat(),
            "cost": self.cost,
            "confidence_score": self.confidence_score,
            "digital_maturity_score": self.digital_maturity_score,
            "seo_metrics": self.seo_metrics,
            "social_media": self.social_media,
            "website_analysis": self.website_analysis,
            "content_strategy": self.content_strategy,
            "online_reputation": self.online_reputation,
            "recommendations": self.recommendations,
            "raw_data": self.raw_data
        }


class DigitalPresenceService(IResearchService):
    """
    Service for analyzing company's digital presence

    Analyzes:
    - SEO performance and rankings
    - Social media presence and engagement
    - Website performance and user experience
    - Content strategy and effectiveness
    - Online reputation and brand sentiment
    """

    def __init__(self, provider: Optional[IResearchProvider] = None):
        """
        Initialize the service

        Args:
            provider: Research provider for data fetching (optional)
        """
        self.provider = provider
        self.cost_per_analysis = 0.006
        self.service_name = "digital_presence"
        self.version = "1.0.0"

    # Implementação dos métodos abstratos da IResearchService
    def get_name(self) -> str:
        """Retorna o nome único do serviço."""
        return self.service_name

    def get_version(self) -> str:
        """Retorna a versão do serviço."""
        return self.version

    def get_description(self) -> str:
        """Retorna uma descrição do que o serviço faz."""
        return (
            "Analisa a presença digital completa de uma empresa incluindo SEO, "
            "redes sociais, performance do website, estratégia de conteúdo e reputação online. "
            "Fornece score de maturidade digital e recomendações acionáveis."
        )

    def get_cost(self) -> Decimal:
        """Retorna o custo estimado em USD para executar este serviço."""
        return Decimal(str(self.cost_per_analysis))

    def get_required_fields(self) -> List[str]:
        """Retorna lista de campos obrigatórios no ResearchRequest."""
        return ["company_name"]

    def get_optional_fields(self) -> List[str]:
        """Retorna lista de campos opcionais que o serviço pode usar."""
        return ["company_url", "additional_context"]

    async def validate_request(self, request: ResearchRequest) -> bool:
        """
        Valida se a requisição tem todos os dados necessários.

        Args:
            request: Requisição de pesquisa

        Returns:
            True se válida, False caso contrário
        """
        # Verifica se tem o campo obrigatório
        if not request.company_name or request.company_name.strip() == "":
            logger.error("Company name is required")
            return False

        return True

    async def execute(self, request: ResearchRequest) -> ResearchResult:
        """
        Executa a pesquisa e retorna o resultado.

        Args:
            request: Requisição de pesquisa com dados do cliente

        Returns:
            ResearchResult com os dados coletados ou erro
        """
        start_time = datetime.now(UTC)

        try:
            # Valida a requisição
            if not await self.validate_request(request):
                return ResearchResult(
                    service_name=self.get_name(),
                    service_version=self.get_version(),
                    timestamp=datetime.now(UTC),
                    cost=Decimal("0"),
                    success=False,
                    error="Invalid request: company_name is required",
                    processing_time_seconds=0
                )

            # Prepara o contexto
            context = {
                "website": request.company_url
            }
            if request.additional_context:
                context.update(request.additional_context)

            # Executa a análise
            analysis = await self.analyze(request.company_name, context)

            # Calcula tempo de processamento
            processing_time = (datetime.now(UTC) - start_time).total_seconds()

            # Retorna resultado de sucesso
            return ResearchResult(
                service_name=self.get_name(),
                service_version=self.get_version(),
                timestamp=analysis.timestamp,
                cost=self.get_cost(),
                success=True,
                data=analysis.to_dict(),
                processing_time_seconds=processing_time,
                confidence_score=analysis.confidence_score / 100  # Converte para 0-1
            )

        except Exception as e:
            logger.error(
                f"Error executing digital presence analysis: {str(e)}")
            processing_time = (datetime.now(UTC) - start_time).total_seconds()

            return ResearchResult(
                service_name=self.get_name(),
                service_version=self.get_version(),
                timestamp=datetime.now(UTC),
                cost=Decimal("0"),
                success=False,
                error=str(e),
                processing_time_seconds=processing_time
            )

    def get_sample_output(self) -> Dict[str, Any]:
        """
        Retorna um exemplo do formato de saída esperado.

        Útil para documentação e testes.
        """
        return {
            "service_name": "digital_presence",
            "timestamp": "2025-01-30T12:00:00Z",
            "cost": 0.006,
            "confidence_score": 85.5,
            "digital_maturity_score": 72.5,
            "seo_metrics": {
                "domain_authority": 45,
                "organic_traffic": {
                    "monthly_estimate": 25000,
                    "growth_trend": "increasing"
                },
                "keywords": {
                    "ranking_keywords": 1250,
                    "top_keywords": ["software empresarial", "gestão empresas"]
                },
                "backlinks": {
                    "total_backlinks": 5420,
                    "referring_domains": 342
                },
                "technical_seo": {
                    "mobile_friendly": True,
                    "page_speed_score": 78
                }
            },
            "social_media": {
                "platforms": {
                    "linkedin": {
                        "active": True,
                        "followers": 15420,
                        "engagement_rate": 3.2
                    }
                },
                "total_reach": 59300,
                "most_active_platform": "linkedin"
            },
            "website_analysis": {
                "performance": {
                    "page_load_time": 2.3,
                    "mobile_score": 85
                },
                "user_experience": {
                    "bounce_rate": 42,
                    "conversion_rate": 2.8
                }
            },
            "content_strategy": {
                "blog": {
                    "active": True,
                    "post_frequency": "weekly"
                },
                "content_types": {
                    "articles": True,
                    "videos": True
                }
            },
            "online_reputation": {
                "reviews": {
                    "google_rating": 4.3,
                    "google_reviews_count": 127
                },
                "brand_mentions": {
                    "total_mentions": 342,
                    "sentiment_distribution": {
                        "positive": 205,
                        "neutral": 98,
                        "negative": 39
                    }
                }
            },
            "recommendations": [
                "Investir em link building de qualidade",
                "Desenvolver estratégia de conteúdo para aumentar engajamento",
                "Otimizar velocidade do site"
            ]
        }

    async def analyze(
        self,
        company_name: str,
        context: Optional[Dict[str, Any]] = None
    ) -> DigitalPresenceAnalysis:
        """
        Analyze company's digital presence

        Args:
            company_name: Name of the company to analyze
            context: Additional context (website URL, social handles, etc.)

        Returns:
            DigitalPresenceAnalysis object with comprehensive digital metrics
        """
        logger.info(f"Starting digital presence analysis for {company_name}")

        try:
            # AIDEV-NOTE: Mock implementation - replace with actual provider call
            if self.provider:
                query = self._build_analysis_query(company_name, context)
                raw_data = await self.provider.search(query, max_tokens=2000)
            else:
                raw_data = self._get_mock_data(company_name)

            # Parse and structure the analysis
            seo_metrics = self._analyze_seo_metrics(raw_data, context)
            social_media = self._analyze_social_media(raw_data, context)
            website_analysis = self._analyze_website(raw_data, context)
            content_strategy = self._analyze_content_strategy(raw_data)
            online_reputation = self._analyze_online_reputation(raw_data)

            # Calculate digital maturity score
            digital_maturity_score = self._calculate_digital_maturity(
                seo_metrics, social_media, website_analysis,
                content_strategy, online_reputation
            )

            # Generate recommendations
            recommendations = self._generate_recommendations(
                seo_metrics, social_media, website_analysis,
                content_strategy, online_reputation
            )

            # Calculate confidence score
            confidence_score = self._calculate_confidence_score(
                seo_metrics, social_media, website_analysis,
                content_strategy, online_reputation
            )

            return DigitalPresenceAnalysis(
                seo_metrics=seo_metrics,
                social_media=social_media,
                website_analysis=website_analysis,
                content_strategy=content_strategy,
                online_reputation=online_reputation,
                digital_maturity_score=digital_maturity_score,
                recommendations=recommendations,
                confidence_score=confidence_score,
                raw_data=raw_data
            )

        except Exception as e:
            logger.error(f"Error analyzing digital presence: {str(e)}")
            raise ResearchException(
                f"Failed to analyze digital presence: {str(e)}")

    def _build_analysis_query(self, company_name: str, context: Optional[Dict[str, Any]]) -> str:
        """Build query for digital presence analysis"""
        website = context.get('website', '') if context else ''

        return f"""
        Analyze the digital presence of {company_name} {f'(website: {website})' if website else ''}:
        
        1. SEO Performance:
           - Domain authority and ranking
           - Organic traffic estimates
           - Key ranking keywords
           - Backlink profile
           - Technical SEO health
        
        2. Social Media Presence:
           - Active platforms (LinkedIn, Twitter, Facebook, Instagram, etc.)
           - Follower counts and growth
           - Engagement rates
           - Content frequency
           - Community management
        
        3. Website Analysis:
           - Performance metrics (speed, mobile-friendliness)
           - User experience indicators
           - Conversion optimization
           - Content quality
           - Technical infrastructure
        
        4. Content Strategy:
           - Blog/resource center activity
           - Content types and formats
           - Publishing frequency
           - Topic coverage
           - SEO optimization
        
        5. Online Reputation:
           - Review platforms presence
           - Average ratings
           - Review volume and recency
           - Brand mentions
           - Sentiment analysis
        
        Provide specific metrics and data points where available.
        """

    def _analyze_seo_metrics(self, data: Dict[str, Any], context: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Extract and analyze SEO metrics"""
        # AIDEV-NOTE: Structured SEO analysis with fallbacks
        return {
            "domain_authority": data.get("domain_authority", 0),
            "organic_traffic": {
                "monthly_estimate": data.get("organic_traffic", 0),
                "growth_trend": data.get("traffic_growth", "stable"),
                "traffic_sources": data.get("traffic_sources", {})
            },
            "keywords": {
                "ranking_keywords": data.get("ranking_keywords", 0),
                "top_keywords": data.get("top_keywords", []),
                "branded_vs_non_branded": data.get("keyword_split", {"branded": 30, "non_branded": 70})
            },
            "backlinks": {
                "total_backlinks": data.get("total_backlinks", 0),
                "referring_domains": data.get("referring_domains", 0),
                "quality_score": data.get("backlink_quality", 0)
            },
            "technical_seo": {
                "mobile_friendly": data.get("mobile_friendly", True),
                "page_speed_score": data.get("page_speed", 0),
                "https_enabled": data.get("https", True),
                "xml_sitemap": data.get("has_sitemap", True),
                "robots_txt": data.get("has_robots", True)
            }
        }

    def _analyze_social_media(self, data: Dict[str, Any], context: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Extract and analyze social media presence"""
        platforms = {}

        # AIDEV-NOTE: Analyze major social platforms
        social_platforms = ["linkedin", "twitter",
                            "facebook", "instagram", "youtube", "tiktok"]

        for platform in social_platforms:
            platform_data = data.get(f"{platform}_data", {})
            if platform_data:
                platforms[platform] = {
                    "active": True,
                    "followers": platform_data.get("followers", 0),
                    "engagement_rate": platform_data.get("engagement_rate", 0),
                    "post_frequency": platform_data.get("post_frequency", "unknown"),
                    "last_post": platform_data.get("last_post", "unknown"),
                    "growth_rate": platform_data.get("growth_rate", 0)
                }

        return {
            "platforms": platforms,
            "total_reach": sum(p.get("followers", 0) for p in platforms.values()),
            "most_active_platform": max(platforms.keys(), key=lambda x: platforms[x].get("followers", 0)) if platforms else "none",
            "average_engagement": sum(p.get("engagement_rate", 0) for p in platforms.values()) / len(platforms) if platforms else 0,
            "social_selling_index": data.get("social_selling_index", 0),
            "influencer_score": data.get("influencer_score", 0)
        }

    def _analyze_website(self, data: Dict[str, Any], context: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Extract and analyze website performance"""
        # AIDEV-NOTE: Website analysis with performance metrics
        return {
            "performance": {
                "page_load_time": data.get("page_load_time", 0),
                "mobile_score": data.get("mobile_score", 0),
                "desktop_score": data.get("desktop_score", 0),
                "core_web_vitals": {
                    "lcp": data.get("lcp", 0),  # Largest Contentful Paint
                    "fid": data.get("fid", 0),  # First Input Delay
                    "cls": data.get("cls", 0)   # Cumulative Layout Shift
                }
            },
            "user_experience": {
                "bounce_rate": data.get("bounce_rate", 0),
                "average_session_duration": data.get("session_duration", 0),
                "pages_per_session": data.get("pages_per_session", 0),
                "conversion_rate": data.get("conversion_rate", 0)
            },
            "technology": {
                "cms": data.get("cms", "unknown"),
                "hosting": data.get("hosting", "unknown"),
                "cdn": data.get("cdn", "none"),
                "analytics_tools": data.get("analytics_tools", []),
                "marketing_tools": data.get("marketing_tools", [])
            },
            "content": {
                "total_pages": data.get("total_pages", 0),
                "blog_posts": data.get("blog_posts", 0),
                "multimedia_content": data.get("multimedia_content", False),
                "languages": data.get("languages", ["pt-BR"])
            }
        }

    def _analyze_content_strategy(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract and analyze content strategy"""
        return {
            "blog": {
                "active": data.get("has_blog", False),
                "post_frequency": data.get("blog_frequency", "none"),
                "total_posts": data.get("total_blog_posts", 0),
                "categories": data.get("blog_categories", []),
                "average_length": data.get("average_post_length", 0)
            },
            "content_types": {
                "articles": data.get("has_articles", False),
                "videos": data.get("has_videos", False),
                "podcasts": data.get("has_podcasts", False),
                "infographics": data.get("has_infographics", False),
                "ebooks": data.get("has_ebooks", False),
                "webinars": data.get("has_webinars", False)
            },
            "content_quality": {
                "originality_score": data.get("originality_score", 0),
                "readability_score": data.get("readability_score", 0),
                "seo_optimization": data.get("content_seo_score", 0),
                "multimedia_usage": data.get("multimedia_score", 0)
            },
            "distribution": {
                "email_marketing": data.get("has_newsletter", False),
                "social_sharing": data.get("social_sharing_enabled", True),
                "content_syndication": data.get("content_syndication", False)
            }
        }

    def _analyze_online_reputation(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract and analyze online reputation"""
        return {
            "reviews": {
                "google_rating": data.get("google_rating", 0),
                "google_reviews_count": data.get("google_reviews", 0),
                "glassdoor_rating": data.get("glassdoor_rating", 0),
                "trustpilot_rating": data.get("trustpilot_rating", 0),
                "reclame_aqui_rating": data.get("reclame_aqui_rating", 0)
            },
            "brand_mentions": {
                "total_mentions": data.get("brand_mentions", 0),
                "sentiment_distribution": {
                    "positive": data.get("positive_mentions", 0),
                    "neutral": data.get("neutral_mentions", 0),
                    "negative": data.get("negative_mentions", 0)
                },
                "mention_sources": data.get("mention_sources", [])
            },
            "crisis_indicators": {
                "recent_controversies": data.get("controversies", []),
                "complaint_trends": data.get("complaint_trends", "stable"),
                "response_rate": data.get("response_rate", 0)
            },
            "thought_leadership": {
                "industry_mentions": data.get("industry_mentions", 0),
                "media_features": data.get("media_features", []),
                "awards": data.get("awards", [])
            }
        }

    def _calculate_digital_maturity(
        self,
        seo: Dict[str, Any],
        social: Dict[str, Any],
        website: Dict[str, Any],
        content: Dict[str, Any],
        reputation: Dict[str, Any]
    ) -> float:
        """Calculate overall digital maturity score (0-100)"""
        # AIDEV-NOTE: Weighted scoring for digital maturity
        scores = {
            "seo": self._score_seo_maturity(seo) * 0.25,
            "social": self._score_social_maturity(social) * 0.20,
            "website": self._score_website_maturity(website) * 0.20,
            "content": self._score_content_maturity(content) * 0.20,
            "reputation": self._score_reputation_maturity(reputation) * 0.15
        }

        return round(sum(scores.values()), 1)

    def _score_seo_maturity(self, seo: Dict[str, Any]) -> float:
        """Score SEO maturity (0-100)"""
        score = 0

        # Domain authority (0-30 points)
        da = seo.get("domain_authority", 0)
        score += min(da * 0.3, 30)

        # Technical SEO (0-25 points)
        tech = seo.get("technical_seo", {})
        if tech.get("mobile_friendly", False):
            score += 5
        if tech.get("https_enabled", False):
            score += 5
        score += min(tech.get("page_speed_score", 0) * 0.15, 15)

        # Keyword performance (0-25 points)
        keywords = seo.get("keywords", {})
        ranking_keywords = keywords.get("ranking_keywords", 0)
        score += min(ranking_keywords * 0.025, 25)

        # Backlinks (0-20 points)
        backlinks = seo.get("backlinks", {})
        referring_domains = backlinks.get("referring_domains", 0)
        score += min(referring_domains * 0.02, 20)

        return min(score, 100)

    def _score_social_maturity(self, social: Dict[str, Any]) -> float:
        """Score social media maturity (0-100)"""
        score = 0
        platforms = social.get("platforms", {})

        # Platform presence (0-30 points)
        active_platforms = sum(
            1 for p in platforms.values() if p.get("active", False))
        score += min(active_platforms * 5, 30)

        # Total reach (0-30 points)
        total_reach = social.get("total_reach", 0)
        if total_reach > 100000:
            score += 30
        elif total_reach > 50000:
            score += 25
        elif total_reach > 10000:
            score += 20
        elif total_reach > 5000:
            score += 15
        elif total_reach > 1000:
            score += 10
        elif total_reach > 0:
            score += 5

        # Engagement (0-25 points)
        avg_engagement = social.get("average_engagement", 0)
        score += min(avg_engagement * 5, 25)

        # Social selling (0-15 points)
        score += min(social.get("social_selling_index", 0) * 0.15, 15)

        return min(score, 100)

    def _score_website_maturity(self, website: Dict[str, Any]) -> float:
        """Score website maturity (0-100)"""
        score = 0

        # Performance (0-35 points)
        perf = website.get("performance", {})
        score += min(perf.get("mobile_score", 0) * 0.2, 20)
        score += min(perf.get("desktop_score", 0) * 0.15, 15)

        # User experience (0-35 points)
        ux = website.get("user_experience", {})
        if ux.get("bounce_rate", 100) < 50:
            score += 10
        if ux.get("pages_per_session", 0) > 2:
            score += 10
        if ux.get("conversion_rate", 0) > 2:
            score += 15

        # Technology stack (0-30 points)
        tech = website.get("technology", {})
        if tech.get("cdn", "none") != "none":
            score += 10
        if len(tech.get("analytics_tools", [])) > 0:
            score += 10
        if len(tech.get("marketing_tools", [])) > 0:
            score += 10

        return min(score, 100)

    def _score_content_maturity(self, content: Dict[str, Any]) -> float:
        """Score content maturity (0-100)"""
        score = 0

        # Blog activity (0-30 points)
        blog = content.get("blog", {})
        if blog.get("active", False):
            score += 10
            if blog.get("post_frequency", "none") in ["weekly", "bi-weekly"]:
                score += 20
            elif blog.get("post_frequency", "none") == "monthly":
                score += 15
            elif blog.get("post_frequency", "none") == "quarterly":
                score += 10

        # Content diversity (0-30 points)
        types = content.get("content_types", {})
        content_types = sum(1 for t in types.values() if t)
        score += min(content_types * 5, 30)

        # Content quality (0-25 points)
        quality = content.get("content_quality", {})
        score += min(quality.get("originality_score", 0) * 0.125, 12.5)
        score += min(quality.get("seo_optimization", 0) * 0.125, 12.5)

        # Distribution (0-15 points)
        dist = content.get("distribution", {})
        if dist.get("email_marketing", False):
            score += 7.5
        if dist.get("content_syndication", False):
            score += 7.5

        return min(score, 100)

    def _score_reputation_maturity(self, reputation: Dict[str, Any]) -> float:
        """Score reputation maturity (0-100)"""
        score = 0

        # Review ratings (0-40 points)
        reviews = reputation.get("reviews", {})
        avg_rating = 0
        rating_count = 0
        for platform, rating in reviews.items():
            if rating > 0:
                avg_rating += rating
                rating_count += 1

        if rating_count > 0:
            avg_rating = avg_rating / rating_count
            score += min(avg_rating * 8, 40)

        # Brand sentiment (0-30 points)
        mentions = reputation.get("brand_mentions", {})
        sentiment = mentions.get("sentiment_distribution", {})
        positive_ratio = sentiment.get(
            "positive", 0) / max(sum(sentiment.values()), 1)
        score += min(positive_ratio * 30, 30)

        # Crisis management (0-15 points)
        crisis = reputation.get("crisis_indicators", {})
        if crisis.get("response_rate", 0) > 80:
            score += 15
        elif crisis.get("response_rate", 0) > 50:
            score += 10
        elif crisis.get("response_rate", 0) > 20:
            score += 5

        # Thought leadership (0-15 points)
        leadership = reputation.get("thought_leadership", {})
        if leadership.get("industry_mentions", 0) > 10:
            score += 7.5
        if len(leadership.get("awards", [])) > 0:
            score += 7.5

        return min(score, 100)

    def _generate_recommendations(
        self,
        seo: Dict[str, Any],
        social: Dict[str, Any],
        website: Dict[str, Any],
        content: Dict[str, Any],
        reputation: Dict[str, Any]
    ) -> List[str]:
        """Generate actionable recommendations based on analysis"""
        recommendations = []

        # SEO recommendations
        if seo.get("domain_authority", 0) < 30:
            recommendations.append(
                "Investir em link building de qualidade para aumentar autoridade do domínio")
        if seo.get("technical_seo", {}).get("page_speed_score", 0) < 70:
            recommendations.append(
                "Otimizar velocidade do site para melhorar experiência e SEO")

        # Social media recommendations
        platforms = social.get("platforms", {})
        if len(platforms) < 3:
            recommendations.append(
                "Expandir presença para mais plataformas sociais relevantes ao público")
        if social.get("average_engagement", 0) < 2:
            recommendations.append(
                "Desenvolver estratégia de conteúdo para aumentar engajamento social")

        # Website recommendations
        ux = website.get("user_experience", {})
        if ux.get("bounce_rate", 100) > 60:
            recommendations.append(
                "Melhorar conteúdo e UX da landing page para reduzir taxa de rejeição")
        if ux.get("conversion_rate", 0) < 2:
            recommendations.append(
                "Implementar otimizações de CRO para aumentar conversões")

        # Content recommendations
        blog = content.get("blog", {})
        if not blog.get("active", False):
            recommendations.append(
                "Criar blog corporativo para educação de mercado e SEO")
        elif blog.get("post_frequency", "none") not in ["weekly", "bi-weekly", "monthly"]:
            recommendations.append(
                "Estabelecer calendário editorial consistente para o blog")

        # Reputation recommendations
        reviews = reputation.get("reviews", {})
        if reviews.get("google_rating", 0) < 4.5:
            recommendations.append(
                "Implementar programa de gestão de reputação para melhorar avaliações")

        # Digital maturity recommendations
        if len(content.get("content_types", {})) < 6:
            recommendations.append(
                "Diversificar tipos de conteúdo (vídeos, podcasts, webinars) para alcançar diferentes públicos")

        # Ensure at least one recommendation
        if not recommendations:
            recommendations.append(
                "Continuar monitorando métricas de presença digital para identificar oportunidades de melhoria")

        # Limit to top 5 most important recommendations
        return recommendations[:5]

    def _calculate_confidence_score(
        self,
        seo: Dict[str, Any],
        social: Dict[str, Any],
        website: Dict[str, Any],
        content: Dict[str, Any],
        reputation: Dict[str, Any]
    ) -> float:
        """Calculate confidence score based on data completeness"""
        data_points = 0
        total_points = 0

        # Check SEO data completeness
        total_points += 5
        if seo.get("domain_authority", 0) > 0:
            data_points += 1
        if seo.get("keywords", {}).get("ranking_keywords", 0) > 0:
            data_points += 1
        if seo.get("backlinks", {}).get("total_backlinks", 0) > 0:
            data_points += 1
        if seo.get("technical_seo", {}).get("page_speed_score", 0) > 0:
            data_points += 1
        if seo.get("organic_traffic", {}).get("monthly_estimate", 0) > 0:
            data_points += 1

        # Check social media data
        total_points += 3
        platforms = social.get("platforms", {})
        if len(platforms) > 0:
            data_points += 1
        if social.get("total_reach", 0) > 0:
            data_points += 1
        if social.get("average_engagement", 0) > 0:
            data_points += 1

        # Check website data
        total_points += 3
        if website.get("performance", {}).get("page_load_time", 0) > 0:
            data_points += 1
        if website.get("user_experience", {}).get("bounce_rate", 0) > 0:
            data_points += 1
        if website.get("technology", {}).get("cms", "unknown") != "unknown":
            data_points += 1

        # Check content data
        total_points += 2
        if content.get("blog", {}).get("total_posts", 0) > 0:
            data_points += 1
        if any(content.get("content_types", {}).values()):
            data_points += 1

        # Check reputation data
        total_points += 2
        if any(r > 0 for r in reputation.get("reviews", {}).values()):
            data_points += 1
        if reputation.get("brand_mentions", {}).get("total_mentions", 0) > 0:
            data_points += 1

        # Calculate percentage
        confidence = (data_points / total_points) * \
            100 if total_points > 0 else 0
        return round(confidence, 1)

    def _get_mock_data(self, company_name: str) -> Dict[str, Any]:
        """Get mock data for testing"""
        # AIDEV-NOTE: Mock data for development/testing
        return {
            "domain_authority": 45,
            "organic_traffic": 25000,
            "traffic_growth": "increasing",
            "ranking_keywords": 1250,
            "top_keywords": ["software empresarial", "gestão empresas", "ERP cloud"],
            "total_backlinks": 5420,
            "referring_domains": 342,
            "backlink_quality": 72,
            "mobile_friendly": True,
            "page_speed": 78,
            "https": True,
            "has_sitemap": True,
            "has_robots": True,
            "linkedin_data": {
                "followers": 15420,
                "engagement_rate": 3.2,
                "post_frequency": "3x per week",
                "growth_rate": 12
            },
            "twitter_data": {
                "followers": 8930,
                "engagement_rate": 2.1,
                "post_frequency": "daily",
                "growth_rate": 8
            },
            "facebook_data": {
                "followers": 22150,
                "engagement_rate": 1.8,
                "post_frequency": "2x per week",
                "growth_rate": 5
            },
            "instagram_data": {
                "followers": 12800,
                "engagement_rate": 4.5,
                "post_frequency": "3x per week",
                "growth_rate": 15
            },
            "page_load_time": 2.3,
            "mobile_score": 85,
            "desktop_score": 92,
            "lcp": 2.1,
            "fid": 95,
            "cls": 0.05,
            "bounce_rate": 42,
            "session_duration": 185,
            "pages_per_session": 3.2,
            "conversion_rate": 2.8,
            "cms": "WordPress",
            "hosting": "AWS",
            "cdn": "CloudFlare",
            "analytics_tools": ["Google Analytics", "Hotjar"],
            "marketing_tools": ["HubSpot", "Mailchimp"],
            "total_pages": 145,
            "blog_posts": 67,
            "has_blog": True,
            "blog_frequency": "weekly",
            "total_blog_posts": 67,
            "blog_categories": ["Tecnologia", "Gestão", "Inovação", "Cases"],
            "average_post_length": 1200,
            "has_articles": True,
            "has_videos": True,
            "has_podcasts": False,
            "has_infographics": True,
            "has_ebooks": True,
            "has_webinars": True,
            "originality_score": 82,
            "readability_score": 75,
            "content_seo_score": 78,
            "multimedia_score": 65,
            "has_newsletter": True,
            "social_sharing_enabled": True,
            "content_syndication": False,
            "google_rating": 4.3,
            "google_reviews": 127,
            "glassdoor_rating": 3.9,
            "trustpilot_rating": 4.1,
            "reclame_aqui_rating": 7.8,
            "brand_mentions": 342,
            "positive_mentions": 205,
            "neutral_mentions": 98,
            "negative_mentions": 39,
            "mention_sources": ["News sites", "Blogs", "Forums", "Social media"],
            "controversies": [],
            "complaint_trends": "decreasing",
            "response_rate": 85,
            "industry_mentions": 28,
            "media_features": ["TechCrunch", "Exame", "Valor Econômico"],
            "awards": ["Top 100 Startups 2024", "Best B2B Software"],
            "social_selling_index": 72,
            "influencer_score": 65
        }


if __name__ == "__main__":
    # Example usage
    import asyncio

    async def main():
        service = DigitalPresenceService()

        # Test with mock data
        analysis = await service.analyze(
            "Empresa Exemplo",
            {"website": "https://exemplo.com.br"}
        )

        print(f"Digital Maturity Score: {analysis.digital_maturity_score}/100")
        print(f"Confidence Score: {analysis.confidence_score}%")
        print(f"Cost: ${analysis.cost}")
        print("\nRecommendations:")
        for i, rec in enumerate(analysis.recommendations, 1):
            print(f"{i}. {rec}")

    asyncio.run(main())
