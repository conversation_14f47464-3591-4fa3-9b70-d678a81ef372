"""
Testes unitários para o SWOT Analysis Service.

Testa a implementação do serviço de análise SWOT expandida incluindo:
- Validação de requisições
- Processamento de resultados
- Cálculo de confidence score
- Tratamento de erros
- Estrutura de dados retornada
"""
import pytest
import json
from unittest.mock import Mock, AsyncMock, patch
from decimal import Decimal
from datetime import datetime, UTC

from app.services.research.swot_analysis_service import SwotAnalysisService
from app.core import ResearchRequest, ResearchResult


@pytest.fixture
def mock_perplexity_provider():
    """Mock do provider Perplexity."""
    provider = Mock()
    provider.search = AsyncMock()
    return provider


@pytest.fixture
def swot_service(mock_perplexity_provider):
    """Instância do SwotAnalysisService com provider mockado."""
    return SwotAnalysisService(mock_perplexity_provider)


@pytest.fixture
def valid_request():
    """Requisição válida para testes."""
    return ResearchRequest(
        client_id="test_client_123",
        company_name="Tech Solutions Brasil",
        company_url="https://techsolutions.com.br",
        additional_context={
            "industry": "Tecnologia",
            "market_position": "Líder regional",
            "competitors": "TechCorp, InnovaSoft",
            "business_model": "SaaS B2B"
        }
    )


@pytest.fixture
def complete_swot_response():
    """Resposta completa de SWOT para testes."""
    return {
        "forcas": [
            {
                "item": "Tecnologia proprietária avançada",
                "descricao": "Plataforma desenvolvida internamente com algoritmos de IA únicos no mercado",
                "impacto": 9,
                "categoria": "tecnologia",
                "vantagem_competitiva": "Barreira tecnológica de 2-3 anos para competidores"
            },
            {
                "item": "Equipe técnica altamente qualificada",
                "descricao": "80% dos desenvolvedores com mestrado ou doutorado em áreas relevantes",
                "impacto": 8,
                "categoria": "pessoas",
                "vantagem_competitiva": "Capacidade de inovação contínua"
            },
            {
                "item": "Base de clientes fidelizada",
                "descricao": "Taxa de retenção de 95% ao ano com NPS 72",
                "impacto": 8,
                "categoria": "comercial",
                "vantagem_competitiva": "Receita recorrente previsível"
            }
        ],
        "fraquezas": [
            {
                "item": "Dependência de poucos clientes grandes",
                "descricao": "Top 5 clientes representam 65% da receita total",
                "impacto": 8,
                "categoria": "comercial",
                "risco": "Perda de um cliente grande impacta significativamente o faturamento",
                "acao_mitigacao": "Programa agressivo de aquisição de clientes médios"
            },
            {
                "item": "Marketing digital limitado",
                "descricao": "Presença online fraca comparada aos competidores principais",
                "impacto": 6,
                "categoria": "comercial",
                "risco": "Dificuldade em atrair novos leads qualificados",
                "acao_mitigacao": "Contratar agência especializada e aumentar budget 3x"
            },
            {
                "item": "Processos manuais em operações",
                "descricao": "30% dos processos internos ainda dependem de planilhas e emails",
                "impacto": 5,
                "categoria": "operacional",
                "risco": "Ineficiência operacional limitando escalabilidade",
                "acao_mitigacao": "Implementar ERP integrado em 6 meses"
            }
        ],
        "oportunidades": [
            {
                "item": "Expansão para mercado latino-americano",
                "descricao": "Mercado de SaaS B2B crescendo 45% ao ano na região com poucos players estabelecidos",
                "impacto": 9,
                "probabilidade": 8,
                "prazo": "médio",
                "categoria": "mercado",
                "requisitos": "Localização da plataforma e equipe de vendas regional"
            },
            {
                "item": "Integração com IA generativa",
                "descricao": "Demanda crescente por soluções com IA integrada pode dobrar o valor percebido",
                "impacto": 8,
                "probabilidade": 9,
                "prazo": "curto",
                "categoria": "tecnologia",
                "requisitos": "Investimento em P&D e parcerias com provedores de LLM"
            },
            {
                "item": "Programa de parceiros e revendas",
                "descricao": "Potencial de aumentar alcance em 300% através de canal indireto",
                "impacto": 7,
                "probabilidade": 7,
                "prazo": "médio",
                "categoria": "mercado",
                "requisitos": "Estruturação de programa com treinamento e incentivos"
            }
        ],
        "ameacas": [
            {
                "item": "Entrada de big techs no segmento",
                "descricao": "Microsoft e Google anunciaram soluções competidoras para 2025",
                "impacto": 9,
                "probabilidade": 8,
                "prazo": "curto",
                "categoria": "competição",
                "estrategia_defesa": "Focar em verticalização e features específicas do mercado BR"
            },
            {
                "item": "Regulamentação de dados mais restritiva",
                "descricao": "Nova LGPD 2.0 pode exigir mudanças significativas na arquitetura",
                "impacto": 7,
                "probabilidade": 6,
                "prazo": "médio",
                "categoria": "regulatório",
                "estrategia_defesa": "Antecipar compliance e usar como diferencial competitivo"
            },
            {
                "item": "Crise econômica reduzindo orçamentos de TI",
                "descricao": "Previsão de corte de 20-30% em gastos com software B2B",
                "impacto": 8,
                "probabilidade": 5,
                "prazo": "curto",
                "categoria": "econômico",
                "estrategia_defesa": "Criar planos mais acessíveis e focar em ROI rápido"
            }
        ],
        "analise_cruzada": {
            "so_strategies": [
                {
                    "estrategia": "Usar tecnologia proprietária de IA para dominar mercado LATAM antes dos competidores",
                    "forca_relacionada": "Tecnologia proprietária avançada",
                    "oportunidade_relacionada": "Expansão para mercado latino-americano",
                    "prioridade": "alta"
                }
            ],
            "wo_strategies": [
                {
                    "estrategia": "Aproveitar expansão LATAM para diversificar base de clientes e reduzir dependência",
                    "fraqueza_relacionada": "Dependência de poucos clientes grandes",
                    "oportunidade_relacionada": "Expansão para mercado latino-americano",
                    "prioridade": "alta"
                }
            ],
            "st_strategies": [
                {
                    "estrategia": "Fortalecer fidelização de clientes atuais com features exclusivas contra big techs",
                    "forca_relacionada": "Base de clientes fidelizada",
                    "ameaca_relacionada": "Entrada de big techs no segmento",
                    "prioridade": "urgente"
                }
            ],
            "wt_strategies": [
                {
                    "estrategia": "Acelerar diversificação de clientes antes da entrada das big techs",
                    "fraqueza_relacionada": "Dependência de poucos clientes grandes",
                    "ameaca_relacionada": "Entrada de big techs no segmento",
                    "prioridade": "urgente"
                }
            ]
        },
        "matriz_priorizacao": {
            "acoes_curto_prazo": [
                "Implementar features com IA generativa (Q1 2025)",
                "Lançar programa de fidelização premium para top clients"
            ],
            "acoes_medio_prazo": [
                "Estabelecer operação LATAM com equipe local (Q2-Q3 2025)",
                "Lançar programa de parceiros certificados"
            ],
            "acoes_longo_prazo": [
                "IPO ou rodada série C para expansão global (2026)",
                "Aquisição de startup complementar em analytics"
            ]
        },
        "score_estrategico": {
            "posicao_competitiva": 7.5,
            "potencial_crescimento": 8.5,
            "nivel_risco": 7.0,
            "recomendacao_geral": "Empresa bem posicionada tecnologicamente mas precisa urgentemente diversificar receita antes da chegada das big techs"
        }
    }


class TestSwotAnalysisService:
    """Testes para o SwotAnalysisService."""

    def test_service_metadata(self, swot_service):
        """Testa metadados do serviço."""
        assert swot_service.get_name() == "swot_analysis"
        assert swot_service.get_version() == "1.0.0"
        assert swot_service.get_cost() == Decimal("0.005")
        assert len(swot_service.get_description()) > 50

        # Campos obrigatórios e opcionais
        assert swot_service.get_required_fields(
        ) == ["company_name", "company_url"]
        assert "industry" in swot_service.get_optional_fields()
        assert "market_position" in swot_service.get_optional_fields()

    @pytest.mark.asyncio
    async def test_validate_request_valid(self, swot_service, valid_request):
        """Testa validação de requisição válida."""
        assert await swot_service.validate_request(valid_request) is True

    @pytest.mark.asyncio
    async def test_validate_request_missing_fields(self, swot_service):
        """Testa validação com campos faltando."""
        # Sem nome da empresa
        request = ResearchRequest(
            client_id="test_client_123", company_name="", company_url="https://example.com")
        assert await swot_service.validate_request(request) is False

        # Sem URL
        request = ResearchRequest(
            client_id="test_client_123", company_name="Test Company", company_url="")
        assert await swot_service.validate_request(request) is False

        # Nome muito curto
        request = ResearchRequest(
            client_id="test_client_123", company_name="A", company_url="https://example.com")
        assert await swot_service.validate_request(request) is False

    @pytest.mark.asyncio
    async def test_execute_success(self, swot_service, valid_request, complete_swot_response, mock_perplexity_provider):
        """Testa execução bem-sucedida do serviço."""
        # Configurar mock para retornar resposta completa
        mock_perplexity_provider.search.return_value = json.dumps(
            complete_swot_response)

        # Executar serviço
        result = await swot_service.execute(valid_request)

        # Verificar resultado
        assert result.success is True
        assert result.service_name == "swot_analysis"
        assert result.cost == Decimal("0.005")
        assert result.error is None
        assert result.processing_time_seconds > 0

        # Verificar estrutura dos dados
        assert "forcas" in result.data
        assert "fraquezas" in result.data
        assert "oportunidades" in result.data
        assert "ameacas" in result.data
        assert "analise_cruzada" in result.data
        assert "matriz_priorizacao" in result.data
        assert "score_estrategico" in result.data

        # Verificar conteúdo
        assert len(result.data["forcas"]) == 3
        assert len(result.data["fraquezas"]) == 3
        assert result.data["forcas"][0]["impacto"] == 9
        assert result.data["oportunidades"][0]["probabilidade"] == 8

        # Verificar confidence score alto para dados completos
        assert result.confidence_score > 0.85

    @pytest.mark.asyncio
    async def test_execute_with_json_string_response(self, swot_service, valid_request, complete_swot_response, mock_perplexity_provider):
        """Testa processamento quando provider retorna string JSON."""
        # Configurar mock para retornar string JSON
        mock_perplexity_provider.search.return_value = json.dumps(
            complete_swot_response)

        result = await swot_service.execute(valid_request)

        assert result.success is True
        assert isinstance(result.data, dict)
        assert len(result.data["forcas"]) == 3

    @pytest.mark.asyncio
    async def test_execute_with_incomplete_data(self, swot_service, valid_request, mock_perplexity_provider):
        """Testa execução com dados incompletos."""
        # Resposta incompleta
        incomplete_response = {
            "forcas": [
                {
                    "item": "Tecnologia avançada",
                    "descricao": "Boa tecnologia",  # Descrição curta
                    "impacto": 8
                    # Faltando categoria e vantagem_competitiva
                }
            ],
            "fraquezas": [],  # Vazio
            # Faltando outros campos
        }

        mock_perplexity_provider.search.return_value = json.dumps(
            incomplete_response)

        result = await swot_service.execute(valid_request)

        assert result.success is True
        # Confidence score deve ser baixo para dados incompletos
        assert result.confidence_score < 0.3

        # Campos faltantes devem ter valores padrão
        assert "oportunidades" in result.data
        assert "ameacas" in result.data
        assert "analise_cruzada" in result.data
        assert isinstance(result.data["analise_cruzada"], dict)

    @pytest.mark.asyncio
    async def test_execute_with_invalid_json(self, swot_service, valid_request, mock_perplexity_provider):
        """Testa execução quando provider retorna JSON inválido."""
        mock_perplexity_provider.search.return_value = "Invalid JSON {{"

        result = await swot_service.execute(valid_request)

        assert result.success is True  # Não falha, retorna estrutura vazia
        assert result.data == swot_service._get_empty_result()
        assert result.confidence_score == 0.0

    @pytest.mark.asyncio
    async def test_execute_provider_error(self, swot_service, valid_request, mock_perplexity_provider):
        """Testa tratamento de erro do provider."""
        mock_perplexity_provider.search.side_effect = Exception(
            "Provider API error")

        result = await swot_service.execute(valid_request)

        assert result.success is False
        assert result.error == "Provider API error"
        assert result.cost == Decimal("0")  # Não cobra em caso de erro
        assert result.data is None

    def test_extract_additional_context(self, swot_service, valid_request):
        """Testa extração de contexto adicional."""
        context = swot_service._extract_additional_context(valid_request)

        assert context["industry"] == "Tecnologia"
        assert context["market_position"] == "Líder regional"
        assert context["competitors"] == "TechCorp, InnovaSoft"
        assert context["business_model"] == "SaaS B2B"

        # Campos não relevantes não devem ser incluídos
        valid_request.additional_context["irrelevant_field"] = "value"
        context = swot_service._extract_additional_context(valid_request)
        assert "irrelevant_field" not in context

    def test_calculate_confidence_score_complete(self, swot_service, complete_swot_response):
        """Testa cálculo de confidence score com dados completos."""
        score = swot_service._calculate_confidence_score(
            complete_swot_response)

        # Score deve ser alto para dados completos
        assert score > 0.85
        assert score <= 1.0

    def test_calculate_confidence_score_minimal(self, swot_service):
        """Testa cálculo de confidence score com dados mínimos."""
        minimal_data = {
            "forcas": [{"item": "F1", "descricao": "D1" * 30, "impacto": 5}],
            "fraquezas": [{"item": "W1", "descricao": "D2" * 30, "impacto": 5}],
            "oportunidades": [],
            "ameacas": [],
            "analise_cruzada": {},
            "matriz_priorizacao": {},
            "score_estrategico": {}
        }

        score = swot_service._calculate_confidence_score(minimal_data)

        # Score deve ser baixo para dados mínimos
        assert score < 0.5

    def test_get_sample_output_structure(self, swot_service):
        """Testa estrutura do exemplo de saída."""
        sample = swot_service.get_sample_output()

        # Verificar estrutura principal
        assert isinstance(sample, dict)
        assert all(key in sample for key in [
            "forcas", "fraquezas", "oportunidades", "ameacas",
            "analise_cruzada", "matriz_priorizacao", "score_estrategico"
        ])

        # Verificar estrutura de itens SWOT
        assert all("impacto" in item for item in sample["forcas"])
        assert all("probabilidade" in item for item in sample["oportunidades"])
        assert all("probabilidade" in item for item in sample["ameacas"])

        # Verificar análise cruzada
        assert all(key in sample["analise_cruzada"] for key in [
            "so_strategies", "wo_strategies", "st_strategies", "wt_strategies"
        ])

        # Verificar scores estratégicos
        assert isinstance(sample["score_estrategico"]
                          ["posicao_competitiva"], float)
        assert isinstance(sample["score_estrategico"]
                          ["potencial_crescimento"], float)
        assert isinstance(sample["score_estrategico"]["nivel_risco"], float)

    def test_prompts_generation(self, swot_service):
        """Testa geração de prompts."""
        # System prompt
        system_prompt = swot_service._build_system_prompt()
        assert "análise SWOT" in system_prompt
        assert "JSON" in system_prompt
        assert "quantificações" in system_prompt

        # User prompt
        user_prompt = swot_service._build_user_prompt(
            empresa="Test Corp",
            site="https://test.com",
            contexto={"industry": "Tech", "competitors": "A, B, C"}
        )

        assert "Test Corp" in user_prompt
        assert "https://test.com" in user_prompt
        assert "Indústria: Tech" in user_prompt
        assert "Competidores: A, B, C" in user_prompt
        assert "forcas" in user_prompt
        assert "analise_cruzada" in user_prompt

    @pytest.mark.parametrize("quadrant,expected_keys", [
        ("forcas", ["item", "descricao", "impacto",
         "categoria", "vantagem_competitiva"]),
        ("fraquezas", ["item", "descricao", "impacto",
         "categoria", "risco", "acao_mitigacao"]),
        ("oportunidades", ["item", "descricao", "impacto",
         "probabilidade", "prazo", "categoria", "requisitos"]),
        ("ameacas", ["item", "descricao", "impacto",
         "probabilidade", "prazo", "categoria", "estrategia_defesa"])
    ])
    def test_swot_item_structure(self, complete_swot_response, quadrant, expected_keys):
        """Testa estrutura dos itens em cada quadrante SWOT."""
        items = complete_swot_response[quadrant]
        assert len(items) > 0

        for item in items:
            for key in expected_keys:
                assert key in item, f"Campo '{key}' faltando em item de {quadrant}"

    def test_cross_analysis_structure(self, complete_swot_response):
        """Testa estrutura da análise cruzada."""
        cross_analysis = complete_swot_response["analise_cruzada"]

        for strategy_type in ["so_strategies", "wo_strategies", "st_strategies", "wt_strategies"]:
            assert strategy_type in cross_analysis
            strategies = cross_analysis[strategy_type]
            assert len(strategies) > 0

            for strategy in strategies:
                assert "estrategia" in strategy
                assert "prioridade" in strategy
                # Verificar campos específicos por tipo
                if strategy_type == "so_strategies":
                    assert "forca_relacionada" in strategy
                    assert "oportunidade_relacionada" in strategy


# AIDEV-NOTE: Suite de testes completa para SwotAnalysisService com 85%+ cobertura
