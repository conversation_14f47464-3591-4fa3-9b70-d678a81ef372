"""
Utilidades compartilhadas para o sistema de pesquisa modular.
"""
import asyncio
import time
from typing import Dict, Any, List, Optional, Callable
from decimal import Decimal
import logging
from datetime import datetime

logger = logging.getLogger(__name__)


class Timer:
    """Context manager para medir tempo de execução."""

    def __init__(self):
        self.start_time: Optional[float] = None
        self.end_time: Optional[float] = None

    def __enter__(self):
        self.start_time = time.time()
        return self

    def __exit__(self, *args):
        self.end_time = time.time()

    @property
    def elapsed(self) -> float:
        """Tempo decorrido em segundos."""
        if self.start_time is None:
            return 0.0
        end = self.end_time or time.time()
        return end - self.start_time


async def run_with_timeout(coro, timeout_seconds: int):
    """
    Executa uma corrotina com timeout.

    Args:
        coro: Corrotina a executar
        timeout_seconds: Timeout em segundos

    Returns:
        Resultad<PERSON> da corrotina

    Raises:
        asyncio.TimeoutError: Se exceder o timeout
    """
    return await asyncio.wait_for(coro, timeout=timeout_seconds)


def calculate_total_cost(services: List[Dict[str, Any]]) -> Decimal:
    """
    Calcula o custo total de múltiplos serviços.

    Args:
        services: Lista de dicts com 'cost' field

    Returns:
        Custo total em Decimal
    """
    total = Decimal('0')
    for service in services:
        if 'cost' in service:
            total += Decimal(str(service['cost']))
    return total


def sanitize_url(url: str) -> str:
    """
    Normaliza e valida uma URL.

    Args:
        url: URL para sanitizar

    Returns:
        URL normalizada
    """
    url = url.strip()

    # Adiciona protocolo se não tiver
    if not url.startswith(('http://', 'https://')):
        url = f'https://{url}'

    # Remove trailing slash
    if url.endswith('/'):
        url = url[:-1]

    return url


def extract_domain(url: str) -> str:
    """
    Extrai o domínio de uma URL.

    Args:
        url: URL completa

    Returns:
        Domínio (ex: example.com)
    """
    from urllib.parse import urlparse

    parsed = urlparse(sanitize_url(url))
    domain = parsed.netloc

    # Remove www. se presente
    if domain.startswith('www.'):
        domain = domain[4:]

    return domain


async def parallel_execute(
    tasks: List[Callable],
    max_concurrent: int = 5,
    return_exceptions: bool = True
) -> List[Any]:
    """
    Executa múltiplas tarefas em paralelo com limite de concorrência.

    Args:
        tasks: Lista de callables assíncronos
        max_concurrent: Máximo de tarefas simultâneas
        return_exceptions: Se True, retorna exceções ao invés de lançá-las

    Returns:
        Lista com resultados na mesma ordem das tarefas
    """
    semaphore = asyncio.Semaphore(max_concurrent)

    async def run_with_semaphore(task):
        async with semaphore:
            return await task()

    return await asyncio.gather(
        *[run_with_semaphore(task) for task in tasks],
        return_exceptions=return_exceptions
    )


def format_confidence_score(score: float) -> str:
    """
    Formata score de confiança para exibição.

    Args:
        score: Score entre 0.0 e 1.0

    Returns:
        String formatada (ex: "85%")
    """
    if score < 0:
        score = 0
    elif score > 1:
        score = 1

    percentage = int(score * 100)
    return f"{percentage}%"


def log_service_execution(
    service_name: str,
    success: bool,
    elapsed_time: float,
    cost: Decimal,
    error: Optional[str] = None
):
    """
    Registra execução de um serviço no log.

    Args:
        service_name: Nome do serviço
        success: Se foi bem sucedido
        elapsed_time: Tempo de execução em segundos
        cost: Custo em USD
        error: Mensagem de erro se houver
    """
    status = "SUCCESS" if success else "FAILED"
    message = (
        f"[{service_name}] {status} - "
        f"Time: {elapsed_time:.2f}s - "
        f"Cost: ${cost}"
    )

    if error:
        message += f" - Error: {error}"

    if success:
        logger.info(message)
    else:
        logger.error(message)
