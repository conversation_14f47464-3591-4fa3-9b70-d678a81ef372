"""
Research Orchestrator - Coordena a execução dos serviços de pesquisa modulares.
"""
import asyncio
import logging
from typing import Dict, List, Set, Optional, Any
from decimal import Decimal
from datetime import datetime

from app.core import (
    IResearchService,
    ResearchRequest,
    ResearchResult,
    ResearchServiceNotFoundError,
    ResearchValidationError,
    ResearchTimeoutError,
    Timer,
    parallel_execute,
    calculate_total_cost,
    log_service_execution
)

logger = logging.getLogger(__name__)


class ServiceRegistry:
    """Registro central de serviços de pesquisa disponíveis."""

    def __init__(self):
        self._services: Dict[str, IResearchService] = {}

    def register(self, service: IResearchService) -> None:
        """
        Registra um serviço de pesquisa.

        Args:
            service: Instância do serviço
        """
        name = service.get_name()
        if name in self._services:
            logger.warning(f"Serviço {name} já registrado, sobrescrevendo")

        self._services[name] = service
        logger.info(f"Serviço {name} v{service.get_version()} registrado")

    def get(self, name: str) -> IResearchService:
        """
        Obtém um serviço pelo nome.

        Args:
            name: Nome do serviço

        Returns:
            Instância do serviço

        Raises:
            ResearchServiceNotFoundError: Se o serviço não existir
        """
        if name not in self._services:
            raise ResearchServiceNotFoundError(name)
        return self._services[name]

    def list_services(self) -> List[Dict[str, Any]]:
        """
        Lista todos os serviços disponíveis.

        Returns:
            Lista com informações dos serviços
        """
        services = []
        for name, service in self._services.items():
            services.append({
                'name': name,
                'version': service.get_version(),
                'description': service.get_description(),
                'cost': float(service.get_cost()),
                'required_fields': service.get_required_fields(),
                'optional_fields': service.get_optional_fields()
            })
        return services


class ResearchOrchestrator:
    """
    Orquestra a execução de múltiplos serviços de pesquisa.

    Responsabilidades:
    - Validar requisições
    - Executar serviços em paralelo
    - Gerenciar timeouts
    - Consolidar resultados
    - Calcular custos totais
    """

    def __init__(self, registry: ServiceRegistry, default_timeout: int = 60):
        """
        Args:
            registry: Registro de serviços disponíveis
            default_timeout: Timeout padrão em segundos
        """
        self.registry = registry
        self.default_timeout = default_timeout

    async def execute_services(
        self,
        request: ResearchRequest,
        service_names: List[str],
        parallel: bool = True,
        timeout_per_service: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Executa múltiplos serviços de pesquisa.

        Args:
            request: Requisição de pesquisa
            service_names: Lista de nomes dos serviços a executar
            parallel: Se True, executa em paralelo
            timeout_per_service: Timeout por serviço (usa default se None)

        Returns:
            Dict com resultados consolidados
        """
        timeout = timeout_per_service or self.default_timeout

        # Valida serviços
        services = []
        for name in service_names:
            service = self.registry.get(name)
            services.append(service)

        # Valida requisição para cada serviço
        validation_errors = []
        for service in services:
            try:
                is_valid = await service.validate_request(request)
                if not is_valid:
                    validation_errors.append(
                        f"{service.get_name()}: validação falhou")
            except Exception as e:
                validation_errors.append(f"{service.get_name()}: {str(e)}")

        if validation_errors:
            raise ResearchValidationError(
                "múltiplos serviços", "; ".join(validation_errors))

        # Executa serviços
        with Timer() as total_timer:
            if parallel:
                results = await self._execute_parallel(services, request, timeout)
            else:
                results = await self._execute_sequential(services, request, timeout)

        # Consolida resultados
        return self._consolidate_results(results, total_timer.elapsed)

    async def _execute_parallel(
        self,
        services: List[IResearchService],
        request: ResearchRequest,
        timeout: int
    ) -> List[ResearchResult]:
        """Executa serviços em paralelo."""
        tasks = []
        for service in services:
            def task(s=service): return self._execute_single_service(
                s, request, timeout)
            tasks.append(task)

        results = await parallel_execute(tasks, max_concurrent=5)

        # Filtra None e exceções
        valid_results = []
        for result in results:
            if isinstance(result, ResearchResult):
                valid_results.append(result)
            elif isinstance(result, Exception):
                logger.error(f"Erro em serviço: {result}")

        return valid_results

    async def _execute_sequential(
        self,
        services: List[IResearchService],
        request: ResearchRequest,
        timeout: int
    ) -> List[ResearchResult]:
        """Executa serviços sequencialmente."""
        results = []
        for service in services:
            try:
                result = await self._execute_single_service(service, request, timeout)
                if result:
                    results.append(result)
            except Exception as e:
                logger.error(f"Erro ao executar {service.get_name()}: {e}")
                continue

        return results

    async def _execute_single_service(
        self,
        service: IResearchService,
        request: ResearchRequest,
        timeout: int
    ) -> Optional[ResearchResult]:
        """Executa um único serviço com timeout e logging."""
        service_name = service.get_name()

        with Timer() as timer:
            try:
                # Executa com timeout
                result = await asyncio.wait_for(
                    service.execute(request),
                    timeout=timeout
                )

                # Log de sucesso
                log_service_execution(
                    service_name,
                    success=result.success,
                    elapsed_time=timer.elapsed,
                    cost=result.cost,
                    error=result.error
                )

                return result

            except asyncio.TimeoutError:
                # Cria resultado de timeout
                result = ResearchResult(
                    service_name=service_name,
                    service_version=service.get_version(),
                    timestamp=datetime.utcnow(),
                    cost=Decimal('0'),
                    success=False,
                    error=f"Timeout após {timeout}s",
                    processing_time_seconds=timer.elapsed
                )

                log_service_execution(
                    service_name,
                    success=False,
                    elapsed_time=timer.elapsed,
                    cost=Decimal('0'),
                    error=f"Timeout após {timeout}s"
                )

                return result

            except Exception as e:
                # Cria resultado de erro
                result = ResearchResult(
                    service_name=service_name,
                    service_version=service.get_version(),
                    timestamp=datetime.utcnow(),
                    cost=Decimal('0'),
                    success=False,
                    error=str(e),
                    processing_time_seconds=timer.elapsed
                )

                log_service_execution(
                    service_name,
                    success=False,
                    elapsed_time=timer.elapsed,
                    cost=Decimal('0'),
                    error=str(e)
                )

                return result

    def _consolidate_results(
        self,
        results: List[ResearchResult],
        total_elapsed: float
    ) -> Dict[str, Any]:
        """Consolida resultados de múltiplos serviços."""
        successful = [r for r in results if r.success]
        failed = [r for r in results if not r.success]

        # Calcula custos
        total_cost = sum(r.cost for r in results)
        successful_cost = sum(r.cost for r in successful)

        # Agrupa dados por serviço
        services_data = {}
        for result in results:
            services_data[result.service_name] = {
                'success': result.success,
                'data': result.data,
                'error': result.error,
                'cost': float(result.cost),
                'processing_time': result.processing_time_seconds,
                'confidence_score': result.confidence_score,
                'timestamp': result.timestamp.isoformat()
            }

        return {
            'summary': {
                'total_services': len(results),
                'successful': len(successful),
                'failed': len(failed),
                'total_cost': float(total_cost),
                'successful_cost': float(successful_cost),
                'total_processing_time': total_elapsed,
                'timestamp': datetime.utcnow().isoformat()
            },
            'services': services_data,
            'errors': [
                {'service': r.service_name, 'error': r.error}
                for r in failed
            ]
        }

    async def estimate_cost(self, service_names: List[str]) -> Decimal:
        """
        Estima o custo total de executar os serviços especificados.

        Args:
            service_names: Lista de nomes dos serviços

        Returns:
            Custo total estimado
        """
        total = Decimal('0')
        for name in service_names:
            service = self.registry.get(name)
            total += service.get_cost()
        return total

    def get_available_services(self) -> List[Dict[str, Any]]:
        """
        Retorna lista de serviços disponíveis com suas informações.

        Returns:
            Lista com detalhes dos serviços
        """
        return self.registry.list_services()


# Singleton global do orchestrator
_orchestrator: Optional[ResearchOrchestrator] = None


def get_orchestrator() -> ResearchOrchestrator:
    """Obtém a instância global do orchestrator."""
    global _orchestrator
    if _orchestrator is None:
        registry = ServiceRegistry()
        _orchestrator = ResearchOrchestrator(registry)

        # Auto-registra serviços disponíveis
        _auto_register_services(registry)

    return _orchestrator


def _auto_register_services(registry: ServiceRegistry) -> None:
    """Auto-registra todos os serviços disponíveis no diretório research/."""
    from app.services.research import (
        BasicDossierService,
        SwotAnalysisService,
        TechStackService,
        FundingHistoryService,
        MarketResearchService,
        DigitalPresenceService,
        PartnershipService,
        PricingAnalysisService,
        BusinessModelService,
        ChannelsReviewsService
    )

    # AIDEV-NOTE: Provider temporário até implementar injeção de dependência
    # Por enquanto todos os serviços usam mock data
    # TODO: Implementar PerplexityProvider real e injeção de dependência

    # Registra todos os serviços disponíveis
    # None como provider força uso de mock data
    services = [
        BasicDossierService(None),
        SwotAnalysisService(None),
        TechStackService(None),
        FundingHistoryService(None),
        MarketResearchService(None),
        DigitalPresenceService(None),
        PartnershipService(None),
        PricingAnalysisService(None),
        BusinessModelService(None),
        ChannelsReviewsService(None),
    ]

    for service in services:
        registry.register(service)

    logger.info(f"Auto-registrados {len(services)} serviços de pesquisa")
