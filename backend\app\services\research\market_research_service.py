"""
Market Research Service - Análise de mercado e competidores

Este serviço realiza análise profunda do mercado, incluindo:
- Tamanho e crescimento do mercado
- Principais concorrentes e posicionamento
- Tendências e oportunidades
- Barreiras de entrada e riscos
- Fatores de sucesso no setor

Custo: $0.007 por execução
"""
import json
import logging
from typing import Dict, Any, List, Optional
from decimal import Decimal
from datetime import datetime, timezone

from app.core import (
    IResearchService,
    ResearchRequest,
    ResearchResult,
    Timer
)

logger = logging.getLogger(__name__)


class MarketResearchService(IResearchService):
    """
    Serviço especializado em análise de mercado e competidores.

    Fornece insights sobre:
    - Tamanho e crescimento do mercado (TAM, SAM, SOM)
    - Análise competitiva detalhada
    - Tendências e oportunidades emergentes
    - Posicionamento estratégico
    - Fatores críticos de sucesso
    """

    def __init__(self, provider):
        """
        Args:
            provider: Instância do provider (Perplexity, SearchSociety, etc)
        """
        self.provider = provider
        self._version = "1.0.0"
        self._cost = Decimal("0.007")

    def get_name(self) -> str:
        return "market_research"

    def get_version(self) -> str:
        return self._version

    def get_description(self) -> str:
        return "Análise profunda de mercado, concorrentes, tendências e oportunidades estratégicas"

    def get_cost(self) -> Decimal:
        return self._cost

    def get_required_fields(self) -> List[str]:
        return ["company_name", "company_url"]

    def get_optional_fields(self) -> List[str]:
        return ["industry", "city", "state", "target_market", "business_type"]

    async def validate_request(self, request: ResearchRequest) -> bool:
        """Valida se a requisição tem os dados necessários."""
        if not request.company_name or not request.company_url:
            logger.warning(f"Requisição inválida: faltam dados obrigatórios")
            return False

        # URL deve ser válida
        if not request.company_url.startswith(('http://', 'https://')):
            logger.warning(f"URL inválida: {request.company_url}")
            return False

        return True

    async def execute(self, request: ResearchRequest) -> ResearchResult:
        """Executa a análise de mercado."""
        with Timer() as timer:
            try:
                # AIDEV-NOTE: Market research requer análise detalhada de TAM/SAM/SOM
                empresa = request.company_name
                site = request.company_url

                # Preparar contexto adicional
                context = self._build_context(request)

                # Construir prompts
                system_prompt = self._build_system_prompt()
                user_prompt = self._build_user_prompt(empresa, site, context)

                # Fazer requisição ao provider
                logger.info(f"Executando análise de mercado para {empresa}")
                logger.debug(f"Contexto adicional: {context}")

                result_data = await self.provider.search(
                    system_prompt=system_prompt,
                    user_prompt=user_prompt,
                    temperature=0.7
                )

                # Processar e validar resultado
                processed_data = self._process_result(result_data, empresa)

                # Calcular confidence score
                confidence_score = self._calculate_confidence_score(
                    processed_data)

                logger.info(
                    f"Análise de mercado concluída para {empresa} com confidence: {confidence_score:.2f}")

                return ResearchResult(
                    service_name=self.get_name(),
                    service_version=self.get_version(),
                    timestamp=datetime.now(timezone.utc),
                    cost=self._cost,
                    success=True,
                    data=processed_data,
                    processing_time_seconds=timer.elapsed,
                    confidence_score=confidence_score
                )

            except Exception as e:
                logger.error(
                    f"Erro na análise de mercado para {request.company_name}: {str(e)}")
                return ResearchResult(
                    service_name=self.get_name(),
                    service_version=self.get_version(),
                    timestamp=datetime.now(timezone.utc),
                    cost=Decimal("0"),
                    success=False,
                    error=str(e),
                    processing_time_seconds=timer.elapsed
                )

    def get_sample_output(self) -> Dict[str, Any]:
        """Retorna exemplo de saída da análise de mercado."""
        return {
            "market_overview": {
                "tam_total_addressable_market": "$50 bilhões",
                "sam_serviceable_addressable_market": "$15 bilhões",
                "som_serviceable_obtainable_market": "$1.5 bilhões",
                "growth_rate": "15% ao ano",
                "market_stage": "Crescimento",
                "key_trends": [
                    "Digitalização acelerada pós-pandemia",
                    "Aumento da demanda por automação",
                    "Consolidação do mercado"
                ]
            },
            "competitive_landscape": {
                "direct_competitors": [
                    {
                        "name": "Competitor A",
                        "market_share": "25%",
                        "strengths": ["Brand recognition", "Market leader"],
                        "weaknesses": ["High prices", "Legacy tech"],
                        "positioning": "Premium enterprise"
                    }
                ],
                "indirect_competitors": [
                    {
                        "name": "Alternative Solution B",
                        "threat_level": "Medium",
                        "description": "DIY solution gaining traction"
                    }
                ],
                "market_concentration": "Moderadamente concentrado",
                "barriers_to_entry": [
                    "Alto investimento inicial",
                    "Necessidade de expertise técnica",
                    "Relacionamentos estabelecidos"
                ]
            },
            "positioning_analysis": {
                "current_position": "Challenger inovador",
                "unique_value_proposition": "Solução mais acessível e moderna",
                "target_segments": ["PMEs digitais", "Startups em crescimento"],
                "differentiation_factors": [
                    "Preço competitivo",
                    "Interface intuitiva",
                    "Integração fácil"
                ]
            },
            "opportunities_threats": {
                "opportunities": [
                    {
                        "description": "Expansão para mercado latino-americano",
                        "potential_impact": "Alto",
                        "timeline": "12-18 meses"
                    }
                ],
                "threats": [
                    {
                        "description": "Entrada de big techs no segmento",
                        "probability": "Média",
                        "mitigation": "Foco em nicho específico"
                    }
                ],
                "success_factors": [
                    "Velocidade de inovação",
                    "Qualidade do atendimento",
                    "Parcerias estratégicas"
                ]
            },
            "market_dynamics": {
                "customer_acquisition_cost": "$500-1500",
                "customer_lifetime_value": "$5000-15000",
                "sales_cycle": "30-90 dias",
                "churn_rate": "10-15% anual",
                "pricing_models": ["SaaS mensal", "Anual com desconto", "Enterprise custom"]
            }
        }

    def _build_context(self, request: ResearchRequest) -> Dict[str, Any]:
        """Constrói contexto adicional da requisição."""
        context = {}

        # AIDEV-NOTE: Verificação segura de atributos opcionais do request
        industry = getattr(request, 'industry', None)
        if industry:
            context['industry'] = industry

        target_market = getattr(request, 'target_market', None)
        if target_market:
            context['target_market'] = target_market

        business_type = getattr(request, 'business_type', None)
        if business_type:
            context['business_type'] = business_type

        return context

    def _build_system_prompt(self) -> str:
        """Constrói o prompt do sistema para análise de mercado."""
        return """Você é um analista de mercado sênior especializado em inteligência competitiva e análise estratégica.

Sua missão é fornecer uma análise profunda e acionável do mercado, incluindo:
1. Dimensionamento preciso do mercado (TAM, SAM, SOM)
2. Mapeamento detalhado de competidores
3. Identificação de tendências e oportunidades
4. Análise de posicionamento estratégico
5. Fatores críticos de sucesso

Sempre forneça dados quantitativos quando possível e cite tendências verificáveis.
Use "Dado não encontrado" quando não tiver informações específicas.

Responda SEMPRE em formato JSON estruturado."""

    def _build_user_prompt(self, empresa: str, site: str, context: Dict[str, Any]) -> str:
        """Constrói o prompt do usuário com dados da empresa."""
        context_str = ""
        if context:
            context_str = f"\n\nContexto adicional:\n"
            for key, value in context.items():
                context_str += f"- {key}: {value}\n"

        return f"""Realize uma análise completa de mercado para a empresa {empresa} ({site}).{context_str}

Forneça a análise em formato JSON com a seguinte estrutura:

{{
    "market_overview": {{
        "tam_total_addressable_market": "valor estimado em R$ ou $",
        "sam_serviceable_addressable_market": "valor estimado",
        "som_serviceable_obtainable_market": "valor estimado", 
        "growth_rate": "taxa de crescimento anual",
        "market_stage": "Nascente/Crescimento/Maduro/Declínio",
        "key_trends": ["tendência 1", "tendência 2", "tendência 3"]
    }},
    "competitive_landscape": {{
        "direct_competitors": [
            {{
                "name": "nome do competidor",
                "market_share": "percentual estimado",
                "strengths": ["força 1", "força 2"],
                "weaknesses": ["fraqueza 1", "fraqueza 2"],
                "positioning": "descrição do posicionamento"
            }}
        ],
        "indirect_competitors": [
            {{
                "name": "nome da alternativa",
                "threat_level": "Alto/Médio/Baixo",
                "description": "breve descrição"
            }}
        ],
        "market_concentration": "descrição da concentração",
        "barriers_to_entry": ["barreira 1", "barreira 2"]
    }},
    "positioning_analysis": {{
        "current_position": "posição atual da empresa",
        "unique_value_proposition": "proposta de valor única",
        "target_segments": ["segmento 1", "segmento 2"],
        "differentiation_factors": ["fator 1", "fator 2"]
    }},
    "opportunities_threats": {{
        "opportunities": [
            {{
                "description": "descrição da oportunidade",
                "potential_impact": "Alto/Médio/Baixo",
                "timeline": "prazo estimado"
            }}
        ],
        "threats": [
            {{
                "description": "descrição da ameaça",
                "probability": "Alta/Média/Baixa",
                "mitigation": "estratégia de mitigação"
            }}
        ],
        "success_factors": ["fator 1", "fator 2", "fator 3"]
    }},
    "market_dynamics": {{
        "customer_acquisition_cost": "CAC estimado",
        "customer_lifetime_value": "LTV estimado",
        "sales_cycle": "duração do ciclo de vendas",
        "churn_rate": "taxa de churn estimada",
        "pricing_models": ["modelo 1", "modelo 2"]
    }}
}}

Importante:
- Seja específico e use dados reais sempre que possível
- Para TAM/SAM/SOM, forneça valores numéricos estimados
- Liste no mínimo 3 competidores diretos relevantes
- Identifique tendências emergentes e oportunidades concretas
- Use "Dado não encontrado" quando não tiver informações específicas"""

    def _process_result(self, raw_data: Any, empresa: str) -> Dict[str, Any]:
        """Processa e valida o resultado da análise."""
        # AIDEV-NOTE: Processamento robusto com fallbacks para dados incompletos
        if isinstance(raw_data, str):
            try:
                data = json.loads(raw_data)
            except json.JSONDecodeError:
                logger.error(f"Falha ao decodificar JSON para {empresa}")
                return self._get_empty_result()
        else:
            data = raw_data

        # Garantir estrutura esperada
        result = {
            "market_overview": data.get("market_overview", {}),
            "competitive_landscape": data.get("competitive_landscape", {}),
            "positioning_analysis": data.get("positioning_analysis", {}),
            "opportunities_threats": data.get("opportunities_threats", {}),
            "market_dynamics": data.get("market_dynamics", {})
        }

        # Validar e corrigir market_overview
        market = result["market_overview"]
        market.setdefault("tam_total_addressable_market",
                          "Dado não encontrado")
        market.setdefault("sam_serviceable_addressable_market",
                          "Dado não encontrado")
        market.setdefault("som_serviceable_obtainable_market",
                          "Dado não encontrado")
        market.setdefault("growth_rate", "Dado não encontrado")
        market.setdefault("market_stage", "Não identificado")
        market.setdefault("key_trends", [])

        # Validar competitive_landscape
        competitive = result["competitive_landscape"]
        competitive.setdefault("direct_competitors", [])
        competitive.setdefault("indirect_competitors", [])
        competitive.setdefault("market_concentration", "Não analisado")
        competitive.setdefault("barriers_to_entry", [])

        # Processar competidores
        for comp in competitive["direct_competitors"]:
            comp.setdefault("name", "Competidor não identificado")
            comp.setdefault("market_share", "Não disponível")
            comp.setdefault("strengths", [])
            comp.setdefault("weaknesses", [])
            comp.setdefault("positioning", "Não definido")

        # Validar positioning_analysis
        positioning = result["positioning_analysis"]
        positioning.setdefault("current_position", "Não definido")
        positioning.setdefault("unique_value_proposition", "Não identificado")
        positioning.setdefault("target_segments", [])
        positioning.setdefault("differentiation_factors", [])

        # Validar opportunities_threats
        opp_threats = result["opportunities_threats"]
        opp_threats.setdefault("opportunities", [])
        opp_threats.setdefault("threats", [])
        opp_threats.setdefault("success_factors", [])

        # Validar market_dynamics
        dynamics = result["market_dynamics"]
        dynamics.setdefault("customer_acquisition_cost", "Não estimado")
        dynamics.setdefault("customer_lifetime_value", "Não estimado")
        dynamics.setdefault("sales_cycle", "Não identificado")
        dynamics.setdefault("churn_rate", "Não disponível")
        dynamics.setdefault("pricing_models", [])

        # Adicionar metadata
        result["_metadata"] = {
            "analysis_date": datetime.now(timezone.utc).isoformat(),
            "company_analyzed": empresa,
            "data_completeness": self._assess_data_completeness(result)
        }

        return result

    def _calculate_confidence_score(self, data: Dict[str, Any]) -> float:
        """
        Calcula score de confiança baseado na completude e qualidade dos dados.

        Score considera:
        - Presença de valores de mercado (TAM/SAM/SOM)
        - Número de competidores identificados
        - Completude das análises
        - Presença de dados quantitativos
        """
        total_checks = 0
        passed_checks = 0

        # AIDEV-NOTE: Se dados estão completamente vazios, retorna 0 imediatamente
        # Verifica se há algum dado real (não padrão)
        has_real_data = False

        # Verifica market_overview
        if "market_overview" in data:
            mo = data["market_overview"]
            if (mo.get("tam_total_addressable_market") != "Dado não encontrado" or
                mo.get("growth_rate") != "Dado não encontrado" or
                    len(mo.get("key_trends", [])) > 0):
                has_real_data = True

        # Verifica competitive_landscape
        if "competitive_landscape" in data:
            cl = data["competitive_landscape"]
            if (len(cl.get("direct_competitors", [])) > 0 or
                    len(cl.get("barriers_to_entry", [])) > 0):
                has_real_data = True

        # Se não há dados reais, retorna 0
        if not has_real_data:
            return 0.0

        # Verificar dados de mercado
        market_checks = [
            ("has_tam", lambda d: d["market_overview"]
             ["tam_total_addressable_market"] != "Dado não encontrado"),
            ("has_sam", lambda d: d["market_overview"]
             ["sam_serviceable_addressable_market"] != "Dado não encontrado"),
            ("has_growth", lambda d: d["market_overview"]
             ["growth_rate"] != "Dado não encontrado"),
            ("has_trends", lambda d: len(
                d["market_overview"]["key_trends"]) >= 2),
            ("has_stage", lambda d: d["market_overview"]
             ["market_stage"] != "Não identificado")
        ]

        # Verificar análise competitiva
        competitive_checks = [
            ("has_competitors", lambda d: len(
                d["competitive_landscape"]["direct_competitors"]) >= 2),
            ("competitors_detailed", lambda d: all(
                len(c.get("strengths", [])) > 0 and len(
                    c.get("weaknesses", [])) > 0
                for c in d["competitive_landscape"]["direct_competitors"][:3]
            )),
            ("has_barriers", lambda d: len(
                d["competitive_landscape"]["barriers_to_entry"]) >= 2),
            ("has_concentration", lambda d: d["competitive_landscape"]
             ["market_concentration"] != "Não analisado")
        ]

        # Verificar posicionamento
        positioning_checks = [
            ("has_position", lambda d: d["positioning_analysis"]
             ["current_position"] != "Não definido"),
            ("has_uvp", lambda d: d["positioning_analysis"]
             ["unique_value_proposition"] != "Não identificado"),
            ("has_segments", lambda d: len(
                d["positioning_analysis"]["target_segments"]) > 0),
            ("has_differentiation", lambda d: len(
                d["positioning_analysis"]["differentiation_factors"]) >= 2)
        ]

        # Verificar oportunidades e ameaças
        opp_threat_checks = [
            ("has_opportunities", lambda d: len(
                d["opportunities_threats"]["opportunities"]) > 0),
            ("has_threats", lambda d: len(
                d["opportunities_threats"]["threats"]) > 0),
            ("has_success_factors", lambda d: len(
                d["opportunities_threats"]["success_factors"]) >= 2)
        ]

        # Verificar dinâmicas de mercado
        dynamics_checks = [
            ("has_cac", lambda d: d["market_dynamics"]
             ["customer_acquisition_cost"] != "Não estimado"),
            ("has_ltv", lambda d: d["market_dynamics"]
             ["customer_lifetime_value"] != "Não estimado"),
            ("has_pricing", lambda d: len(
                d["market_dynamics"]["pricing_models"]) > 0)
        ]

        # Executar todas as verificações
        all_checks = (
            market_checks +
            competitive_checks +
            positioning_checks +
            opp_threat_checks +
            dynamics_checks
        )

        for check_name, check_func in all_checks:
            total_checks += 1
            try:
                if check_func(data):
                    passed_checks += 1
                    logger.debug(f"Check passed: {check_name}")
                else:
                    logger.debug(f"Check failed: {check_name}")
            except Exception as e:
                logger.debug(f"Check error {check_name}: {str(e)}")

        score = passed_checks / total_checks if total_checks > 0 else 0.0
        logger.info(
            f"Market research confidence score: {score:.2f} ({passed_checks}/{total_checks} checks)")

        return score

    def _assess_data_completeness(self, data: Dict[str, Any]) -> str:
        """Avalia nível de completude dos dados."""
        score = self._calculate_confidence_score(data)

        if score >= 0.8:
            return "Alto"
        elif score >= 0.6:
            return "Médio"
        elif score >= 0.4:
            return "Baixo"
        else:
            return "Muito Baixo"

    def _get_empty_result(self) -> Dict[str, Any]:
        """Retorna estrutura vazia para casos de erro."""
        return {
            "market_overview": {
                "tam_total_addressable_market": "Dado não encontrado",
                "sam_serviceable_addressable_market": "Dado não encontrado",
                "som_serviceable_obtainable_market": "Dado não encontrado",
                "growth_rate": "Dado não encontrado",
                "market_stage": "Não identificado",
                "key_trends": []
            },
            "competitive_landscape": {
                "direct_competitors": [],
                "indirect_competitors": [],
                "market_concentration": "Não analisado",
                "barriers_to_entry": []
            },
            "positioning_analysis": {
                "current_position": "Não definido",
                "unique_value_proposition": "Não identificado",
                "target_segments": [],
                "differentiation_factors": []
            },
            "opportunities_threats": {
                "opportunities": [],
                "threats": [],
                "success_factors": []
            },
            "market_dynamics": {
                "customer_acquisition_cost": "Não estimado",
                "customer_lifetime_value": "Não estimado",
                "sales_cycle": "Não identificado",
                "churn_rate": "Não disponível",
                "pricing_models": []
            },
            "_metadata": {
                "analysis_date": datetime.now(timezone.utc).isoformat(),
                "company_analyzed": "Desconhecida",
                "data_completeness": "Muito Baixo"
            }
        }
