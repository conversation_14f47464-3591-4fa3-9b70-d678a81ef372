# [TASK-008] Redis Cache System Implementation

**Status**: 📝 TODO
**Prioridade**: P1
**Estimativa**: 12h
**Due**: 2025-02-06
**Tags**: [production-ready, cache, redis, performance, cost-optimization]
**Blocked by**: TASK-007

## 🎯 Goal

Implementar sistema de cache Redis distribuído para reduzir custos de API em 70% e melhorar performance em 5x.

## 📋 Context

- Cada requisição Perplexity custa ~$0.01
- Muitas pesquisas são repetidas para mesmas empresas
- Cache pode reduzir latência de 5s para <100ms
- Redis permite cache distribuído entre workers

## 🚀 Implementation Plan

### FASE 1: Setup Redis Infrastructure (3h)

#### MT-1.1: Configurar Redis Server (1h)
**Goal**: Setup Redis local e produção
**Files Affected**:
- `docker-compose.yml`
- `backend/config/redis_config.py` (NOVO)
**Outline**:
```yaml
redis:
  image: redis:7-alpine
  ports:
    - "6379:6379"
  volumes:
    - redis_data:/data
  command: redis-server --appendonly yes
```

#### MT-1.2: Criar Redis Client Wrapper (2h)
**Goal**: Cliente Redis com connection pooling
**Files Affected**:
- `backend/app/cache/redis_client.py` (NOVO)
**Outline**:
```python
class RedisCache:
    def __init__(self, url: str):
        self.pool = redis.ConnectionPool.from_url(url)
        self.client = redis.Redis(connection_pool=self.pool)
    
    async def get(self, key: str) -> Optional[Any]:
        # Deserializar JSON
    
    async def set(self, key: str, value: Any, ttl: int):
        # Serializar e armazenar
```

### FASE 2: Implementar Cache Strategy (5h)

#### MT-2.1: Criar Cache Key Generator (2h)
**Goal**: Gerar keys únicas e consistentes
**Files Affected**:
- `backend/app/cache/cache_keys.py` (NOVO)
**Outline**:
```python
def generate_cache_key(
    service_name: str,
    company_name: str,
    company_url: str,
    version: str
) -> str:
    # Hash MD5 dos parâmetros
    # Formato: "scopeai:v1:service:{hash}"
```

#### MT-2.2: Implementar Cache Decorator (2h)
**Goal**: Decorator para cachear serviços automaticamente
**Files Affected**:
- `backend/app/cache/decorators.py` (NOVO)
**Outline**:
```python
@cache_result(ttl_hours=24)
async def execute(self, request: ResearchRequest):
    # Execução normal do serviço
```

#### MT-2.3: Configurar TTL Strategy (1h)
**Goal**: TTL diferenciado por tipo de dado
**Files Affected**:
- `backend/app/cache/ttl_strategy.py` (NOVO)
**Outline**:
- Dados financeiros: 6 horas
- Análise técnica: 24 horas
- Dados básicos: 48 horas
- SWOT/Mercado: 72 horas

### FASE 3: Integração com Serviços (4h)

#### MT-3.1: Integrar Cache no Orchestrator (2h)
**Goal**: Cache transparente no orchestrador
**Files Affected**:
- `backend/app/services/research_orchestrator.py`
**Outline**:
1. Check cache antes de executar serviço
2. Armazenar resultados bem-sucedidos
3. Cache warming para empresas populares
4. Métricas de hit/miss ratio

#### MT-3.2: Implementar Cache Invalidation (1h)
**Goal**: Invalidar cache quando necessário
**Files Affected**:
- `backend/app/cache/invalidation.py` (NOVO)
**Outline**:
1. Invalidação manual via API
2. Invalidação por evento (empresa atualizada)
3. Bulk invalidation por pattern
4. Audit log de invalidações

#### MT-3.3: Dashboard de Métricas (1h)
**Goal**: Visualizar eficiência do cache
**Files Affected**:
- `backend/app/api/cache_routes.py` (NOVO)
**Outline**:
```python
GET /api/cache/stats
{
    "hit_rate": 0.75,
    "miss_rate": 0.25,
    "total_requests": 10000,
    "cache_size_mb": 512,
    "cost_saved": "$750.00"
}
```

## 📊 Success Criteria

- [ ] Hit rate > 70% após 1 semana
- [ ] Redução de custos > 70%
- [ ] Latência p95 < 100ms (cache hit)
- [ ] Zero cache corruption
- [ ] Monitoramento completo
- [ ] Documentação de troubleshooting

## 🔧 Technical Requirements

### Configuração Redis
```python
REDIS_CONFIG = {
    "url": "redis://localhost:6379/0",
    "max_connections": 50,
    "decode_responses": True,
    "socket_keepalive": True,
    "socket_connect_timeout": 5,
    "retry_on_timeout": True
}
```

### Estrutura Cache Entry
```json
{
    "key": "scopeai:v1:basic_dossier:a1b2c3d4",
    "value": {
        "data": {...},
        "metadata": {
            "service": "basic_dossier",
            "company": "TechCorp",
            "cached_at": "2025-01-30T17:35:00Z",
            "expires_at": "2025-01-31T17:35:00Z",
            "version": "1.0.0",
            "hit_count": 5
        }
    },
    "ttl": 86400
}
```

## 🚨 Riscos e Mitigações

| Risco | Impacto | Mitigação |
|-------|---------|-----------|
| Cache stampede | Alto | Distributed locks |
| Dados obsoletos | Médio | TTL conservador |
| Redis downtime | Alto | Fallback direto para API |
| Memory overflow | Médio | LRU eviction policy |

## 📝 Notas de Implementação

### Estratégia de Warming
```python
# Cache warming para top 100 empresas
async def warm_cache():
    top_companies = await get_top_companies()
    for company in top_companies:
        await orchestrator.execute_services(
            ResearchRequest(company),
            ["basic_dossier", "swot_analysis"]
        )
```

### Compressão de Dados
- Usar zlib para comprimir valores > 1KB
- Reduz uso de memória em ~60%
- Trade-off: +2ms latência

---

**Criado em**: 2025-01-30 17:35  
**Criado por**: AI Assistant 