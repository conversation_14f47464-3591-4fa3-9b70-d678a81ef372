# Backend App - Arquitetura Simples

Esta é a nova estrutura modular do backend ScopeAI, seguindo princípios de simplicidade e pragmatismo.

## 📁 Estrutura

```
app/
├── config/           # Configurações e variáveis de ambiente
├── routes/           # Endpoints FastAPI organizados por domínio
├── services/         # Lógica de negócio e serviços
│   └── research/     # 11 serviços de pesquisa modulares
├── core/             # Classes base, interfaces e utilitários compartilhados
└── tests/            # Testes unitários e de integração
```

## 🎯 Princípios

1. **Simplicidade**: Sem overengineering, estrutura clara e direta
2. **Modularidade**: Serviços independentes e reutilizáveis
3. **Testabilidade**: Cada módulo facilmente testável
4. **Evolução Incremental**: Fácil adicionar novos serviços

## 🔄 Migração

Estamos migrando de uma arquitetura monolítica para serviços modulares:
- **De**: `perplexity.py` com 2.070 linhas
- **Para**: 11 serviços independentes de ~200 linhas cada

## 💰 Benefícios

- **Economia**: Usuário paga apenas pelos serviços que usar ($0.006-$0.05)
- **Performance**: Execução paralela (30s vs 5min)
- **Manutenibilidade**: Código mais limpo e focado
- **Flexibilidade**: Fácil adicionar ou modificar serviços 