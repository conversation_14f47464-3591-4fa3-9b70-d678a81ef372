"""
Testes unitários para o BasicDossierService.
"""
import pytest
import json
from unittest.mock import Mock, AsyncMock, patch
from decimal import Decimal
from datetime import datetime

from app.services.research import BasicDossierService
from app.core import ResearchRequest, ResearchResult


class TestBasicDossierService:
    """Testes para o serviço de dossiê básico."""

    @pytest.fixture
    def mock_provider(self):
        """Mock do provider Perplexity."""
        provider = Mock()
        provider.search = AsyncMock()
        return provider

    @pytest.fixture
    def service(self, mock_provider):
        """Instância do serviço para testes."""
        return BasicDossierService(mock_provider)

    @pytest.fixture
    def valid_request(self):
        """Requisição válida de exemplo."""
        return ResearchRequest(
            client_id="test123",
            company_name="Empresa Teste S.A.",
            company_url="https://empresateste.com.br",
            additional_context={
                "city": "São Paulo",
                "state": "SP"
            }
        )

    @pytest.fixture
    def valid_response(self):
        """Resposta válida de exemplo do provider."""
        return {
            "informacoes_gerais": {
                "nome": "Empresa Teste S.A.",
                "descricao": "Líder em soluções tecnológicas",
                "executivos": ["João Silva - CEO", "Maria Santos - CTO"]
            },
            "historico": "Fundada em 2010, a empresa se especializou em...",
            "produtos_servicos": ["Software ERP", "Consultoria"],
            "concorrentes": {
                "principais": ["Concorrente A", "Concorrente B"],
                "diferenciais_concorrentes": "Melhor suporte técnico"
            },
            "cases_sucesso": {
                "case_1": "Implementação no Cliente X",
                "case_2": "Transformação digital do Cliente Y"
            },
            "saude_financeira": {
                "faturamento_historico": "R$ 10M (2022) → R$ 15M (2023)",
                "projecao_2024": "R$ 20M",
                "crescimento": "50% ao ano"
            },
            "analise_swot": {
                "forcas": ["Tecnologia própria", "Equipe qualificada"],
                "fraquezas": ["Marketing limitado"],
                "oportunidades": ["Expansão regional"],
                "ameacas": ["Novos entrantes"]
            },
            "oportunidades_novos_produtos": ["IA integrada"],
            "melhorias_futuras": ["UX/UI", "Mobile"]
        }

    def test_service_metadata(self, service):
        """Testa metadados do serviço."""
        assert service.get_name() == "basic_dossier"
        assert service.get_version() == "1.0.0"
        assert service.get_cost() == Decimal("0.006")
        assert "dossiê básico" in service.get_description().lower()

    def test_required_fields(self, service):
        """Testa campos obrigatórios."""
        required = service.get_required_fields()
        assert "company_name" in required
        assert "company_url" in required
        assert len(required) == 2

    def test_optional_fields(self, service):
        """Testa campos opcionais."""
        optional = service.get_optional_fields()
        assert "city" in optional
        assert "state" in optional
        assert "country" in optional

    @pytest.mark.asyncio
    async def test_validate_request_valid(self, service, valid_request):
        """Testa validação de requisição válida."""
        result = await service.validate_request(valid_request)
        assert result is True

    @pytest.mark.asyncio
    async def test_validate_request_missing_name(self, service):
        """Testa validação com nome faltando."""
        request = ResearchRequest(
            client_id="test123",
            company_name="",
            company_url="https://test.com"
        )
        result = await service.validate_request(request)
        assert result is False

    @pytest.mark.asyncio
    async def test_validate_request_short_name(self, service):
        """Testa validação com nome muito curto."""
        request = ResearchRequest(
            client_id="test123",
            company_name="A",
            company_url="https://test.com"
        )
        result = await service.validate_request(request)
        assert result is False

    @pytest.mark.asyncio
    async def test_execute_success(self, service, mock_provider, valid_request, valid_response):
        """Testa execução bem-sucedida."""
        # Configurar mock
        mock_provider.search.return_value = valid_response

        # Executar
        result = await service.execute(valid_request)

        # Verificar resultado
        assert isinstance(result, ResearchResult)
        assert result.success is True
        assert result.service_name == "basic_dossier"
        assert result.cost == Decimal("0.006")
        assert result.data == valid_response
        assert result.confidence_score is not None
        assert result.confidence_score > 0.8  # Alta confiança com dados completos
        assert result.error is None

        # Verificar chamada ao provider
        mock_provider.search.assert_called_once()
        call_args = mock_provider.search.call_args[1]
        assert "system_prompt" in call_args
        assert "user_prompt" in call_args
        assert "Empresa Teste S.A." in call_args["user_prompt"]
        assert "São Paulo" in call_args["user_prompt"]

    @pytest.mark.asyncio
    async def test_execute_with_json_string_response(self, service, mock_provider, valid_request, valid_response):
        """Testa execução quando provider retorna string JSON."""
        # Configurar mock para retornar string
        mock_provider.search.return_value = json.dumps(valid_response)

        # Executar
        result = await service.execute(valid_request)

        # Verificar que processou corretamente
        assert result.success is True
        assert result.data == valid_response

    @pytest.mark.asyncio
    async def test_execute_with_missing_fields(self, service, mock_provider, valid_request):
        """Testa execução com campos faltando na resposta."""
        # Resposta incompleta
        incomplete_response = {
            "informacoes_gerais": {
                "nome": "Empresa Teste",
                "descricao": "Descrição"
            },
            "historico": "História da empresa"
            # Faltam vários campos
        }

        mock_provider.search.return_value = incomplete_response

        # Executar
        result = await service.execute(valid_request)

        # Verificar que preencheu campos faltantes
        assert result.success is True
        assert "produtos_servicos" in result.data
        assert "concorrentes" in result.data
        assert "analise_swot" in result.data
        assert result.confidence_score < 0.5  # Baixa confiança com dados incompletos

    @pytest.mark.asyncio
    async def test_execute_provider_error(self, service, mock_provider, valid_request):
        """Testa tratamento de erro do provider."""
        # Configurar erro
        mock_provider.search.side_effect = Exception("API error")

        # Executar
        result = await service.execute(valid_request)

        # Verificar resultado de erro
        assert result.success is False
        assert result.error == "API error"
        assert result.cost == Decimal("0")  # Não cobra em erro
        assert result.data is None

    @pytest.mark.asyncio
    async def test_execute_invalid_json_response(self, service, mock_provider, valid_request):
        """Testa tratamento de resposta JSON inválida."""
        # Configurar resposta inválida
        mock_provider.search.return_value = "Invalid JSON {{"

        # Executar
        result = await service.execute(valid_request)

        # Verificar que retornou estrutura vazia
        assert result.success is True
        assert result.data["informacoes_gerais"]["nome"] == "Dado não encontrado"
        assert result.confidence_score == 0.0

    def test_sample_output_structure(self, service):
        """Testa estrutura do output de exemplo."""
        sample = service.get_sample_output()

        # Verificar estrutura
        assert "informacoes_gerais" in sample
        assert "historico" in sample
        assert "produtos_servicos" in sample
        assert "concorrentes" in sample
        assert "cases_sucesso" in sample
        assert "saude_financeira" in sample
        assert "analise_swot" in sample
        assert "oportunidades_novos_produtos" in sample
        assert "melhorias_futuras" in sample

        # Verificar sub-estruturas
        assert "principais" in sample["concorrentes"]
        assert "forcas" in sample["analise_swot"]
        assert "fraquezas" in sample["analise_swot"]

    def test_confidence_score_calculation(self, service):
        """Testa cálculo do score de confiança."""
        # Dados completos = alta confiança
        complete_data = service.get_sample_output()
        score = service._calculate_confidence_score(complete_data)
        assert score > 0.8

        # Dados vazios = baixa confiança
        empty_data = service._get_empty_result()
        score = service._calculate_confidence_score(empty_data)
        assert score == 0.0

        # Dados parciais = confiança média
        partial_data = {
            "informacoes_gerais": {"nome": "Teste", "descricao": "Desc", "executivos": ["CEO"]},
            "historico": "História completa da empresa com mais de 50 caracteres",
            "produtos_servicos": ["Produto 1"],
            "concorrentes": {"principais": [], "diferenciais_concorrentes": "Dado não encontrado"},
            "cases_sucesso": {},
            "saude_financeira": {"faturamento_historico": "Dado não encontrado"},
            "analise_swot": {"forcas": ["F1"], "fraquezas": [], "oportunidades": [], "ameacas": []},
            "oportunidades_novos_produtos": [],
            "melhorias_futuras": []
        }
        score = service._calculate_confidence_score(partial_data)
        assert 0.3 < score < 0.7

    def test_build_prompts(self, service):
        """Testa construção dos prompts."""
        # System prompt
        system_prompt = service._build_system_prompt()
        assert "analista sênior" in system_prompt
        assert "JSON válido" in system_prompt

        # User prompt com localização
        user_prompt = service._build_user_prompt(
            empresa="Teste Corp",
            site="https://teste.com",
            cidade="São Paulo",
            estado="SP"
        )
        assert "Teste Corp" in user_prompt
        assert "São Paulo" in user_prompt
        assert "SP" in user_prompt
        assert "Brasil" in user_prompt

        # User prompt sem localização
        user_prompt_no_location = service._build_user_prompt(
            empresa="Teste Corp",
            site="https://teste.com"
        )
        assert "sediada em" not in user_prompt_no_location
