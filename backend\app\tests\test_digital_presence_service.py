"""
Unit tests for Digital Presence Service
"""

import pytest
from datetime import datetime, UTC
from unittest.mock import Mo<PERSON>, AsyncMock, patch
import json

from app.services.research.digital_presence_service import (
    DigitalPresenceService,
    DigitalPresenceAnalysis
)
from app.core.exceptions import ResearchException


class TestDigitalPresenceService:
    """Test suite for DigitalPresenceService"""

    @pytest.fixture
    def service(self):
        """Create service instance"""
        return DigitalPresenceService()

    @pytest.fixture
    def mock_provider(self):
        """Create mock provider"""
        provider = Mock()
        provider.search = AsyncMock()
        return provider

    @pytest.fixture
    def service_with_provider(self, mock_provider):
        """Create service with mock provider"""
        return DigitalPresenceService(provider=mock_provider)

    @pytest.fixture
    def sample_data(self):
        """Sample data for testing"""
        return {
            "domain_authority": 45,
            "organic_traffic": 25000,
            "traffic_growth": "increasing",
            "ranking_keywords": 1250,
            "top_keywords": ["software empresarial", "gestão empresas", "ERP cloud"],
            "total_backlinks": 5420,
            "referring_domains": 342,
            "backlink_quality": 72,
            "mobile_friendly": True,
            "page_speed": 78,
            "https": True,
            "has_sitemap": True,
            "has_robots": True,
            "linkedin_data": {
                "followers": 15420,
                "engagement_rate": 3.2,
                "post_frequency": "3x per week",
                "growth_rate": 12
            },
            "twitter_data": {
                "followers": 8930,
                "engagement_rate": 2.1,
                "post_frequency": "daily",
                "growth_rate": 8
            },
            "facebook_data": {
                "followers": 22150,
                "engagement_rate": 1.8,
                "post_frequency": "2x per week",
                "growth_rate": 5
            },
            "instagram_data": {
                "followers": 12800,
                "engagement_rate": 4.5,
                "post_frequency": "3x per week",
                "growth_rate": 15
            },
            "page_load_time": 2.3,
            "mobile_score": 85,
            "desktop_score": 92,
            "lcp": 2.1,
            "fid": 95,
            "cls": 0.05,
            "bounce_rate": 42,
            "session_duration": 185,
            "pages_per_session": 3.2,
            "conversion_rate": 2.8,
            "cms": "WordPress",
            "hosting": "AWS",
            "cdn": "CloudFlare",
            "analytics_tools": ["Google Analytics", "Hotjar"],
            "marketing_tools": ["HubSpot", "Mailchimp"],
            "total_pages": 145,
            "blog_posts": 67,
            "has_blog": True,
            "blog_frequency": "weekly",
            "total_blog_posts": 67,
            "blog_categories": ["Tecnologia", "Gestão", "Inovação", "Cases"],
            "average_post_length": 1200,
            "has_articles": True,
            "has_videos": True,
            "has_podcasts": False,
            "has_infographics": True,
            "has_ebooks": True,
            "has_webinars": True,
            "originality_score": 82,
            "readability_score": 75,
            "content_seo_score": 78,
            "multimedia_score": 65,
            "has_newsletter": True,
            "social_sharing_enabled": True,
            "content_syndication": False,
            "google_rating": 4.3,
            "google_reviews": 127,
            "glassdoor_rating": 3.9,
            "trustpilot_rating": 4.1,
            "reclame_aqui_rating": 7.8,
            "brand_mentions": 342,
            "positive_mentions": 205,
            "neutral_mentions": 98,
            "negative_mentions": 39,
            "mention_sources": ["News sites", "Blogs", "Forums", "Social media"],
            "controversies": [],
            "complaint_trends": "decreasing",
            "response_rate": 85,
            "industry_mentions": 28,
            "media_features": ["TechCrunch", "Exame", "Valor Econômico"],
            "awards": ["Top 100 Startups 2024", "Best B2B Software"],
            "social_selling_index": 72,
            "influencer_score": 65
        }

    def test_init(self, service):
        """Test service initialization"""
        assert service.cost_per_analysis == 0.006
        assert service.service_name == "digital_presence"
        assert service.provider is None

    def test_init_with_provider(self, mock_provider):
        """Test initialization with provider"""
        service = DigitalPresenceService(provider=mock_provider)
        assert service.provider == mock_provider

    @pytest.mark.asyncio
    async def test_analyze_with_mock_data(self, service):
        """Test analyze with mock data"""
        result = await service.analyze("Test Company")

        assert isinstance(result, DigitalPresenceAnalysis)
        assert result.service_name == "digital_presence"
        assert result.cost == 0.006
        assert result.digital_maturity_score > 0
        assert len(result.recommendations) > 0
        assert result.confidence_score > 0

    @pytest.mark.asyncio
    async def test_analyze_with_provider(self, service_with_provider, mock_provider, sample_data):
        """Test analyze with provider"""
        mock_provider.search.return_value = sample_data

        result = await service_with_provider.analyze(
            "Test Company",
            {"website": "https://test.com"}
        )

        assert isinstance(result, DigitalPresenceAnalysis)
        mock_provider.search.assert_called_once()

        # Verify SEO metrics
        assert result.seo_metrics["domain_authority"] == 45
        assert result.seo_metrics["organic_traffic"]["monthly_estimate"] == 25000
        assert result.seo_metrics["keywords"]["ranking_keywords"] == 1250

        # Verify social media
        assert "linkedin" in result.social_media["platforms"]
        assert result.social_media["total_reach"] > 0

        # Verify website analysis
        assert result.website_analysis["performance"]["mobile_score"] == 85
        assert result.website_analysis["user_experience"]["bounce_rate"] == 42

    @pytest.mark.asyncio
    async def test_analyze_error_handling(self, service_with_provider, mock_provider):
        """Test error handling"""
        mock_provider.search.side_effect = Exception("API Error")

        with pytest.raises(ResearchException) as exc_info:
            await service_with_provider.analyze("Test Company")

        assert "Failed to analyze digital presence" in str(exc_info.value)

    def test_build_analysis_query(self, service):
        """Test query building"""
        query = service._build_analysis_query(
            "Test Company", {"website": "https://test.com"})

        assert "Test Company" in query
        assert "https://test.com" in query
        assert "SEO Performance" in query
        assert "Social Media Presence" in query
        assert "Website Analysis" in query

    def test_analyze_seo_metrics(self, service, sample_data):
        """Test SEO metrics extraction"""
        seo = service._analyze_seo_metrics(sample_data, None)

        assert seo["domain_authority"] == 45
        assert seo["organic_traffic"]["monthly_estimate"] == 25000
        assert seo["keywords"]["ranking_keywords"] == 1250
        assert seo["backlinks"]["total_backlinks"] == 5420
        assert seo["technical_seo"]["mobile_friendly"] is True
        assert seo["technical_seo"]["page_speed_score"] == 78

    def test_analyze_social_media(self, service, sample_data):
        """Test social media analysis"""
        social = service._analyze_social_media(sample_data, None)

        assert len(social["platforms"]) == 4
        assert social["platforms"]["linkedin"]["followers"] == 15420
        assert social["total_reach"] == 59300  # Sum of all followers
        # Has most followers
        assert social["most_active_platform"] == "facebook"
        assert social["average_engagement"] > 0

    def test_analyze_website(self, service, sample_data):
        """Test website analysis"""
        website = service._analyze_website(sample_data, None)

        assert website["performance"]["page_load_time"] == 2.3
        assert website["performance"]["mobile_score"] == 85
        assert website["user_experience"]["bounce_rate"] == 42
        assert website["technology"]["cms"] == "WordPress"
        assert website["content"]["total_pages"] == 145

    def test_analyze_content_strategy(self, service, sample_data):
        """Test content strategy analysis"""
        content = service._analyze_content_strategy(sample_data)

        assert content["blog"]["active"] is True
        assert content["blog"]["post_frequency"] == "weekly"
        assert content["content_types"]["videos"] is True
        assert content["content_types"]["podcasts"] is False
        assert content["content_quality"]["originality_score"] == 82

    def test_analyze_online_reputation(self, service, sample_data):
        """Test online reputation analysis"""
        reputation = service._analyze_online_reputation(sample_data)

        assert reputation["reviews"]["google_rating"] == 4.3
        assert reputation["brand_mentions"]["total_mentions"] == 342
        assert reputation["brand_mentions"]["sentiment_distribution"]["positive"] == 205
        assert reputation["crisis_indicators"]["response_rate"] == 85
        assert len(reputation["thought_leadership"]["awards"]) == 2

    def test_calculate_digital_maturity(self, service, sample_data):
        """Test digital maturity calculation"""
        seo = service._analyze_seo_metrics(sample_data, None)
        social = service._analyze_social_media(sample_data, None)
        website = service._analyze_website(sample_data, None)
        content = service._analyze_content_strategy(sample_data)
        reputation = service._analyze_online_reputation(sample_data)

        score = service._calculate_digital_maturity(
            seo, social, website, content, reputation)

        assert 0 <= score <= 100
        assert score > 50  # With good sample data, should be above average

    def test_score_seo_maturity(self, service):
        """Test SEO maturity scoring"""
        # Test with good SEO data
        good_seo = {
            "domain_authority": 70,
            "technical_seo": {
                "mobile_friendly": True,
                "https_enabled": True,
                "page_speed_score": 90
            },
            "keywords": {"ranking_keywords": 2000},
            "backlinks": {"referring_domains": 1000}
        }
        score = service._score_seo_maturity(good_seo)
        assert score > 70

        # Test with poor SEO data
        poor_seo = {
            "domain_authority": 10,
            "technical_seo": {
                "mobile_friendly": False,
                "https_enabled": False,
                "page_speed_score": 30
            },
            "keywords": {"ranking_keywords": 100},
            "backlinks": {"referring_domains": 10}
        }
        score = service._score_seo_maturity(poor_seo)
        assert score < 30

    def test_generate_recommendations(self, service):
        """Test recommendations generation"""
        # Test with poor metrics
        poor_data = {
            "domain_authority": 15,
            "technical_seo": {"page_speed_score": 40}
        }
        seo = poor_data
        social = {"platforms": {"linkedin": {}}, "average_engagement": 0.5}
        website = {"user_experience": {
            "bounce_rate": 75, "conversion_rate": 0.5}}
        content = {"blog": {"active": False}}
        reputation = {"reviews": {"google_rating": 3.2}}

        recommendations = service._generate_recommendations(
            seo, social, website, content, reputation
        )

        assert len(recommendations) > 0
        assert len(recommendations) <= 5
        assert any("autoridade do domínio" in r for r in recommendations)
        assert any("velocidade do site" in r for r in recommendations)

    def test_calculate_confidence_score(self, service, sample_data):
        """Test confidence score calculation"""
        seo = service._analyze_seo_metrics(sample_data, None)
        social = service._analyze_social_media(sample_data, None)
        website = service._analyze_website(sample_data, None)
        content = service._analyze_content_strategy(sample_data)
        reputation = service._analyze_online_reputation(sample_data)

        confidence = service._calculate_confidence_score(
            seo, social, website, content, reputation
        )

        assert 0 <= confidence <= 100
        assert confidence > 80  # With complete sample data

    def test_to_dict_conversion(self, service, sample_data):
        """Test DigitalPresenceAnalysis to_dict conversion"""
        seo = service._analyze_seo_metrics(sample_data, None)
        social = service._analyze_social_media(sample_data, None)
        website = service._analyze_website(sample_data, None)
        content = service._analyze_content_strategy(sample_data)
        reputation = service._analyze_online_reputation(sample_data)

        analysis = DigitalPresenceAnalysis(
            seo_metrics=seo,
            social_media=social,
            website_analysis=website,
            content_strategy=content,
            online_reputation=reputation,
            digital_maturity_score=75.5,
            recommendations=["Test recommendation"],
            confidence_score=85.0,
            raw_data=sample_data
        )

        result_dict = analysis.to_dict()

        assert result_dict["service_name"] == "digital_presence"
        assert result_dict["cost"] == 0.006
        assert result_dict["digital_maturity_score"] == 75.5
        assert result_dict["confidence_score"] == 85.0
        assert len(result_dict["recommendations"]) == 1
        assert "timestamp" in result_dict

    def test_empty_social_platforms(self, service):
        """Test handling of empty social platforms"""
        data = {}
        social = service._analyze_social_media(data, None)

        assert social["platforms"] == {}
        assert social["total_reach"] == 0
        assert social["most_active_platform"] == "none"
        assert social["average_engagement"] == 0

    def test_partial_data_handling(self, service):
        """Test handling of partial data"""
        partial_data = {
            "domain_authority": 30,
            "google_rating": 4.5,
            "has_blog": True
        }

        seo = service._analyze_seo_metrics(partial_data, None)
        assert seo["domain_authority"] == 30
        assert seo["organic_traffic"]["monthly_estimate"] == 0

        reputation = service._analyze_online_reputation(partial_data)
        assert reputation["reviews"]["google_rating"] == 4.5

    @pytest.mark.asyncio
    async def test_integration_flow(self, service):
        """Test complete integration flow"""
        # Run full analysis
        result = await service.analyze("Integration Test Company")

        # Verify all components are present
        assert hasattr(result, 'seo_metrics')
        assert hasattr(result, 'social_media')
        assert hasattr(result, 'website_analysis')
        assert hasattr(result, 'content_strategy')
        assert hasattr(result, 'online_reputation')
        assert hasattr(result, 'digital_maturity_score')
        assert hasattr(result, 'recommendations')
        assert hasattr(result, 'confidence_score')

        # Verify relationships between components
        dict_result = result.to_dict()
        assert isinstance(dict_result, dict)
        assert all(key in dict_result for key in [
            "seo_metrics", "social_media", "website_analysis",
            "content_strategy", "online_reputation"
        ])


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
