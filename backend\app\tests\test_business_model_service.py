"""
Testes unitários para o BusinessModelService.
"""
import pytest
from unittest.mock import Mock, AsyncMock
from decimal import Decimal
from datetime import datetime, timezone

from app.services.research.business_model_service import BusinessModelService
from app.core import ResearchRequest, ResearchResult


class TestBusinessModelService:
    """Testes para o BusinessModelService."""

    @pytest.fixture
    def mock_provider(self):
        """Cria um mock do provider."""
        provider = Mock()
        provider._mock_mode = True
        provider.search = AsyncMock()
        return provider

    @pytest.fixture
    def service(self, mock_provider):
        """Cria uma instância do serviço com provider mockado."""
        return BusinessModelService(mock_provider)

    @pytest.fixture
    def valid_request(self):
        """Cria uma requisição válida para testes."""
        return ResearchRequest(
            client_id="test_client_123",
            company_url="https://example.com",
            company_name="Example Corp",
            additional_context={
                "industry": "SaaS",
                "company_size": "50-200",
                "target_market": "B2B",
                "business_stage": "growth"
            }
        )

    def test_service_initialization(self, service):
        """Testa a inicialização do serviço."""
        assert service.get_name() == "business_model"
        assert service.get_version() == "1.0.0"
        assert service.get_cost() == Decimal("0.006")
        assert "Business Model Canvas" in service.get_description()

    def test_required_fields(self, service):
        """Testa os campos obrigatórios."""
        required = service.get_required_fields()
        assert "company_name" in required
        assert "company_url" in required
        assert len(required) == 2

    def test_optional_fields(self, service):
        """Testa os campos opcionais."""
        optional = service.get_optional_fields()
        assert "industry" in optional
        assert "company_size" in optional
        assert "target_market" in optional
        assert "business_stage" in optional
        assert len(optional) == 4

    @pytest.mark.asyncio
    async def test_validate_request_valid(self, service, valid_request):
        """Testa validação de requisição válida."""
        assert await service.validate_request(valid_request) is True

    @pytest.mark.asyncio
    async def test_validate_request_missing_name(self, service):
        """Testa validação com nome faltando."""
        request = ResearchRequest(
            client_id="test_client_123",
            company_url="https://example.com",
            company_name="",
            additional_context={}
        )
        assert await service.validate_request(request) is False

    @pytest.mark.asyncio
    async def test_validate_request_invalid_url(self, service):
        """Testa validação com URL inválida."""
        request = ResearchRequest(
            client_id="test_client_123",
            company_url="invalid-url",
            company_name="Example Corp",
            additional_context={}
        )
        assert await service.validate_request(request) is False

    @pytest.mark.asyncio
    async def test_execute_mock_mode(self, service, valid_request):
        """Testa execução em modo mock."""
        result = await service.execute(valid_request)

        assert result.success is True
        assert result.service_name == "business_model"
        assert result.cost == Decimal("0.006")
        assert result.data is not None
        assert "business_model_canvas" in result.data
        assert "distribution_model" in result.data
        assert "licensing_model" in result.data
        assert "target_audience" in result.data
        assert "revenue_model_analysis" in result.data
        assert "sustainability_analysis" in result.data
        assert "metadata" in result.data
        assert "recommendations" in result.data

    @pytest.mark.asyncio
    async def test_business_model_canvas_structure(self, service, valid_request):
        """Testa estrutura do Business Model Canvas."""
        result = await service.execute(valid_request)
        canvas = result.data["business_model_canvas"]

        # Verificar todos os 9 blocos
        assert "customer_segments" in canvas
        assert "value_propositions" in canvas
        assert "channels" in canvas
        assert "customer_relationships" in canvas
        assert "revenue_streams" in canvas
        assert "key_resources" in canvas
        assert "key_activities" in canvas
        assert "key_partnerships" in canvas
        assert "cost_structure" in canvas

        # Verificar estrutura de alguns blocos
        assert "primary" in canvas["customer_segments"]
        assert "secondary" in canvas["customer_segments"]
        assert "main_value" in canvas["value_propositions"]
        assert "key_benefits" in canvas["value_propositions"]

    @pytest.mark.asyncio
    async def test_distribution_model_structure(self, service, valid_request):
        """Testa estrutura do modelo de distribuição."""
        result = await service.execute(valid_request)
        dist_model = result.data["distribution_model"]

        assert "type" in dist_model
        assert "deployment" in dist_model
        assert "advantages" in dist_model
        assert "challenges" in dist_model
        assert dist_model["type"] in [
            "cloud", "on-premise", "hybrid", "unknown"]

    @pytest.mark.asyncio
    async def test_revenue_model_analysis(self, service, valid_request):
        """Testa análise do modelo de receita."""
        result = await service.execute(valid_request)
        revenue = result.data["revenue_model_analysis"]

        assert "current_models" in revenue
        assert "revenue_mix" in revenue
        assert "growth_drivers" in revenue
        assert "unit_economics" in revenue

        # Verificar unit economics
        unit_econ = revenue["unit_economics"]
        assert "cac" in unit_econ
        assert "ltv" in unit_econ
        assert "ltv_cac_ratio" in unit_econ
        assert "payback_period" in unit_econ

    @pytest.mark.asyncio
    async def test_metadata_fields(self, service, valid_request):
        """Testa campos de metadados."""
        result = await service.execute(valid_request)
        metadata = result.data["metadata"]

        assert "analysis_date" in metadata
        assert "company_url" in metadata
        assert "industry" in metadata
        assert "company_size" in metadata
        assert "target_market" in metadata
        assert "business_stage" in metadata
        assert "confidence_score" in metadata
        assert "business_model_maturity_score" in metadata

        # Verificar valores
        assert metadata["industry"] == "SaaS"
        assert metadata["company_size"] == "50-200"
        assert metadata["target_market"] == "B2B"
        assert 0 <= metadata["confidence_score"] <= 1
        assert 0 <= metadata["business_model_maturity_score"] <= 1

    @pytest.mark.asyncio
    async def test_recommendations_generation(self, service, valid_request):
        """Testa geração de recomendações."""
        result = await service.execute(valid_request)
        recommendations = result.data["recommendations"]

        assert isinstance(recommendations, list)
        assert len(recommendations) > 0
        assert len(recommendations) <= 5  # Máximo 5 recomendações

        # Verificar estrutura das recomendações
        for rec in recommendations:
            assert "area" in rec
            assert "suggestion" in rec
            assert "impact" in rec
            assert "effort" in rec
            assert "timeline" in rec
            assert rec["impact"] in ["high", "medium", "low"]
            assert rec["effort"] in ["high", "medium", "low"]

    @pytest.mark.asyncio
    async def test_mock_data_scenarios(self, service):
        """Testa diferentes cenários de mock data."""
        # Teste cenário SaaS B2B (default)
        request = ResearchRequest(
            client_id="test_123",
            company_url="https://saas-example.com",
            company_name="SaaS Example",
            additional_context={"industry": "SaaS", "target_market": "B2B"}
        )
        result = await service.execute(request)
        assert result.data["distribution_model"]["type"] == "cloud"
        assert result.data["target_audience"]["model"] == "B2B"

        # Teste cenário Marketplace
        request.company_name = "Marketplace Example"
        request.additional_context["industry"] = "marketplace"
        result = await service.execute(request)
        assert result.data["target_audience"]["model"] == "B2B2C"
        assert "Multi-sided platform" in result.data["distribution_model"]["deployment"]

        # Teste cenário Enterprise
        request.company_name = "Enterprise Corp"
        # Resetar industry
        request.additional_context["industry"] = "enterprise"
        request.additional_context["target_market"] = "B2B Enterprise"
        result = await service.execute(request)
        assert result.data["distribution_model"]["type"] == "hybrid"
        assert result.data["licensing_model"]["approach"] == "Perpetual + Subscription hybrid"

    @pytest.mark.asyncio
    async def test_confidence_score_calculation(self, service, valid_request):
        """Testa cálculo do score de confiança."""
        result = await service.execute(valid_request)
        confidence_score = result.confidence_score

        assert isinstance(confidence_score, float)
        assert 0 <= confidence_score <= 1
        assert confidence_score > 0.5  # Mock data deve ter boa confiança

    @pytest.mark.asyncio
    async def test_maturity_score_calculation(self, service, valid_request):
        """Testa cálculo do score de maturidade."""
        result = await service.execute(valid_request)
        maturity_score = result.data["metadata"]["business_model_maturity_score"]

        assert isinstance(maturity_score, float)
        assert 0 <= maturity_score <= 1
        assert maturity_score > 0.5  # Mock data representa empresas maduras

    @pytest.mark.asyncio
    async def test_empty_additional_context(self, service):
        """Testa com contexto adicional vazio."""
        request = ResearchRequest(
            client_id="test_123",
            company_url="https://example.com",
            company_name="Example Corp",
            additional_context=None
        )
        result = await service.execute(request)

        assert result.success is True
        assert result.data["metadata"]["industry"] == "não especificada"
        assert result.data["metadata"]["company_size"] == "não especificada"
        assert result.data["metadata"]["target_market"] == "B2B/B2C"
        assert result.data["metadata"]["business_stage"] == "growth"

    @pytest.mark.asyncio
    async def test_real_mode_execution(self, mock_provider, valid_request):
        """Testa execução em modo real (não mock)."""
        # Configure provider para modo real
        mock_provider._mock_mode = False
        mock_provider.search.return_value = {
            "business_model_canvas": {
                "customer_segments": {
                    "primary": ["SMBs"],
                    "secondary": [],
                    "characteristics": ["Digital-first"],
                    "size_estimate": "10K companies"
                },
                "value_propositions": {
                    "main_value": "Automation platform",
                    "key_benefits": ["Save time"],
                    "differentiators": ["AI-powered"]
                },
                "channels": {
                    "distribution": ["Online"],
                    "marketing": ["Content"],
                    "support": ["Chat"]
                },
                "customer_relationships": {
                    "type": "Automated",
                    "acquisition": "Inbound",
                    "retention": "Product-led",
                    "growth": "Upsell"
                },
                "revenue_streams": {
                    "primary": ["Subscription"],
                    "secondary": [],
                    "pricing_model": "SaaS"
                },
                "key_resources": {
                    "human": ["Engineers"],
                    "technological": ["Platform"],
                    "intellectual": ["Patents"]
                },
                "key_activities": {
                    "core": ["Development"],
                    "support": ["Support"]
                },
                "key_partnerships": {
                    "strategic": ["AWS"],
                    "suppliers": [],
                    "channels": []
                },
                "cost_structure": {
                    "fixed_costs": ["Salaries"],
                    "variable_costs": ["Cloud"],
                    "major_investments": ["R&D"]
                }
            },
            "distribution_model": {
                "type": "cloud",
                "deployment": ["SaaS"],
                "advantages": ["Scalable"],
                "challenges": ["Security"]
            },
            "licensing_model": {
                "type": "proprietary",
                "approach": "Subscription",
                "terms": ["Monthly"]
            },
            "target_audience": {
                "model": "B2B",
                "segments": ["SMB"],
                "geographic_focus": "Global",
                "expansion_potential": "High"
            },
            "revenue_model_analysis": {
                "current_models": ["SaaS"],
                "revenue_mix": {
                    "recurring": "90%",
                    "one_time": "10%",
                    "services": "0%"
                },
                "growth_drivers": ["New customers"],
                "unit_economics": {
                    "cac": "$1000",
                    "ltv": "$10000",
                    "ltv_cac_ratio": 10,
                    "payback_period": "3 months"
                }
            },
            "sustainability_analysis": {
                "financial_sustainability": "high",
                "competitive_moat": ["Technology"],
                "scalability": "high",
                "risks": ["Competition"]
            }
        }

        service = BusinessModelService(mock_provider)
        result = await service.execute(valid_request)

        assert result.success is True
        assert mock_provider.search.called
        assert result.data is not None
        assert "business_model_canvas" in result.data

    @pytest.mark.asyncio
    async def test_error_handling_json_decode(self, mock_provider, valid_request):
        """Testa tratamento de erro de JSON."""
        mock_provider._mock_mode = False
        mock_provider.search.return_value = "Invalid JSON {{{}}}"

        service = BusinessModelService(mock_provider)
        result = await service.execute(valid_request)

        # Deve retornar estrutura vazia mas válida
        assert result.success is True
        assert result.data is not None
        assert "business_model_canvas" in result.data

    @pytest.mark.asyncio
    async def test_error_handling_exception(self, mock_provider, valid_request):
        """Testa tratamento de exceções."""
        mock_provider._mock_mode = False
        mock_provider.search.side_effect = Exception("API Error")

        service = BusinessModelService(mock_provider)
        result = await service.execute(valid_request)

        assert result.success is False
        assert result.error == "API Error"
        assert result.cost == Decimal("0")
        assert result.confidence_score == 0.0

    def test_sample_output_structure(self, service):
        """Testa estrutura do exemplo de saída."""
        sample = service.get_sample_output()

        # Verificar estrutura principal
        assert "business_model_canvas" in sample
        assert "distribution_model" in sample
        assert "licensing_model" in sample
        assert "target_audience" in sample
        assert "revenue_model_analysis" in sample
        assert "sustainability_analysis" in sample
        assert "metadata" in sample
        assert "recommendations" in sample

        # Verificar que o sample é válido
        canvas = sample["business_model_canvas"]
        assert len(canvas) == 9  # 9 blocos do canvas

    @pytest.mark.asyncio
    async def test_processing_time_tracking(self, service, valid_request):
        """Testa rastreamento do tempo de processamento."""
        result = await service.execute(valid_request)

        assert hasattr(result, 'processing_time_seconds')
        assert isinstance(result.processing_time_seconds, float)
        assert result.processing_time_seconds >= 0  # Pode ser 0 em mocks rápidos
        assert result.processing_time_seconds < 10  # Mock deve ser rápido

    @pytest.mark.asyncio
    async def test_recommendations_impact_ordering(self, service, valid_request):
        """Testa ordenação de recomendações por impacto."""
        result = await service.execute(valid_request)
        recommendations = result.data["recommendations"]

        # Verificar que estão ordenadas por impacto
        impact_priority = {"high": 0, "medium": 1, "low": 2}
        for i in range(len(recommendations) - 1):
            current_impact = impact_priority[recommendations[i]["impact"]]
            next_impact = impact_priority[recommendations[i + 1]["impact"]]
            assert current_impact <= next_impact

    @pytest.mark.asyncio
    async def test_unit_economics_validation(self, service, valid_request):
        """Testa validação de unit economics."""
        result = await service.execute(valid_request)
        unit_econ = result.data["revenue_model_analysis"]["unit_economics"]

        # Verificar LTV/CAC ratio
        if isinstance(unit_econ["ltv_cac_ratio"], (int, float)):
            assert unit_econ["ltv_cac_ratio"] > 0

            # Se LTV/CAC < 3, deve haver recomendação de otimização
            if unit_econ["ltv_cac_ratio"] < 3:
                recommendations = result.data["recommendations"]
                unit_econ_recs = [
                    r for r in recommendations if r["area"] == "unit_economics_optimization"]
                assert len(unit_econ_recs) > 0
