"""
Captura de screenshots usando Playwright

Captura screenshots automatizados de websites em:
- Resolução desktop (1920x1080)
- Resolução mobile (375x667)
- Múltiplos browsers (Chromium, Firefox, Safari)

Versão melhorada com:
- Async/await
- Integração MongoDB GridFS
- Compressão de imagens
- Vinculação com cliente
"""

import os
import re
import io
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, List
from playwright.async_api import async_playwright
import uuid
import logging
from motor.motor_asyncio import AsyncIOMotorDatabase, AsyncIOMotorGridFSBucket
from bson import ObjectId
from PIL import Image

from .interfaces import ScreenshotData, ScreenshotMetadata, ViewportConfig

logger = logging.getLogger(__name__)


class PlaywrightScreenshots:
    """
    Capturador de screenshots usando Playwright

    Versão async com armazenamento em MongoDB GridFS
    """

    def __init__(self, db: AsyncIOMotorDatabase):
        """
        Inicializa o capturador de screenshots

        Args:
            db: Conexão MongoDB assíncrona
        """
        self.db = db
        self.collection = db.diagnostic_results
        self.gridfs = AsyncIOMotorGridFSBucket(db)

        # Viewports padrão
        self.desktop_viewport: ViewportConfig = {"width": 1920, "height": 1080}
        self.mobile_viewport: ViewportConfig = {"width": 375, "height": 667}

        # Configurações de compressão
        self.jpeg_quality = 85
        self.png_optimize = True

    async def capture_screenshots(
        self,
        url: str,
        client_id: str,
        browser_type: str = "chromium",
        compress: bool = True
    ) -> Dict[str, ScreenshotData]:
        """
        Captura screenshots desktop e mobile de uma URL

        Args:
            url: URL do website para captura
            client_id: ID do cliente para vinculação
            browser_type: Tipo de browser ("chromium", "firefox", "webkit")
            compress: Se deve comprimir as imagens

        Returns:
            Dict contendo dados dos screenshots salvos

        Raises:
            Exception: Se a URL for inválida ou houver erro na captura
        """
        if not self._is_valid_url(url):
            raise ValueError(f"URL inválida: {url}")

        logger.info(f"🎯 Iniciando captura de screenshots para {url}")

        async with async_playwright() as p:
            # Selecionar browser
            browser_launcher = getattr(p, browser_type)
            browser = await browser_launcher.launch(headless=True)

            try:
                # Capturar screenshot desktop
                desktop_data = await self._capture_desktop_screenshot(
                    browser, url, client_id, browser_type, compress
                )

                # Capturar screenshot mobile
                mobile_data = await self._capture_mobile_screenshot(
                    browser, url, client_id, browser_type, compress
                )

                # Salvar referências no banco
                await self._save_screenshot_references(
                    url, client_id, desktop_data, mobile_data
                )

                return {
                    "desktop": desktop_data,
                    "mobile": mobile_data
                }

            finally:
                await browser.close()

    def _is_valid_url(self, url: str) -> bool:
        """Valida se a URL está em formato correto"""
        if not url or not isinstance(url, str):
            return False

        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            # domain...
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)

        return bool(url_pattern.match(url))

    async def _capture_desktop_screenshot(
        self,
        browser,
        url: str,
        client_id: str,
        browser_type: str,
        compress: bool
    ) -> ScreenshotData:
        """
        Captura screenshot desktop e salva no GridFS

        Returns:
            Dados do screenshot salvo
        """
        page = await browser.new_page()
        await page.set_viewport_size(self.desktop_viewport)

        try:
            # Navegar para a URL
            await page.goto(url, wait_until="networkidle", timeout=30000)

            # Aguardar carregamento adicional
            await page.wait_for_timeout(2000)

            # Capturar screenshot
            screenshot_bytes = await page.screenshot(full_page=True)

            # Comprimir se solicitado
            if compress:
                screenshot_bytes = await self._compress_image(screenshot_bytes, "desktop")

                # Gerar metadata
            filename = f"desktop_{self._generate_filename(url)}"
            metadata = self._generate_metadata(url, browser_type, "desktop")

            # Salvar no GridFS
            grid_file_id = await self._save_to_gridfs(
                screenshot_bytes, filename, dict(metadata), client_id
            )

            logger.info(f"✅ Screenshot desktop salvo: {filename}")

            return ScreenshotData(
                filename=filename,
                grid_fs_id=grid_file_id,
                metadata=metadata,
                size_bytes=len(screenshot_bytes),
                mime_type="image/jpeg" if compress else "image/png"
            )

        finally:
            await page.close()

    async def _capture_mobile_screenshot(
        self,
        browser,
        url: str,
        client_id: str,
        browser_type: str,
        compress: bool
    ) -> ScreenshotData:
        """
        Captura screenshot mobile e salva no GridFS

        Returns:
            Dados do screenshot salvo
        """
        context = await browser.new_context(
            viewport=self.mobile_viewport,
            user_agent=(
                "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) "
                "AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 "
                "Mobile/15E148 Safari/604.1"
            )
        )

        page = await context.new_page()

        try:
            # Navegar para a URL
            await page.goto(url, wait_until="networkidle", timeout=30000)

            # Aguardar carregamento adicional
            await page.wait_for_timeout(2000)

            # Capturar screenshot
            screenshot_bytes = await page.screenshot(full_page=True)

            # Comprimir se solicitado
            if compress:
                screenshot_bytes = await self._compress_image(screenshot_bytes, "mobile")

                # Gerar metadata
            filename = f"mobile_{self._generate_filename(url)}"
            metadata = self._generate_metadata(url, browser_type, "mobile")

            # Salvar no GridFS
            grid_file_id = await self._save_to_gridfs(
                screenshot_bytes, filename, dict(metadata), client_id
            )

            logger.info(f"✅ Screenshot mobile salvo: {filename}")

            return ScreenshotData(
                filename=filename,
                grid_fs_id=grid_file_id,
                metadata=metadata,
                size_bytes=len(screenshot_bytes),
                mime_type="image/jpeg" if compress else "image/png"
            )

        finally:
            await context.close()

    async def _compress_image(self, image_bytes: bytes, viewport_type: str) -> bytes:
        """
        Comprime imagem para reduzir tamanho

        Args:
            image_bytes: Bytes da imagem original
            viewport_type: Tipo de viewport para ajustar compressão

        Returns:
            Bytes da imagem comprimida
        """
        try:
            # Abrir imagem com PIL
            image = Image.open(io.BytesIO(image_bytes))

            # Converter para RGB se necessário (JPEG não suporta transparência)
            if image.mode in ('RGBA', 'LA'):
                background = Image.new('RGB', image.size, (255, 255, 255))
                background.paste(image, mask=image.split()[-1])
                image = background

            # Redimensionar se muito grande (desktop apenas)
            if viewport_type == "desktop":
                max_width = 2560
                if image.width > max_width:
                    ratio = max_width / image.width
                    new_height = int(image.height * ratio)
                    image = image.resize(
                        (max_width, new_height), Image.Resampling.LANCZOS)

            # Salvar como JPEG comprimido
            output = io.BytesIO()
            image.save(output, format='JPEG',
                       quality=self.jpeg_quality, optimize=True)
            compressed_bytes = output.getvalue()

            # Log de compressão
            original_size = len(image_bytes)
            compressed_size = len(compressed_bytes)
            compression_ratio = (1 - compressed_size / original_size) * 100

            logger.debug(
                f"📦 Compressão {viewport_type}: {original_size:,} → {compressed_size:,} bytes "
                f"({compression_ratio:.1f}% redução)"
            )

            return compressed_bytes

        except Exception as e:
            logger.warning(
                f"Erro ao comprimir imagem: {str(e)}. Usando original.")
            return image_bytes

    def _generate_filename(self, url: str) -> str:
        """Gera nome único para arquivo de screenshot"""
        try:
            # Extrair domínio da URL
            domain = url.replace("https://", "").replace("http://", "")
            domain = domain.split("/")[0]
            domain = re.sub(r'[^\w\-.]', '_', domain)

            # Adicionar timestamp e UUID para unicidade
            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())[:8]

            return f"{domain}_{timestamp}_{unique_id}"

        except Exception:
            # Fallback para nome genérico
            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())[:8]
            return f"screenshot_{timestamp}_{unique_id}"

    def _generate_metadata(self, url: str, browser_type: str, viewport_type: str) -> ScreenshotMetadata:
        """Gera metadata para o screenshot"""
        viewport = self.desktop_viewport if viewport_type == "desktop" else self.mobile_viewport

        return ScreenshotMetadata(
            timestamp=datetime.now(timezone.utc),
            url=url,
            browser_type=browser_type,
            viewport=viewport,
            capture_settings={
                "full_page": True,
                "quality": self.jpeg_quality,
                "wait_until": "networkidle",
                "timeout": 30000,
                "additional_wait": 2000,
                "viewport_type": viewport_type
            }
        )

    async def _save_to_gridfs(
        self,
        data: bytes,
        filename: str,
        metadata: Dict[str, Any],
        client_id: str
    ) -> ObjectId:
        """
        Salva arquivo no GridFS

        Args:
            data: Bytes do arquivo
            filename: Nome do arquivo
            metadata: Metadata do arquivo
            client_id: ID do cliente

        Returns:
            ID do arquivo no GridFS
        """
        # AIDEV-NOTE: GridFS armazena arquivos grandes de forma eficiente
        grid_file_id = await self.gridfs.upload_from_stream(
            filename,
            data,
            metadata={
                **metadata,
                "client_id": ObjectId(client_id),
                "content_type": "image/jpeg" if filename.endswith('.jpg') else "image/png"
            }
        )

        return grid_file_id

    async def _save_screenshot_references(
        self,
        url: str,
        client_id: str,
        desktop_data: ScreenshotData,
        mobile_data: ScreenshotData
    ) -> None:
        """
        Salva referências dos screenshots no documento de diagnóstico

        Args:
            url: URL analisada
            client_id: ID do cliente
            desktop_data: Dados do screenshot desktop
            mobile_data: Dados do screenshot mobile
        """
        try:
            await self.collection.update_one(
                {
                    "client_id": ObjectId(client_id),
                    "url": url
                },
                {
                    "$set": {
                        "screenshots": {
                            "desktop": desktop_data,
                            "mobile": mobile_data
                        },
                        "screenshots_timestamp": datetime.now(timezone.utc)
                    },
                    "$setOnInsert": {
                        "client_id": ObjectId(client_id),
                        "url": url,
                        "timestamp": datetime.now(timezone.utc)
                    }
                },
                upsert=True
            )

            logger.info(
                f"✅ Referências de screenshots salvas para cliente {client_id}")

        except Exception as e:
            logger.error(f"Erro ao salvar referências: {str(e)}")

    async def get_screenshot(self, grid_file_id: ObjectId) -> Optional[bytes]:
        """
        Recupera screenshot do GridFS

        Args:
            grid_file_id: ID do arquivo no GridFS

        Returns:
            Bytes do arquivo ou None se não encontrado
        """
        try:
            # Download do GridFS
            stream = await self.gridfs.open_download_stream(grid_file_id)
            return await stream.read()

        except Exception as e:
            logger.error(f"Erro ao recuperar screenshot: {str(e)}")
            return None

    async def delete_old_screenshots(self, days: int = 30) -> int:
        """
        Remove screenshots antigos do GridFS

        Args:
            days: Número de dias para manter screenshots

        Returns:
            Número de arquivos removidos
        """
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)

            # Buscar arquivos antigos
            cursor = self.gridfs.find({
                "uploadDate": {"$lt": cutoff_date}
            })

            deleted_count = 0

            async for grid_file in cursor:
                await self.gridfs.delete(grid_file._id)
                deleted_count += 1

            logger.info(f"🗑️ {deleted_count} screenshots antigos removidos")
            return deleted_count

        except Exception as e:
            logger.error(f"Erro ao limpar screenshots: {str(e)}")
            return 0

    async def capture_multiple_browsers(
        self,
        url: str,
        client_id: str,
        browsers: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Captura screenshots em múltiplos browsers

        Args:
            url: URL para captura
            client_id: ID do cliente
            browsers: Lista de browsers (default: ["chromium", "firefox", "webkit"])

        Returns:
            Dict com screenshots de diferentes browsers
        """
        if not self._is_valid_url(url):
            raise ValueError(f"URL inválida: {url}")

        browsers = browsers or ["chromium", "firefox", "webkit"]
        results = {}

        for browser_type in browsers:
            try:
                result = await self.capture_screenshots(url, client_id, browser_type)
                results[browser_type] = result
            except Exception as e:
                logger.error(f"Erro ao capturar com {browser_type}: {str(e)}")
                results[browser_type] = {"error": str(e)}

        return results
