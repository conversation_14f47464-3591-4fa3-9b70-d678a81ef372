# [2025-06-29 21:17] Backend Refactoring - Clean Architecture

**PRIORITY**: P1 (Crítico)  
**ESTIMATE**: 140h (~3.5 semanas)  
**DUE**: 2025-07-21  
**TAGS**: [refactoring, architecture, clean-code, ddd, technical-debt]  
**BLOCKED_BY**: None

## 📋 Goal

Refatorar o backend monolítico do ScopeAI para Clean Architecture com Domain-Driven Design (DDD), quebrando 9 "God Files" com mais de 17.500 linhas totais em módulos coesos e testáveis.

## 🎯 Success Criteria

1. Nenhum arquivo com mais de 500 linhas
2. Cobertura de testes > 80%
3. Complexidade ciclomática < 10
4. Zero duplicação de código
5. Performance mantida ou melhorada
6. Zero breaking changes na API
7. Documentação completa de arquitetura

## 📝 Context

### Problemas Identificados
- **schemas.py**: 2.554 linhas (dezenas de models misturados)
- **parsers.py**: 2.878 linhas (8+ parsers diferentes)
- **routes.py**: 2.417 linhas (10+ responsabilidades)
- **perplexity.py**: 2.070 linhas (integração monolítica)
- **project_generation_routes.py**: 1.858 linhas
- **report_routes.py**: 1.670 linhas
- **pdf_generator_enhanced.py**: 1.724 linhas
- **html_report_generator.py**: 1.606 linhas
- **report_manager.py**: 1.146 linhas

### Arquitetura Alvo
```
backend/
├── src/
│   ├── domain/           # Entidades e regras de negócio
│   │   ├── models/       # Domain models e value objects
│   │   ├── events/       # Domain events
│   │   └── exceptions/   # Domain exceptions
│   ├── application/      # Casos de uso e serviços
│   │   ├── services/     # Application services
│   │   ├── use_cases/    # Use cases (interactors)
│   │   └── interfaces/   # Port interfaces
│   ├── infrastructure/   # Implementações externas
│   │   ├── database/     # MongoDB repositories
│   │   ├── external/     # APIs externas (Perplexity, etc)
│   │   ├── cache/        # Redis implementation
│   │   └── messaging/    # WebSocket, queues
│   └── api/              # Presentation layer
│       ├── v1/           # API version 1
│       │   ├── endpoints/# Controllers by domain
│       │   ├── middleware/# API middleware
│       │   └── schemas/  # API DTOs
│       └── dependencies/ # Dependency injection
```

## 🔧 Technical Approach

### Princípios a Seguir
1. **Single Responsibility Principle (SRP)**: Uma classe, uma responsabilidade
2. **Dependency Inversion**: Depender de abstrações, não implementações
3. **Domain-Driven Design**: Lógica de negócio no domínio
4. **Clean Architecture**: Dependências apontam para dentro
5. **Type Safety**: Type hints em 100% do código
6. **Async First**: Toda I/O deve ser assíncrona
7. **Test Driven**: Escrever testes antes do código

### Padrões de Implementação
```python
# Domain Model Example
from dataclasses import dataclass
from typing import Optional
from datetime import datetime

@dataclass(frozen=True)
class ClientId:
    """Value Object for Client ID"""
    value: str
    
    def __post_init__(self):
        if not self.value or len(self.value) != 24:
            raise ValueError("Invalid Client ID")

class Client:
    """Domain Entity"""
    def __init__(
        self, 
        id: ClientId,
        name: str,
        email: Email,
        created_at: datetime
    ):
        self._id = id
        self._name = name
        self._email = email
        self._created_at = created_at
        self._events = []
    
    def update_email(self, new_email: Email) -> None:
        """Business rule for email update"""
        if self._email == new_email:
            return
        
        old_email = self._email
        self._email = new_email
        self._events.append(
            ClientEmailUpdated(
                client_id=self._id,
                old_email=old_email,
                new_email=new_email,
                updated_at=datetime.now(UTC)
            )
        )

# Repository Interface
from abc import ABC, abstractmethod

class IClientRepository(ABC):
    @abstractmethod
    async def save(self, client: Client) -> None:
        pass
    
    @abstractmethod
    async def find_by_id(self, client_id: ClientId) -> Optional[Client]:
        pass

# Use Case Example
class UpdateClientEmailUseCase:
    def __init__(self, repository: IClientRepository):
        self._repository = repository
    
    async def execute(self, client_id: str, new_email: str) -> None:
        client = await self._repository.find_by_id(ClientId(client_id))
        if not client:
            raise ClientNotFoundError(client_id)
        
        client.update_email(Email(new_email))
        await self._repository.save(client)
```

---

## ✅ FASE 1: Foundation & Schemas Refactoring (40h)

### TAREFA 1.1: Setup Clean Architecture Structure
**Estimate**: 8h

Microtarefas:
1. [ ] Criar estrutura de diretórios src/domain, src/application, src/infrastructure, src/api
2. [ ] Configurar __init__.py com exports corretos em cada módulo
3. [ ] Criar src/shared/base_classes.py com Entity, ValueObject, DomainEvent, AggregateRoot
4. [ ] Criar src/shared/interfaces.py com IRepository, IUseCase, IService, IAdapter
5. [ ] Setup src/shared/exceptions.py com DomainError, ApplicationError, InfrastructureError
6. [ ] Configurar pytest fixtures em tests/conftest.py para nova estrutura
7. [ ] Atualizar imports no main.py para apontar para nova estrutura
8. [ ] Criar documentação inicial em docs/architecture/clean-architecture.md

### TAREFA 1.2: Extract Client Domain Models
**Estimate**: 8h

Microtarefas:
1. [ ] Analisar schemas.py e identificar todos os 15+ schemas relacionados a Client
2. [ ] Criar src/domain/models/client.py com classe Client (entity)
3. [ ] Criar src/domain/models/value_objects/client_vo.py com ClientId, Email, Phone, CNPJ
4. [ ] Implementar validações de domínio em cada value object
5. [ ] Criar src/domain/events/client_events.py com ClientCreated, ClientUpdated
6. [ ] Criar src/application/dto/client_dto.py com ClientCreateDTO, ClientUpdateDTO
7. [ ] Migrar Pydantic schemas para src/api/v1/schemas/client_schemas.py
8. [ ] Criar mapper em src/application/mappers/client_mapper.py
9. [ ] Escrever testes unitários em tests/unit/domain/models/test_client.py
10. [ ] Atualizar imports em routes.py para usar novos models

### TAREFA 1.3: Extract Project Domain Models
**Estimate**: 8h

Microtarefas:
1. [ ] Identificar os 20+ schemas relacionados a Project em schemas.py
2. [ ] Criar src/domain/models/project.py com classe Project (entity)
3. [ ] Criar src/domain/models/value_objects/project_vo.py com ProjectId, Money, Percentage
4. [ ] Criar src/domain/models/project_estimate.py para estimativas (value object complexo)
5. [ ] Implementar regras de negócio: can_start(), calculate_progress(), validate_estimate()
6. [ ] Criar src/domain/events/project_events.py com ProjectCreated, EstimateGenerated
7. [ ] Criar src/application/dto/project_dto.py com DTOs necessários
8. [ ] Migrar schemas para src/api/v1/schemas/project_schemas.py
9. [ ] Criar testes em tests/unit/domain/models/test_project.py
10. [ ] Verificar que project_generation_routes.py ainda funciona

### TAREFA 1.4: Extract Report Domain Models
**Estimate**: 8h

Microtarefas:
1. [ ] Mapear 30+ schemas de reports (DossierReport, MarketResearch, TechnicalDiagnostic, etc)
2. [ ] Criar src/domain/models/report/ como submodule com múltiplos arquivos
3. [ ] Criar report_base.py com classe abstrata Report
4. [ ] Criar dossier_report.py, market_research_report.py, technical_report.py
5. [ ] Implementar value objects específicos: Score, Metric, Insight
6. [ ] Criar factory pattern em src/domain/factories/report_factory.py
7. [ ] Migrar schemas para src/api/v1/schemas/report/ (submodule)
8. [ ] Criar testes para cada tipo de report
9. [ ] Garantir compatibilidade com report_routes.py

### TAREFA 1.5: Extract Analysis Models
**Estimate**: 8h

Microtarefas:
1. [ ] Identificar schemas de análise: FundingData, DigitalPresence, Partnerships, etc
2. [ ] Criar src/domain/models/analysis/ como submodule
3. [ ] Criar funding_analysis.py, digital_presence_analysis.py, partnerships_analysis.py
4. [ ] Criar business_model_analysis.py, pricing_analysis.py, market_analysis.py
5. [ ] Implementar validações específicas de cada análise
6. [ ] Criar value objects compartilhados: DateRange, ScoreRange, ConfidenceLevel
7. [ ] Migrar schemas relacionados
8. [ ] Criar suite de testes para análises
9. [ ] Verificar integração com parsers.py

---

## ✅ FASE 2: Services & Parsers Refactoring (32h)

### TAREFA 2.1: Create Parser Service Architecture
**Estimate**: 4h

Microtarefas:
1. [ ] Criar src/application/interfaces/parser_interface.py com IParser[T]
2. [ ] Criar src/application/services/parsers/base_parser.py com BaseParser abstrato
3. [ ] Definir ParseResult[T] como generic type para resultados
4. [ ] Criar ParserError hierarchy para tratamento de erros
5. [ ] Setup dependency injection pattern para parsers
6. [ ] Criar tests/unit/application/services/parsers/conftest.py

### TAREFA 2.2: Extract Funding Parser Service
**Estimate**: 4h

Microtarefas:
1. [ ] Analisar _processar_funding_data() e 11 funções relacionadas em parsers.py
2. [ ] Criar src/application/services/parsers/funding_parser.py
3. [ ] Implementar FundingParser: IParser[FundingAnalysis]
4. [ ] Mover todas as funções de normalização como métodos privados
5. [ ] Implementar cache com Redis para resultados
6. [ ] Criar testes com dados reais em tests/unit/application/services/parsers/test_funding_parser.py
7. [ ] Remover código do parsers.py original
8. [ ] Atualizar imports onde funding parser é usado

### TAREFA 2.3: Extract Digital Presence Parser
**Estimate**: 4h

Microtarefas:
1. [ ] Extrair _processar_presenca_digital_data() e 15+ funções relacionadas
2. [ ] Criar src/application/services/parsers/digital_presence_parser.py
3. [ ] Implementar tratamento de SEO metrics, social media, site analysis
4. [ ] Criar validações específicas para scores e métricas
5. [ ] Implementar fallbacks para dados faltantes
6. [ ] Criar suite de testes com mocks de APIs
7. [ ] Integrar com DigitalPresenceAnalysis domain model

### TAREFA 2.4: Extract Business Model Parser
**Estimate**: 4h

Microtarefas:
1. [ ] Extrair _processar_modelo_negocio_data() e funções relacionadas
2. [ ] Criar src/application/services/parsers/business_model_parser.py
3. [ ] Implementar parsing de unit economics, escalabilidade, métricas
4. [ ] Criar helpers para cálculos financeiros
5. [ ] Implementar validações de consistência
6. [ ] Criar testes com cenários de negócio
7. [ ] Remover do parsers.py original

### TAREFA 2.5: Extract Remaining Parsers
**Estimate**: 12h

Microtarefas:
1. [ ] Criar partnerships_parser.py (4h)
   - Extrair _processar_parcerias_data()
   - Implementar parsing de tipos, cases, métricas
2. [ ] Criar pricing_parser.py (4h)
   - Extrair _processar_pricing_data()
   - Implementar tiers, métricas avançadas
3. [ ] Criar market_research_parser.py (4h)
   - Extrair _processar_pesquisa_mercado_data()
   - Implementar insights competitivos

### TAREFA 2.6: Create Parser Factory & Registry
**Estimate**: 4h

Microtarefas:
1. [ ] Criar src/application/factories/parser_factory.py
2. [ ] Implementar ParserRegistry com auto-discovery
3. [ ] Criar get_parser(parser_type: ParserType) -> IParser
4. [ ] Implementar composite parser para análises completas
5. [ ] Criar testes de integração entre parsers
6. [ ] Documentar uso dos parsers

---

## ✅ FASE 3: API & Routes Refactoring (40h)

### TAREFA 3.1: Setup API Structure
**Estimate**: 4h

Microtarefas:
1. [ ] Criar src/api/v1/__init__.py com version info
2. [ ] Criar src/api/v1/endpoints/__init__.py
3. [ ] Criar src/api/dependencies/auth.py para autenticação
4. [ ] Criar src/api/dependencies/database.py para injeção do DB
5. [ ] Criar src/api/dependencies/services.py para injeção de serviços
6. [ ] Setup src/api/middleware/error_handler.py global
7. [ ] Criar src/api/v1/router.py para agregação de rotas

### TAREFA 3.2: Extract Client Controller
**Estimate**: 8h

Microtarefas:
1. [ ] Analisar routes.py e identificar os 8 endpoints de clients
2. [ ] Criar src/api/v1/endpoints/client_controller.py
3. [ ] Implementar create_client_quick() com injeção de ClientService
4. [ ] Implementar get_clients() com paginação e filtros
5. [ ] Implementar get_client_by_id() com cache
6. [ ] Implementar update_client() com validações
7. [ ] Implementar delete_client() com soft delete
8. [ ] Criar ClientService em src/application/services/client_service.py
9. [ ] Implementar testes de integração da API
10. [ ] Remover endpoints do routes.py original

### TAREFA 3.3: Extract Project Controller
**Estimate**: 8h

Microtarefas:
1. [ ] Criar src/api/v1/endpoints/project_controller.py
2. [ ] Mover generate_projects() com refatoração para use case
3. [ ] Mover create_project() com validações
4. [ ] Mover get_client_projects() com eager loading
5. [ ] Mover get_all_projects() com filtros avançados
6. [ ] Criar ProjectService e ProjectGenerationUseCase
7. [ ] Implementar WebSocket notifications via eventos
8. [ ] Criar testes end-to-end
9. [ ] Atualizar project_generation_routes.py para usar nova estrutura

### TAREFA 3.4: Extract Report Controller
**Estimate**: 8h

Microtarefas:
1. [ ] Criar src/api/v1/endpoints/report_controller.py
2. [ ] Mover complete_report endpoints
3. [ ] Mover download_report endpoints com streaming
4. [ ] Implementar report status endpoints
5. [ ] Criar ReportService com orchestration
6. [ ] Implementar background job handling limpo
7. [ ] Criar testes para geração assíncrona
8. [ ] Integrar com report_routes.py existente

### TAREFA 3.5: Extract Analysis Controller
**Estimate**: 6h

Microtarefas:
1. [ ] Criar src/api/v1/endpoints/analysis_controller.py
2. [ ] Mover endpoints de dossiê e pesquisa
3. [ ] Mover endpoints de análise técnica
4. [ ] Implementar cache inteligente
5. [ ] Criar AnalysisOrchestrator service
6. [ ] Implementar rate limiting por cliente

### TAREFA 3.6: WebSocket & Background Tasks
**Estimate**: 6h

Microtarefas:
1. [ ] Criar src/api/v1/endpoints/websocket_controller.py
2. [ ] Refatorar WebSocket manager para event-driven
3. [ ] Criar src/infrastructure/messaging/websocket_service.py
4. [ ] Implementar pub/sub pattern com Redis
5. [ ] Criar background task manager limpo
6. [ ] Implementar retry logic e dead letter queue

---

## ✅ FASE 4: External Services & Infrastructure (28h)

### TAREFA 4.1: Perplexity Adapter Implementation
**Estimate**: 8h

Microtarefas:
1. [ ] Criar src/application/interfaces/research_provider.py
2. [ ] Criar src/infrastructure/external/perplexity/perplexity_adapter.py
3. [ ] Implementar PerplexityAdapter: IResearchProvider
4. [ ] Extrair funções de perplexity.py mantendo assinaturas
5. [ ] Implementar retry logic com exponential backoff
6. [ ] Implementar cache com TTL configurável
7. [ ] Criar fallback provider para resiliência
8. [ ] Implementar testes com mocks
9. [ ] Documentar rate limits e custos

### TAREFA 4.2: Report Generators Refactoring
**Estimate**: 12h

Microtarefas:
1. [ ] Criar src/application/interfaces/report_generator.py
2. [ ] Criar src/infrastructure/reports/ structure
3. [ ] Refatorar pdf_generator_enhanced.py (4h)
   - Extrair em PDFGenerator: IReportGenerator
   - Quebrar em template engine + renderer
   - Implementar streaming generation
4. [ ] Refatorar html_report_generator.py (4h)
   - Extrair HTMLGenerator com templates
   - Implementar cache de templates
5. [ ] Refatorar report_manager.py (4h)
   - Criar ReportOrchestrator
   - Implementar factory pattern

### TAREFA 4.3: Database Repositories Implementation
**Estimate**: 8h

Microtarefas:
1. [ ] Criar src/infrastructure/database/mongodb/base_repository.py
2. [ ] Implementar ClientMongoRepository: IClientRepository
3. [ ] Implementar ProjectMongoRepository: IProjectRepository
4. [ ] Implementar ReportMongoRepository: IReportRepository
5. [ ] Criar connection pool manager
6. [ ] Implementar transaction support
7. [ ] Criar indexes programaticamente
8. [ ] Implementar testes com MongoDB em memória

---

## 🚧 Risks & Mitigations

### Risk 1: Breaking Changes na API
- **Impact**: Alto - Frontend para de funcionar
- **Mitigation**: 
  - Manter endpoints originais como deprecated
  - Implementar versionamento de API
  - Testes end-to-end extensivos
  - Feature flags para rollback

### Risk 2: Performance Degradation
- **Impact**: Médio - Sistema fica mais lento
- **Mitigation**:
  - Benchmark antes e depois
  - Implementar cache agressivo
  - Query optimization
  - Load testing

### Risk 3: Data Inconsistency Durante Migração
- **Impact**: Alto - Dados corrompidos
- **Mitigation**:
  - Migração em fases com validação
  - Backup completo antes
  - Scripts de rollback
  - Dry-run em staging

## 📊 Estimated Impact

### Qualidade de Código
- **Tamanho médio de arquivo**: De 1.944 para 300 linhas (-85%)
- **Complexidade ciclomática**: De 15 para 8 (-47%)
- **Duplicação de código**: De 8.2% para <3% (-63%)
- **Cobertura de testes**: De 73% para 90% (+23%)

### Produtividade
- **Tempo para adicionar feature**: -60% (de 5 dias para 2 dias)
- **Tempo para debugar**: -70% (melhor isolamento)
- **Onboarding novos devs**: -50% (arquitetura clara)

### Performance
- **Response time**: Mantido ou -10% (melhor cache)
- **Throughput**: +20% (melhor async)
- **Memory usage**: -15% (menos objetos grandes)

## 🔗 References

- [Clean Architecture - Uncle Bob](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)
- [Domain-Driven Design - Eric Evans](https://www.domainlanguage.com/ddd/)
- [FastAPI Best Practices](https://github.com/zhanymkanov/fastapi-best-practices)
- [Python Type Hints](https://docs.python.org/3/library/typing.html)
- Existing DDD implementation: `/backend/src/core/` (25% done)

## 📝 Implementation Notes

### Ordem de Implementação Recomendada
1. **Semana 1**: FASE 1 (Schemas) - Crítico, desbloqueia outras tarefas
2. **Semana 2**: FASE 2 (Parsers) - Reduz complexidade rapidamente  
3. **Semana 3**: FASE 3 (Routes) - Maior impacto na manutenibilidade
4. **Semana 4**: FASE 4 (External) - Polimento e otimizações

### Checkpoints de Validação
- [ ] Após cada FASE: Todos os testes passando
- [ ] Após cada FASE: Deploy em staging
- [ ] Após cada FASE: Code review com time
- [ ] Após cada FASE: Atualizar documentação

### Definição de Pronto (DoD)
- [ ] Código implementado seguindo padrões
- [ ] Testes unitários com cobertura > 80%
- [ ] Testes de integração para fluxos críticos
- [ ] Documentação atualizada
- [ ] Zero TODOs ou FIXMEs
- [ ] Performance validada
- [ ] Code review aprovado
- [ ] Deploy em staging funcionando

---

**Status**: 📝 TODO  
**Started**: -  
**Completed**: -  
**Score**: -/30  
**Actual Time**: - 