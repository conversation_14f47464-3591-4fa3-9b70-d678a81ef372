"""
Exceções customizadas para o sistema de pesquisa modular.
"""
from typing import Optional


class ResearchException(Exception):
    """Exceção base para erros relacionados a pesquisa."""
    pass


class ResearchServiceNotFoundError(ResearchException):
    """Lançada quando um serviço de pesquisa solicitado não existe."""

    def __init__(self, service_name: str):
        self.service_name = service_name
        super().__init__(f"Serviço de pesquisa não encontrado: {service_name}")


class ResearchValidationError(ResearchException):
    """Lançada quando a validação de uma requisição falha."""

    def __init__(self, service_name: str, reason: str):
        self.service_name = service_name
        self.reason = reason
        super().__init__(
            f"Validação falhou no serviço {service_name}: {reason}")


class ResearchProviderError(ResearchException):
    """Lançada quando há erro com o provedor externo."""

    def __init__(self, provider: str, reason: str):
        self.provider = provider
        self.reason = reason
        super().__init__(f"Erro no provedor {provider}: {reason}")


class ResearchRateLimitError(ResearchProviderError):
    """Lançada quando atingimos o rate limit do provedor."""

    def __init__(self, provider: str, retry_after: Optional[int] = None):
        self.retry_after = retry_after
        reason = f"Rate limit atingido"
        if retry_after:
            reason += f". Tente novamente em {retry_after} segundos"
        super().__init__(provider, reason)


class ResearchTimeoutError(ResearchException):
    """Lançada quando a pesquisa excede o tempo limite."""

    def __init__(self, service_name: str, timeout_seconds: int):
        self.service_name = service_name
        self.timeout_seconds = timeout_seconds
        super().__init__(
            f"Timeout no serviço {service_name} após {timeout_seconds}s")
