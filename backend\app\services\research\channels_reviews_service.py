"""
Channels Reviews Service - Análise de canais de comunicação e avaliações

Este serviço analisa os canais de comunicação, vendas e coleta reviews/avaliações
da empresa para avaliar efetividade e reputação online.

Custo: $0.005 por execução
"""
import json
import logging
from typing import Dict, Any, List, Optional
from decimal import Decimal
from datetime import datetime, timezone

from app.core import (
    IResearchService,
    ResearchRequest,
    ResearchResult,
    Timer
)

logger = logging.getLogger(__name__)


class ChannelsReviewsService(IResearchService):
    """
    Serviço especializado em análise de canais e reviews.

    Analisa e avalia:
    - Canais de comunicação (website, social media, email, telefone, chat)
    - Canais de venda (e-commerce, marketplaces, parceiros, vendas diretas)
    - Reviews e avaliações (Google, Trustpilot, Reclame Aqui, etc)
    - Análise de sentimento e padrões de feedback
    - Efetividade dos canais e reputação online
    """

    def __init__(self, provider):
        """
        Args:
            provider: Instância do provider (Perplexity, SearchSociety, etc)
        """
        self.provider = provider
        self._version = "1.0.0"
        self._cost = Decimal("0.005")

    def get_name(self) -> str:
        return "channels_reviews"

    def get_version(self) -> str:
        return self._version

    def get_description(self) -> str:
        return "Análise completa de canais de comunicação, vendas e reviews com scoring de efetividade e reputação"

    def get_cost(self) -> Decimal:
        return self._cost

    def get_required_fields(self) -> List[str]:
        return ["company_name", "company_url"]

    def get_optional_fields(self) -> List[str]:
        return ["industry", "company_size", "target_market", "primary_channel"]

    async def validate_request(self, request: ResearchRequest) -> bool:
        """Valida se a requisição tem os dados necessários."""
        if not request.company_name or not request.company_url:
            logger.error("Nome da empresa e URL são obrigatórios")
            return False

        # Validar formato da URL
        url = request.company_url.lower()
        if not url.startswith(('http://', 'https://')):
            logger.error(f"URL inválida: {request.company_url}")
            return False

        return True

    async def execute(self, request: ResearchRequest) -> ResearchResult:
        """Executa a análise de canais e reviews."""
        with Timer() as timer:
            try:
                empresa = request.company_name
                site = request.company_url

                # AIDEV-NOTE: Extrair campos opcionais com fallbacks inteligentes
                industria = getattr(request, 'industry', None)
                if industria is None and request.additional_context:
                    industria = request.additional_context.get(
                        'industry', 'não especificada')
                if industria is None:
                    industria = 'não especificada'

                company_size = getattr(
                    request, 'company_size', 'não especificada')
                if request.additional_context:
                    company_size = request.additional_context.get(
                        'company_size', company_size)

                target_market = getattr(request, 'target_market', 'B2B/B2C')
                if request.additional_context:
                    target_market = request.additional_context.get(
                        'target_market', target_market)

                primary_channel = getattr(
                    request, 'primary_channel', 'digital')
                if request.additional_context:
                    primary_channel = request.additional_context.get(
                        'primary_channel', primary_channel)

                # Mock mode para desenvolvimento
                if hasattr(self.provider, '_mock_mode') and self.provider._mock_mode:
                    logger.info(
                        f"[MOCK] Retornando análise de canais e reviews mock para {empresa}")
                    processed_data = self._get_mock_data(
                        empresa, industria, target_market, company_size)
                else:
                    # AIDEV-NOTE: Construir prompts especializados para análise de canais e reviews
                    system_prompt = self._build_system_prompt()
                    user_prompt = self._build_user_prompt(
                        empresa, site, industria, target_market, primary_channel)

                    logger.info(
                        f"Executando análise de canais e reviews para {empresa}")

                    # Fazer requisição ao provider
                    raw_result = await self.provider.search(
                        system_prompt=system_prompt,
                        user_prompt=user_prompt,
                        temperature=0.7,
                        max_tokens=2000
                    )

                    # Processar e estruturar resultado
                    processed_data = self._process_result(raw_result, empresa)

                # Calcular scores
                confidence_score = self._calculate_confidence_score(
                    processed_data)
                channel_effectiveness_score = self._calculate_channel_effectiveness_score(
                    processed_data)
                online_reputation_score = self._calculate_online_reputation_score(
                    processed_data)

                # Adicionar metadados
                processed_data['metadata'] = {
                    'analysis_date': datetime.now(timezone.utc).isoformat(),
                    'company_url': site,
                    'industry': industria,
                    'company_size': company_size,
                    'target_market': target_market,
                    'primary_channel': primary_channel,
                    'confidence_score': confidence_score,
                    'channel_effectiveness_score': channel_effectiveness_score,
                    'online_reputation_score': online_reputation_score
                }

                # Gerar recomendações
                processed_data['recommendations'] = self._generate_recommendations(
                    processed_data, industria, target_market
                )

                return ResearchResult(
                    service_name=self.get_name(),
                    service_version=self.get_version(),
                    timestamp=datetime.now(timezone.utc),
                    cost=self._cost,
                    success=True,
                    data=processed_data,
                    processing_time_seconds=timer.elapsed,
                    confidence_score=confidence_score
                )

            except json.JSONDecodeError as e:
                logger.error(f"Erro ao processar JSON: {str(e)}")
                return self._create_error_result(
                    "Erro ao processar resposta do modelo",
                    timer.elapsed
                )
            except Exception as e:
                logger.error(
                    f"Erro na análise de canais e reviews: {str(e)}", exc_info=True)
                return self._create_error_result(str(e), timer.elapsed)

    def get_sample_output(self) -> Dict[str, Any]:
        """Retorna exemplo de saída do serviço."""
        return {
            "communication_channels": {
                "website": {
                    "status": "active",
                    "effectiveness": "high",
                    "features": ["Responsive", "Live chat", "FAQ", "Blog"],
                    "issues": ["Slow load time", "Limited multilingual support"]
                },
                "social_media": {
                    "platforms": {
                        "linkedin": {"followers": "15.2K", "engagement": "high", "posts_per_week": 5},
                        "instagram": {"followers": "8.5K", "engagement": "medium", "posts_per_week": 3},
                        "facebook": {"followers": "5.1K", "engagement": "low", "posts_per_week": 2},
                        "twitter": {"followers": "3.2K", "engagement": "medium", "posts_per_week": 7}
                    },
                    "overall_presence": "strong",
                    "content_strategy": "Educational and product-focused"
                },
                "email": {
                    "newsletter": True,
                    "frequency": "weekly",
                    "estimated_subscribers": "10K-20K",
                    "open_rate_estimate": "industry average"
                },
                "phone": {
                    "available": True,
                    "hours": "Business hours",
                    "languages": ["Portuguese", "English"]
                },
                "chat": {
                    "live_chat": True,
                    "chatbot": True,
                    "availability": "24/7",
                    "ai_powered": True
                }
            },
            "sales_channels": {
                "primary_channels": [
                    {
                        "type": "direct_sales",
                        "contribution": "40%",
                        "effectiveness": "high"
                    },
                    {
                        "type": "website",
                        "contribution": "35%",
                        "effectiveness": "high"
                    },
                    {
                        "type": "partners",
                        "contribution": "25%",
                        "effectiveness": "medium"
                    }
                ],
                "e_commerce": {
                    "has_online_store": True,
                    "platform": "Custom",
                    "payment_methods": ["Credit card", "Boleto", "PIX"],
                    "shipping_options": ["Standard", "Express"]
                },
                "marketplaces": {
                    "present_in": ["Mercado Livre", "Amazon"],
                    "performance": "growing"
                },
                "sales_team": {
                    "inside_sales": True,
                    "field_sales": True,
                    "team_size_estimate": "20-50"
                }
            },
            "customer_reviews": {
                "google_reviews": {
                    "rating": 4.3,
                    "total_reviews": 127,
                    "recent_trend": "stable",
                    "response_rate": "85%"
                },
                "trustpilot": {
                    "rating": 4.1,
                    "total_reviews": 89,
                    "verified_company": True
                },
                "reclame_aqui": {
                    "rating": 7.8,
                    "reputation": "good",
                    "response_rate": "92%",
                    "resolution_rate": "78%"
                },
                "glassdoor": {
                    "rating": 4.0,
                    "recommend_to_friend": "75%",
                    "ceo_approval": "82%"
                },
                "industry_specific": {
                    "g2_crowd": {"rating": 4.4, "reviews": 45},
                    "capterra": {"rating": 4.5, "reviews": 38}
                }
            },
            "feedback_analysis": {
                "sentiment_distribution": {
                    "positive": "68%",
                    "neutral": "22%",
                    "negative": "10%"
                },
                "common_praises": [
                    "Excellent customer service",
                    "Product quality",
                    "Fast delivery",
                    "Innovation"
                ],
                "common_complaints": [
                    "Pricing",
                    "Limited features in basic plan",
                    "Technical support response time"
                ],
                "trending_topics": [
                    "New product launch",
                    "Mobile app improvements",
                    "Customer success stories"
                ],
                "company_responsiveness": {
                    "average_response_time": "within 24 hours",
                    "response_quality": "professional and helpful"
                }
            },
            "metadata": {
                "analysis_date": "2025-01-30T10:00:00Z",
                "company_url": "https://example.com",
                "industry": "SaaS",
                "company_size": "50-200",
                "target_market": "B2B",
                "primary_channel": "digital",
                "confidence_score": 0.87,
                "channel_effectiveness_score": 0.82,
                "online_reputation_score": 0.85
            },
            "recommendations": [
                {
                    "area": "social_media",
                    "suggestion": "Aumentar engajamento no Facebook com conteúdo mais interativo",
                    "impact": "medium",
                    "effort": "low",
                    "priority": "high"
                },
                {
                    "area": "reviews",
                    "suggestion": "Implementar programa de incentivo para reviews no Google",
                    "impact": "high",
                    "effort": "medium",
                    "priority": "high"
                }
            ]
        }

    def _build_system_prompt(self) -> str:
        """Constrói o prompt do sistema para análise de canais e reviews."""
        return """Você é um especialista em análise de canais de comunicação, vendas e reputação online com experiência em:
- Avaliação de efetividade de canais digitais e físicos
- Análise de presença em redes sociais e engajamento
- Monitoramento de reviews e avaliações online
- Análise de sentimento e feedback de clientes
- Estratégias omnichannel e customer experience
- E-commerce e marketplaces

Sua tarefa é analisar profundamente os canais de comunicação e vendas de uma empresa, considerando:
1. Todos os pontos de contato com o cliente
2. Efetividade e alcance de cada canal
3. Reviews e avaliações em múltiplas plataformas
4. Padrões de feedback e sentimento geral
5. Responsividade e gestão de reputação

Você deve fornecer uma análise objetiva e baseada em dados públicos disponíveis."""

    def _build_user_prompt(self, empresa: str, site: str, industria: str,
                           target_market: str, primary_channel: str) -> str:
        """Constrói o prompt do usuário com dados específicos."""
        return f"""Analise os canais de comunicação, vendas e reviews da empresa {empresa} ({site}).
Indústria: {industria}
Mercado-alvo: {target_market}
Canal principal: {primary_channel}

Forneça uma análise completa incluindo:

1. **Canais de Comunicação**:
   - Website: funcionalidades, efetividade, problemas
   - Redes sociais: presença, engajamento, estratégia de conteúdo
   - Email/Newsletter: frequência, alcance estimado
   - Telefone: disponibilidade, atendimento
   - Chat/Chatbot: tipo, disponibilidade, efetividade

2. **Canais de Venda**:
   - Canais primários e sua contribuição
   - E-commerce próprio: plataforma, funcionalidades
   - Marketplaces: presença e performance
   - Equipe de vendas: estrutura estimada

3. **Reviews e Avaliações**:
   - Google Reviews: nota, quantidade, tendência
   - Trustpilot: nota e verificação
   - Reclame Aqui: reputação, taxas de resposta/resolução
   - Glassdoor: satisfação dos funcionários
   - Plataformas específicas do setor (G2, Capterra, etc)

4. **Análise de Feedback**:
   - Distribuição de sentimento (positivo/neutro/negativo)
   - Elogios mais comuns
   - Reclamações recorrentes
   - Tópicos em tendência
   - Responsividade da empresa

IMPORTANTE:
- Baseie-se em informações públicas disponíveis
- Use estimativas realistas quando dados exatos não estiverem disponíveis
- Indique o nível de certeza nas análises
- Use "Não disponível" quando não houver informação

Formato de resposta JSON:
{{
    "communication_channels": {{
        "website": {{
            "status": "active/inactive",
            "effectiveness": "low/medium/high",
            "features": ["lista de recursos"],
            "issues": ["lista de problemas"]
        }},
        "social_media": {{
            "platforms": {{
                "linkedin": {{"followers": "número", "engagement": "low/medium/high", "posts_per_week": número}},
                "instagram": {{"followers": "número", "engagement": "low/medium/high", "posts_per_week": número}},
                "facebook": {{"followers": "número", "engagement": "low/medium/high", "posts_per_week": número}},
                "twitter": {{"followers": "número", "engagement": "low/medium/high", "posts_per_week": número}}
            }},
            "overall_presence": "weak/moderate/strong",
            "content_strategy": "descrição da estratégia"
        }},
        "email": {{
            "newsletter": true/false,
            "frequency": "frequência",
            "estimated_subscribers": "faixa estimada",
            "open_rate_estimate": "estimativa"
        }},
        "phone": {{
            "available": true/false,
            "hours": "horário de atendimento",
            "languages": ["idiomas"]
        }},
        "chat": {{
            "live_chat": true/false,
            "chatbot": true/false,
            "availability": "horário",
            "ai_powered": true/false
        }}
    }},
    "sales_channels": {{
        "primary_channels": [
            {{
                "type": "tipo do canal",
                "contribution": "percentual",
                "effectiveness": "low/medium/high"
            }}
        ],
        "e_commerce": {{
            "has_online_store": true/false,
            "platform": "plataforma utilizada",
            "payment_methods": ["métodos"],
            "shipping_options": ["opções"]
        }},
        "marketplaces": {{
            "present_in": ["lista de marketplaces"],
            "performance": "descrição"
        }},
        "sales_team": {{
            "inside_sales": true/false,
            "field_sales": true/false,
            "team_size_estimate": "faixa estimada"
        }}
    }},
    "customer_reviews": {{
        "google_reviews": {{
            "rating": nota_numerica,
            "total_reviews": numero,
            "recent_trend": "improving/stable/declining",
            "response_rate": "percentual"
        }},
        "trustpilot": {{
            "rating": nota_numerica,
            "total_reviews": numero,
            "verified_company": true/false
        }},
        "reclame_aqui": {{
            "rating": nota_numerica,
            "reputation": "bad/regular/good/great",
            "response_rate": "percentual",
            "resolution_rate": "percentual"
        }},
        "glassdoor": {{
            "rating": nota_numerica,
            "recommend_to_friend": "percentual",
            "ceo_approval": "percentual"
        }},
        "industry_specific": {{
            "platform_name": {{"rating": nota, "reviews": numero}}
        }}
    }},
    "feedback_analysis": {{
        "sentiment_distribution": {{
            "positive": "percentual",
            "neutral": "percentual",
            "negative": "percentual"
        }},
        "common_praises": ["lista de elogios"],
        "common_complaints": ["lista de reclamações"],
        "trending_topics": ["tópicos em alta"],
        "company_responsiveness": {{
            "average_response_time": "tempo médio",
            "response_quality": "descrição"
        }}
    }}
}}"""

    def _process_result(self, raw_data: Any, empresa: str) -> Dict[str, Any]:
        """Processa e valida o resultado da análise."""
        # Parse JSON se necessário
        if isinstance(raw_data, str):
            try:
                data = json.loads(raw_data)
            except json.JSONDecodeError as e:
                logger.warning(f"Resposta não é JSON válido para {empresa}")
                # Re-lança a exceção para ser capturada no método execute
                raise e
        else:
            data = raw_data

        # Validar e normalizar estrutura
        result = self._get_empty_channels_analysis()

        # AIDEV-NOTE: Processar cada seção garantindo estrutura consistente
        sections = [
            'communication_channels', 'sales_channels',
            'customer_reviews', 'feedback_analysis'
        ]

        for section in sections:
            if section in data and isinstance(data[section], dict):
                result[section] = self._normalize_section(
                    data[section], section)
            else:
                logger.warning(f"Seção {section} não encontrada ou inválida")

        return result

    def _normalize_section(self, section_data: Dict, section_name: str) -> Dict[str, Any]:
        """Normaliza dados de uma seção específica."""
        if section_name == 'communication_channels':
            return self._normalize_communication_channels(section_data)
        elif section_name == 'sales_channels':
            return self._normalize_sales_channels(section_data)
        elif section_name == 'customer_reviews':
            return self._normalize_customer_reviews(section_data)
        elif section_name == 'feedback_analysis':
            return self._normalize_feedback_analysis(section_data)
        else:
            return section_data

    def _normalize_communication_channels(self, channels_data: Dict) -> Dict[str, Any]:
        """Normaliza dados de canais de comunicação."""
        normalized = {
            'website': {
                'status': channels_data.get('website', {}).get('status', 'unknown'),
                'effectiveness': channels_data.get('website', {}).get('effectiveness', 'unknown'),
                'features': channels_data.get('website', {}).get('features', []),
                'issues': channels_data.get('website', {}).get('issues', [])
            },
            'social_media': channels_data.get('social_media', self._get_default_social_media()),
            'email': channels_data.get('email', self._get_default_email()),
            'phone': channels_data.get('phone', {'available': False}),
            'chat': channels_data.get('chat', {'live_chat': False, 'chatbot': False})
        }
        return normalized

    def _normalize_sales_channels(self, sales_data: Dict) -> Dict[str, Any]:
        """Normaliza dados de canais de venda."""
        return {
            'primary_channels': sales_data.get('primary_channels', []),
            'e_commerce': sales_data.get('e_commerce', {'has_online_store': False}),
            'marketplaces': sales_data.get('marketplaces', {'present_in': [], 'performance': 'unknown'}),
            'sales_team': sales_data.get('sales_team', {
                'inside_sales': False,
                'field_sales': False,
                'team_size_estimate': 'unknown'
            })
        }

    def _normalize_customer_reviews(self, reviews_data: Dict) -> Dict[str, Any]:
        """Normaliza dados de reviews."""
        normalized = {}

        # Plataformas padrão
        platforms = ['google_reviews', 'trustpilot',
                     'reclame_aqui', 'glassdoor']
        for platform in platforms:
            if platform in reviews_data:
                normalized[platform] = reviews_data[platform]
            else:
                normalized[platform] = self._get_default_review_platform(
                    platform)

        # Plataformas específicas da indústria
        normalized['industry_specific'] = reviews_data.get(
            'industry_specific', {})

        return normalized

    def _normalize_feedback_analysis(self, feedback_data: Dict) -> Dict[str, Any]:
        """Normaliza dados de análise de feedback."""
        sentiment = feedback_data.get('sentiment_distribution', {})

        # AIDEV-NOTE: Garantir que a distribuição de sentimento soma 100%
        if sentiment and all(k in sentiment for k in ['positive', 'neutral', 'negative']):
            try:
                total = float(sentiment['positive'].rstrip('%')) + \
                    float(sentiment['neutral'].rstrip('%')) + \
                    float(sentiment['negative'].rstrip('%'))
                if abs(total - 100) > 1:  # Tolerância de 1%
                    sentiment = {'positive': '33%',
                                 'neutral': '34%', 'negative': '33%'}
            except:
                sentiment = {'positive': '33%',
                             'neutral': '34%', 'negative': '33%'}
        else:
            sentiment = {'positive': '33%',
                         'neutral': '34%', 'negative': '33%'}

        return {
            'sentiment_distribution': sentiment,
            'common_praises': feedback_data.get('common_praises', []),
            'common_complaints': feedback_data.get('common_complaints', []),
            'trending_topics': feedback_data.get('trending_topics', []),
            'company_responsiveness': feedback_data.get('company_responsiveness', {
                'average_response_time': 'unknown',
                'response_quality': 'unknown'
            })
        }

    def _calculate_confidence_score(self, data: Dict[str, Any]) -> float:
        """Calcula score de confiança baseado na completude dos dados."""
        scores = []

        # Communication channels completeness
        comm_channels = data.get('communication_channels', {})
        if comm_channels:
            channel_count = sum(1 for k, v in comm_channels.items()
                                if v and k != 'unknown')
            scores.append(min(channel_count / 5, 1.0))

        # Sales channels completeness
        sales_channels = data.get('sales_channels', {})
        if sales_channels.get('primary_channels'):
            scores.append(0.9)
        elif sales_channels:
            scores.append(0.5)

        # Reviews completeness
        reviews = data.get('customer_reviews', {})
        if reviews:
            review_count = sum(1 for k, v in reviews.items()
                               if isinstance(v, dict) and v.get('rating'))
            scores.append(min(review_count / 4, 1.0))

        # Feedback analysis completeness
        feedback = data.get('feedback_analysis', {})
        if feedback.get('sentiment_distribution') and feedback.get('common_praises'):
            scores.append(0.9)
        elif feedback:
            scores.append(0.5)

        return round(sum(scores) / len(scores) if scores else 0.5, 2)

    def _calculate_channel_effectiveness_score(self, data: Dict[str, Any]) -> float:
        """Calcula score de efetividade dos canais (0-100)."""
        score_components = []
        weights = {
            'communication': 0.30,
            'sales': 0.25,
            'integration': 0.25,
            'responsiveness': 0.20
        }

        # Communication channels score
        comm_score = 0
        comm_channels = data.get('communication_channels', {})

        # Website effectiveness
        website = comm_channels.get('website', {})
        if website.get('effectiveness') == 'high':
            comm_score += 30
        elif website.get('effectiveness') == 'medium':
            comm_score += 20
        elif website.get('effectiveness') == 'low':
            comm_score += 10

        # Social media presence
        social = comm_channels.get('social_media', {})
        if social.get('overall_presence') == 'strong':
            comm_score += 30
        elif social.get('overall_presence') == 'moderate':
            comm_score += 20
        elif social.get('overall_presence') == 'weak':
            comm_score += 10

        # Other channels
        if comm_channels.get('email', {}).get('newsletter'):
            comm_score += 10
        if comm_channels.get('phone', {}).get('available'):
            comm_score += 10
        if comm_channels.get('chat', {}).get('live_chat') or comm_channels.get('chat', {}).get('chatbot'):
            comm_score += 20

        score_components.append(min(comm_score, 100) *
                                weights['communication'])

        # Sales channels score
        sales_score = 0
        sales_channels = data.get('sales_channels', {})

        # Primary channels diversity
        primary_channels = sales_channels.get('primary_channels', [])
        if len(primary_channels) >= 3:
            sales_score += 40
        elif len(primary_channels) >= 2:
            sales_score += 25
        elif len(primary_channels) >= 1:
            sales_score += 15

        # E-commerce presence
        if sales_channels.get('e_commerce', {}).get('has_online_store'):
            sales_score += 30

        # Marketplace presence
        marketplaces = sales_channels.get(
            'marketplaces', {}).get('present_in', [])
        if len(marketplaces) >= 2:
            sales_score += 20
        elif len(marketplaces) >= 1:
            sales_score += 10

        # Sales team
        if sales_channels.get('sales_team', {}).get('inside_sales'):
            sales_score += 10

        score_components.append(min(sales_score, 100) * weights['sales'])

        # Integration score (omnichannel)
        integration_score = 0
        channel_count = 0

        # Count active channels
        if website.get('status') == 'active':
            channel_count += 1
        if social.get('platforms'):
            active_social = sum(1 for p in social['platforms'].values()
                                if p.get('followers') and p['followers'] != '0')
            channel_count += min(active_social, 3)
        if comm_channels.get('email', {}).get('newsletter'):
            channel_count += 1
        if sales_channels.get('e_commerce', {}).get('has_online_store'):
            channel_count += 1

        if channel_count >= 5:
            integration_score = 100
        elif channel_count >= 3:
            integration_score = 70
        elif channel_count >= 2:
            integration_score = 40
        else:
            integration_score = 20

        score_components.append(integration_score * weights['integration'])

        # Responsiveness score
        responsiveness_score = 0
        feedback = data.get('feedback_analysis', {}).get(
            'company_responsiveness', {})

        if 'within 24 hours' in feedback.get('average_response_time', ''):
            responsiveness_score += 50
        elif 'within 48 hours' in feedback.get('average_response_time', ''):
            responsiveness_score += 30
        elif 'within' in feedback.get('average_response_time', ''):
            responsiveness_score += 20

        if 'professional' in feedback.get('response_quality', '').lower() or \
           'helpful' in feedback.get('response_quality', '').lower():
            responsiveness_score += 50
        elif feedback.get('response_quality') != 'unknown':
            responsiveness_score += 25

        score_components.append(responsiveness_score *
                                weights['responsiveness'])

        # Calculate final score
        final_score = sum(score_components)
        return round(final_score / 100, 2)

    def _calculate_online_reputation_score(self, data: Dict[str, Any]) -> float:
        """Calcula score de reputação online (0-100)."""
        reputation_components = []
        weights = {
            'reviews': 0.35,
            'sentiment': 0.25,
            'responsiveness': 0.20,
            'volume': 0.20
        }

        # Reviews score
        reviews_scores = []
        reviews = data.get('customer_reviews', {})

        # Google Reviews (mais peso)
        google = reviews.get('google_reviews', {})
        if google.get('rating'):
            normalized_score = (google['rating'] / 5.0) * 100
            reviews_scores.append(normalized_score * 1.5)  # Peso extra

        # Outras plataformas
        for platform in ['trustpilot', 'glassdoor']:
            platform_data = reviews.get(platform, {})
            if platform_data.get('rating'):
                normalized_score = (platform_data['rating'] / 5.0) * 100
                reviews_scores.append(normalized_score)

        # Reclame Aqui
        reclame_aqui = reviews.get('reclame_aqui', {})
        if reclame_aqui.get('rating'):
            normalized_score = (reclame_aqui['rating'] / 10.0) * 100
            reviews_scores.append(normalized_score)

        if reviews_scores:
            avg_review_score = sum(reviews_scores) / len(reviews_scores)
            reputation_components.append(avg_review_score * weights['reviews'])

        # Sentiment score
        sentiment = data.get('feedback_analysis', {}).get(
            'sentiment_distribution', {})
        if sentiment:
            try:
                positive = float(sentiment.get('positive', '0').rstrip('%'))
                neutral = float(sentiment.get('neutral', '0').rstrip('%'))
                negative = float(sentiment.get('negative', '0').rstrip('%'))

                # Fórmula ponderada: positive + (neutral * 0.5) - (negative * 2)
                sentiment_score = positive + (neutral * 0.5) - (negative * 2)
                sentiment_score = max(0, min(100, sentiment_score))

                reputation_components.append(
                    sentiment_score * weights['sentiment'])
            except:
                reputation_components.append(50 * weights['sentiment'])

        # Responsiveness score
        responsiveness_score = 0

        # Response rates from reviews
        if google.get('response_rate'):
            try:
                rate = float(google['response_rate'].rstrip('%'))
                responsiveness_score += rate * 0.5
            except:
                pass

        if reclame_aqui.get('response_rate'):
            try:
                rate = float(reclame_aqui['response_rate'].rstrip('%'))
                responsiveness_score += rate * 0.3
            except:
                pass

        if reclame_aqui.get('resolution_rate'):
            try:
                rate = float(reclame_aqui['resolution_rate'].rstrip('%'))
                responsiveness_score += rate * 0.2
            except:
                pass

        reputation_components.append(
            responsiveness_score * weights['responsiveness'])

        # Volume score (number of reviews indicates engagement)
        total_reviews = 0

        for platform_data in reviews.values():
            if isinstance(platform_data, dict) and platform_data.get('total_reviews'):
                total_reviews += platform_data['total_reviews']

        # Industry specific reviews
        industry_reviews = reviews.get('industry_specific', {})
        for platform_data in industry_reviews.values():
            if isinstance(platform_data, dict) and platform_data.get('reviews'):
                total_reviews += platform_data['reviews']

        # Score based on volume (logarithmic scale)
        if total_reviews >= 1000:
            volume_score = 100
        elif total_reviews >= 500:
            volume_score = 90
        elif total_reviews >= 200:
            volume_score = 80
        elif total_reviews >= 100:
            volume_score = 70
        elif total_reviews >= 50:
            volume_score = 60
        elif total_reviews >= 20:
            volume_score = 50
        elif total_reviews >= 10:
            volume_score = 40
        else:
            volume_score = 30

        reputation_components.append(volume_score * weights['volume'])

        # Calculate final score
        final_score = sum(reputation_components)
        return round(final_score / 100, 2)

    def _generate_recommendations(self, data: Dict[str, Any],
                                  industria: str, target_market: str) -> List[Dict[str, Any]]:
        """Gera recomendações baseadas na análise."""
        recommendations = []

        # Analisar gaps nos canais de comunicação
        comm_channels = data.get('communication_channels', {})

        # Website recommendations
        website = comm_channels.get('website', {})
        if website.get('effectiveness') != 'high':
            if 'Slow load time' in website.get('issues', []):
                recommendations.append({
                    'area': 'website_performance',
                    'suggestion': 'Otimizar velocidade de carregamento do site para melhorar experiência do usuário',
                    'impact': 'high',
                    'effort': 'medium',
                    'priority': 'high'
                })

        # Social media recommendations
        social = comm_channels.get('social_media', {})
        if social.get('overall_presence') != 'strong':
            low_engagement_platforms = []
            for platform, data in social.get('platforms', {}).items():
                if data.get('engagement') == 'low':
                    low_engagement_platforms.append(platform)

            if low_engagement_platforms:
                recommendations.append({
                    'area': 'social_media_engagement',
                    'suggestion': f'Melhorar estratégia de conteúdo em {", ".join(low_engagement_platforms)} para aumentar engajamento',
                    'impact': 'medium',
                    'effort': 'low',
                    'priority': 'high'
                })

        # Chat/Support recommendations
        chat = comm_channels.get('chat', {})
        if not chat.get('live_chat') and not chat.get('chatbot'):
            recommendations.append({
                'area': 'customer_support',
                'suggestion': 'Implementar chat ao vivo ou chatbot para atendimento instantâneo',
                'impact': 'high',
                'effort': 'medium',
                'priority': 'medium'
            })

        # Sales channel recommendations
        sales_channels = data.get('sales_channels', {})
        if not sales_channels.get('e_commerce', {}).get('has_online_store') and target_market in ['B2C', 'B2B2C']:
            recommendations.append({
                'area': 'sales_channels',
                'suggestion': 'Implementar e-commerce próprio para vendas diretas online',
                'impact': 'high',
                'effort': 'high',
                'priority': 'high'
            })

        # Review management recommendations
        reviews = data.get('customer_reviews', {})
        google_reviews = reviews.get('google_reviews', {})

        if google_reviews.get('total_reviews', 0) < 50:
            recommendations.append({
                'area': 'review_generation',
                'suggestion': 'Criar programa de incentivo para aumentar volume de reviews no Google',
                'impact': 'high',
                'effort': 'low',
                'priority': 'high'
            })

        if google_reviews.get('response_rate', '0%').rstrip('%') != '100':
            recommendations.append({
                'area': 'review_management',
                'suggestion': 'Responder 100% dos reviews para demonstrar comprometimento com clientes',
                'impact': 'medium',
                'effort': 'low',
                'priority': 'medium'
            })

        # Sentiment recommendations
        sentiment = data.get('feedback_analysis', {}).get(
            'sentiment_distribution', {})
        try:
            negative_pct = float(sentiment.get('negative', '0').rstrip('%'))
            if negative_pct > 15:
                common_complaints = data.get(
                    'feedback_analysis', {}).get('common_complaints', [])
                if common_complaints:
                    recommendations.append({
                        'area': 'customer_satisfaction',
                        'suggestion': f'Criar plano de ação para resolver principais reclamações: {", ".join(common_complaints[:2])}',
                        'impact': 'high',
                        'effort': 'medium',
                        'priority': 'high'
                    })
        except:
            pass

        # Sort by priority and return top recommendations
        recommendations.sort(key=lambda x: (
            x['priority'] == 'high',
            x['impact'] == 'high',
            x['effort'] == 'low'
        ), reverse=True)

        return recommendations[:5]  # Top 5 recommendations

    def _get_default_social_media(self) -> Dict[str, Any]:
        """Retorna estrutura padrão para redes sociais."""
        return {
            'platforms': {
                'linkedin': {'followers': '0', 'engagement': 'unknown', 'posts_per_week': 0},
                'instagram': {'followers': '0', 'engagement': 'unknown', 'posts_per_week': 0},
                'facebook': {'followers': '0', 'engagement': 'unknown', 'posts_per_week': 0},
                'twitter': {'followers': '0', 'engagement': 'unknown', 'posts_per_week': 0}
            },
            'overall_presence': 'unknown',
            'content_strategy': 'Not identified'
        }

    def _get_default_email(self) -> Dict[str, Any]:
        """Retorna estrutura padrão para email."""
        return {
            'newsletter': False,
            'frequency': 'unknown',
            'estimated_subscribers': 'unknown',
            'open_rate_estimate': 'unknown'
        }

    def _get_default_review_platform(self, platform: str) -> Dict[str, Any]:
        """Retorna estrutura padrão para plataforma de review."""
        if platform == 'google_reviews':
            return {
                'rating': 0,
                'total_reviews': 0,
                'recent_trend': 'unknown',
                'response_rate': '0%'
            }
        elif platform == 'trustpilot':
            return {
                'rating': 0,
                'total_reviews': 0,
                'verified_company': False
            }
        elif platform == 'reclame_aqui':
            return {
                'rating': 0,
                'reputation': 'unknown',
                'response_rate': '0%',
                'resolution_rate': '0%'
            }
        elif platform == 'glassdoor':
            return {
                'rating': 0,
                'recommend_to_friend': '0%',
                'ceo_approval': '0%'
            }
        else:
            return {}

    def _get_empty_channels_analysis(self) -> Dict[str, Any]:
        """Retorna estrutura vazia para análise de canais."""
        return {
            'communication_channels': {
                'website': {
                    'status': 'unknown',
                    'effectiveness': 'unknown',
                    'features': [],
                    'issues': []
                },
                'social_media': self._get_default_social_media(),
                'email': self._get_default_email(),
                'phone': {'available': False},
                'chat': {'live_chat': False, 'chatbot': False}
            },
            'sales_channels': {
                'primary_channels': [],
                'e_commerce': {'has_online_store': False},
                'marketplaces': {'present_in': [], 'performance': 'unknown'},
                'sales_team': {
                    'inside_sales': False,
                    'field_sales': False,
                    'team_size_estimate': 'unknown'
                }
            },
            'customer_reviews': {
                'google_reviews': self._get_default_review_platform('google_reviews'),
                'trustpilot': self._get_default_review_platform('trustpilot'),
                'reclame_aqui': self._get_default_review_platform('reclame_aqui'),
                'glassdoor': self._get_default_review_platform('glassdoor'),
                'industry_specific': {}
            },
            'feedback_analysis': {
                'sentiment_distribution': {
                    'positive': '33%',
                    'neutral': '34%',
                    'negative': '33%'
                },
                'common_praises': [],
                'common_complaints': [],
                'trending_topics': [],
                'company_responsiveness': {
                    'average_response_time': 'unknown',
                    'response_quality': 'unknown'
                }
            }
        }

    def _get_mock_data(self, empresa: str, industria: str,
                       target_market: str, company_size: str) -> Dict[str, Any]:
        """Retorna dados mock para desenvolvimento."""
        # Selecionar mock baseado em critérios
        if 'e-commerce' in industria.lower() or 'retail' in industria.lower():
            return self._get_ecommerce_mock()
        elif target_market == 'B2B' and ('enterprise' in company_size.lower() or '500+' in company_size):
            return self._get_b2b_enterprise_mock()
        else:
            return self._get_default_mock()

    def _get_default_mock(self) -> Dict[str, Any]:
        """Mock padrão para empresa típica."""
        return {
            "communication_channels": {
                "website": {
                    "status": "active",
                    "effectiveness": "medium",
                    "features": ["Responsive design", "Contact form", "Product catalog", "About us"],
                    "issues": ["No live chat", "Limited search functionality"]
                },
                "social_media": {
                    "platforms": {
                        "linkedin": {"followers": "2.5K", "engagement": "medium", "posts_per_week": 3},
                        "instagram": {"followers": "1.2K", "engagement": "high", "posts_per_week": 5},
                        "facebook": {"followers": "800", "engagement": "low", "posts_per_week": 2},
                        "twitter": {"followers": "450", "engagement": "low", "posts_per_week": 1}
                    },
                    "overall_presence": "moderate",
                    "content_strategy": "Mix of product updates and industry news"
                },
                "email": {
                    "newsletter": True,
                    "frequency": "monthly",
                    "estimated_subscribers": "1K-5K",
                    "open_rate_estimate": "20-25%"
                },
                "phone": {
                    "available": True,
                    "hours": "Mon-Fri 9AM-6PM",
                    "languages": ["Portuguese"]
                },
                "chat": {
                    "live_chat": False,
                    "chatbot": True,
                    "availability": "24/7",
                    "ai_powered": False
                }
            },
            "sales_channels": {
                "primary_channels": [
                    {
                        "type": "direct_sales",
                        "contribution": "60%",
                        "effectiveness": "high"
                    },
                    {
                        "type": "website",
                        "contribution": "30%",
                        "effectiveness": "medium"
                    },
                    {
                        "type": "phone",
                        "contribution": "10%",
                        "effectiveness": "low"
                    }
                ],
                "e_commerce": {
                    "has_online_store": True,
                    "platform": "WooCommerce",
                    "payment_methods": ["Credit card", "Boleto"],
                    "shipping_options": ["Standard", "Express"]
                },
                "marketplaces": {
                    "present_in": [],
                    "performance": "not applicable"
                },
                "sales_team": {
                    "inside_sales": True,
                    "field_sales": False,
                    "team_size_estimate": "5-10"
                }
            },
            "customer_reviews": {
                "google_reviews": {
                    "rating": 4.2,
                    "total_reviews": 47,
                    "recent_trend": "stable",
                    "response_rate": "60%"
                },
                "trustpilot": {
                    "rating": 0,
                    "total_reviews": 0,
                    "verified_company": False
                },
                "reclame_aqui": {
                    "rating": 7.2,
                    "reputation": "good",
                    "response_rate": "85%",
                    "resolution_rate": "70%"
                },
                "glassdoor": {
                    "rating": 3.8,
                    "recommend_to_friend": "65%",
                    "ceo_approval": "72%"
                },
                "industry_specific": {}
            },
            "feedback_analysis": {
                "sentiment_distribution": {
                    "positive": "62%",
                    "neutral": "25%",
                    "negative": "13%"
                },
                "common_praises": [
                    "Good customer service",
                    "Quality products",
                    "Fair pricing"
                ],
                "common_complaints": [
                    "Delivery delays",
                    "Limited payment options",
                    "Website navigation"
                ],
                "trending_topics": [
                    "New product line",
                    "Shipping improvements"
                ],
                "company_responsiveness": {
                    "average_response_time": "within 48 hours",
                    "response_quality": "professional but could be faster"
                }
            }
        }

    def _get_ecommerce_mock(self) -> Dict[str, Any]:
        """Mock para empresa e-commerce."""
        return {
            "communication_channels": {
                "website": {
                    "status": "active",
                    "effectiveness": "high",
                    "features": ["Mobile app", "Live chat", "Wishlist", "Product reviews", "Advanced search", "Personalization"],
                    "issues": ["Occasional downtime during sales"]
                },
                "social_media": {
                    "platforms": {
                        "linkedin": {"followers": "5K", "engagement": "low", "posts_per_week": 1},
                        "instagram": {"followers": "25K", "engagement": "high", "posts_per_week": 7},
                        "facebook": {"followers": "18K", "engagement": "medium", "posts_per_week": 5},
                        "twitter": {"followers": "8K", "engagement": "medium", "posts_per_week": 3}
                    },
                    "overall_presence": "strong",
                    "content_strategy": "Product showcases, user-generated content, promotions"
                },
                "email": {
                    "newsletter": True,
                    "frequency": "weekly",
                    "estimated_subscribers": "20K-50K",
                    "open_rate_estimate": "22%"
                },
                "phone": {
                    "available": True,
                    "hours": "Mon-Sat 8AM-10PM",
                    "languages": ["Portuguese", "English", "Spanish"]
                },
                "chat": {
                    "live_chat": True,
                    "chatbot": True,
                    "availability": "24/7",
                    "ai_powered": True
                }
            },
            "sales_channels": {
                "primary_channels": [
                    {
                        "type": "website",
                        "contribution": "45%",
                        "effectiveness": "high"
                    },
                    {
                        "type": "mobile_app",
                        "contribution": "25%",
                        "effectiveness": "high"
                    },
                    {
                        "type": "marketplaces",
                        "contribution": "20%",
                        "effectiveness": "medium"
                    },
                    {
                        "type": "social_commerce",
                        "contribution": "10%",
                        "effectiveness": "growing"
                    }
                ],
                "e_commerce": {
                    "has_online_store": True,
                    "platform": "Custom platform",
                    "payment_methods": ["Credit card", "Debit card", "PIX", "Boleto", "PayPal", "Apple Pay"],
                    "shipping_options": ["Standard", "Express", "Same-day (selected areas)", "Pickup points"]
                },
                "marketplaces": {
                    "present_in": ["Mercado Livre", "Amazon", "Americanas", "Magazine Luiza"],
                    "performance": "strong, 20% of total revenue"
                },
                "sales_team": {
                    "inside_sales": True,
                    "field_sales": False,
                    "team_size_estimate": "20-50"
                }
            },
            "customer_reviews": {
                "google_reviews": {
                    "rating": 4.5,
                    "total_reviews": 2341,
                    "recent_trend": "improving",
                    "response_rate": "95%"
                },
                "trustpilot": {
                    "rating": 4.3,
                    "total_reviews": 1876,
                    "verified_company": True
                },
                "reclame_aqui": {
                    "rating": 8.2,
                    "reputation": "great",
                    "response_rate": "98%",
                    "resolution_rate": "89%"
                },
                "glassdoor": {
                    "rating": 4.1,
                    "recommend_to_friend": "78%",
                    "ceo_approval": "85%"
                },
                "industry_specific": {
                    "ebit": {"rating": 4.4, "reviews": 3245}
                }
            },
            "feedback_analysis": {
                "sentiment_distribution": {
                    "positive": "75%",
                    "neutral": "18%",
                    "negative": "7%"
                },
                "common_praises": [
                    "Fast delivery",
                    "Great product variety",
                    "Excellent customer service",
                    "Easy returns",
                    "Competitive prices"
                ],
                "common_complaints": [
                    "Out of stock items",
                    "Packaging could be better",
                    "Mobile app bugs"
                ],
                "trending_topics": [
                    "Black Friday preparations",
                    "New sustainable packaging",
                    "Express delivery expansion"
                ],
                "company_responsiveness": {
                    "average_response_time": "within 2 hours",
                    "response_quality": "personalized and solution-oriented"
                }
            }
        }

    def _get_b2b_enterprise_mock(self) -> Dict[str, Any]:
        """Mock para empresa B2B enterprise."""
        return {
            "communication_channels": {
                "website": {
                    "status": "active",
                    "effectiveness": "high",
                    "features": ["Resource center", "Case studies", "ROI calculator", "Demo booking", "Partner portal", "Knowledge base"],
                    "issues": ["Complex navigation for new visitors"]
                },
                "social_media": {
                    "platforms": {
                        "linkedin": {"followers": "45K", "engagement": "high", "posts_per_week": 5},
                        "instagram": {"followers": "8K", "engagement": "low", "posts_per_week": 1},
                        "facebook": {"followers": "12K", "engagement": "low", "posts_per_week": 2},
                        "twitter": {"followers": "22K", "engagement": "medium", "posts_per_week": 4}
                    },
                    "overall_presence": "strong",
                    "content_strategy": "Thought leadership, industry insights, webinars, whitepapers"
                },
                "email": {
                    "newsletter": True,
                    "frequency": "bi-weekly",
                    "estimated_subscribers": "50K-100K",
                    "open_rate_estimate": "28%"
                },
                "phone": {
                    "available": True,
                    "hours": "24/7 for enterprise clients",
                    "languages": ["Portuguese", "English", "Spanish", "French"]
                },
                "chat": {
                    "live_chat": True,
                    "chatbot": True,
                    "availability": "Business hours live, 24/7 bot",
                    "ai_powered": True
                }
            },
            "sales_channels": {
                "primary_channels": [
                    {
                        "type": "enterprise_sales",
                        "contribution": "60%",
                        "effectiveness": "high"
                    },
                    {
                        "type": "partner_channel",
                        "contribution": "25%",
                        "effectiveness": "high"
                    },
                    {
                        "type": "inside_sales",
                        "contribution": "15%",
                        "effectiveness": "medium"
                    }
                ],
                "e_commerce": {
                    "has_online_store": False,
                    "platform": "Quote-based system",
                    "payment_methods": ["Invoice", "Wire transfer", "Corporate credit"],
                    "shipping_options": ["Not applicable - SaaS"]
                },
                "marketplaces": {
                    "present_in": ["AWS Marketplace", "Microsoft Azure Marketplace"],
                    "performance": "growing channel for SMB segment"
                },
                "sales_team": {
                    "inside_sales": True,
                    "field_sales": True,
                    "team_size_estimate": "100-500"
                }
            },
            "customer_reviews": {
                "google_reviews": {
                    "rating": 4.6,
                    "total_reviews": 234,
                    "recent_trend": "stable",
                    "response_rate": "100%"
                },
                "trustpilot": {
                    "rating": 4.4,
                    "total_reviews": 567,
                    "verified_company": True
                },
                "reclame_aqui": {
                    "rating": 8.9,
                    "reputation": "great",
                    "response_rate": "100%",
                    "resolution_rate": "95%"
                },
                "glassdoor": {
                    "rating": 4.3,
                    "recommend_to_friend": "87%",
                    "ceo_approval": "91%"
                },
                "industry_specific": {
                    "g2_crowd": {"rating": 4.5, "reviews": 892},
                    "capterra": {"rating": 4.6, "reviews": 645},
                    "gartner_peer_insights": {"rating": 4.4, "reviews": 127}
                }
            },
            "feedback_analysis": {
                "sentiment_distribution": {
                    "positive": "82%",
                    "neutral": "14%",
                    "negative": "4%"
                },
                "common_praises": [
                    "Excellent enterprise support",
                    "Robust platform features",
                    "Strong ROI",
                    "Great implementation team",
                    "Continuous innovation"
                ],
                "common_complaints": [
                    "High price point",
                    "Steep learning curve",
                    "Integration complexity"
                ],
                "trending_topics": [
                    "AI features launch",
                    "New enterprise pricing",
                    "Partnership announcements"
                ],
                "company_responsiveness": {
                    "average_response_time": "within 1 hour for enterprise",
                    "response_quality": "dedicated account management with SLA"
                }
            }
        }

    def _create_error_result(self, error_message: str, elapsed_time: float) -> ResearchResult:
        """Cria resultado de erro."""
        return ResearchResult(
            service_name=self.get_name(),
            service_version=self.get_version(),
            timestamp=datetime.now(timezone.utc),
            cost=Decimal("0"),
            success=False,
            data={},
            processing_time_seconds=elapsed_time,
            confidence_score=0.0,
            error=error_message
        )
