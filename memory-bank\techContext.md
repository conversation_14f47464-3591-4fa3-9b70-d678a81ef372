# Tech Context - ScopeAI

**Versão**: 1.0.0
**Última Atualização**: 2025-01-29

## 🛠️ Stack Tecnológico

### Frontend
```json
{
  "framework": "Angular 19.0.0",
  "language": "TypeScript 5.3",
  "styling": "Tailwind CSS 3.4 + SCSS",
  "state": "RxJS 7.8",
  "build": "Vite 5.0",
  "testing": "Jasmine + Karma",
  "linting": "ESLint + Prettier"
}
```

### Backend
```json
{
  "framework": "FastAPI 0.104.1",
  "language": "Python 3.12",
  "async": "asyncio + aiohttp",
  "database": "Motor (MongoDB async driver)",
  "cache": "Redis 7.2",
  "queue": "Celery + Redis",
  "testing": "pytest + pytest-asyncio"
}
```

### Infrastructure
```json
{
  "containerization": "Docker 24.0",
  "orchestration": "Docker Compose 2.23",
  "database": "MongoDB Atlas 7.0",
  "storage": "GridFS",
  "reverse_proxy": "Nginx 1.25",
  "monitoring": "Custom logging"
}
```

## 📦 Dependências Principais

### Frontend Dependencies
```typescript
// package.json principais
"@angular/core": "^19.0.0",
"@angular/common": "^19.0.0",
"@angular/router": "^19.0.0",
"rxjs": "^7.8.0",
"tailwindcss": "^3.4.0",
"marked": "^11.0.0",
"mermaid": "^10.6.0",
"cytoscape": "^3.28.0"
```

### Backend Dependencies
```python
# pyproject.toml principais
fastapi = "^0.104.1"
motor = "^3.3.2"
redis = "^5.0.1"
openai = "^1.6.0"
cohere = "^4.37"
playwright = "^1.40.0"
weasyprint = "^60.1"
pydantic = "^2.5.0"
```

### AI/ML Dependencies
```python
# Modelos e APIs
perplexity-client = "custom"
mistralai = "^0.0.12"
langchain = "^0.1.0"
chromadb = "^0.4.22"
sentence-transformers = "^2.2.2"
```

## 🔧 Configurações de Ambiente

### Development
```bash
# .env.development
MONGODB_URI=mongodb://localhost:27017/scopeai_dev
REDIS_URL=redis://localhost:6379/0
OPENAI_API_KEY=sk-...
PERPLEXITY_API_KEY=pplx-...
COHERE_API_KEY=...
LOG_LEVEL=DEBUG
ENABLE_HOT_RELOAD=true
```

### Production
```bash
# .env.production
MONGODB_URI=mongodb+srv://...@cluster.mongodb.net/
REDIS_URL=redis://redis:6379/0
OPENAI_API_KEY=${OPENAI_API_KEY}
PERPLEXITY_API_KEY=${PERPLEXITY_API_KEY}
COHERE_API_KEY=${COHERE_API_KEY}
LOG_LEVEL=INFO
ENABLE_HOT_RELOAD=false
```

## 🚧 Restrições Técnicas

### Performance
- **Response Time**: < 2s para operações síncronas
- **Async Operations**: < 10min para análise completa
- **Concurrent Users**: Suporta até 100 simultâneos
- **Memory Usage**: < 4GB por container
- **CPU Usage**: < 80% sustained

### Scalability
- **Horizontal**: Stateless, pronto para múltiplas instâncias
- **Database**: MongoDB sharding ready
- **Cache**: Redis cluster compatible
- **Queue**: Celery workers escaláveis

### Security
- **Authentication**: JWT com refresh tokens
- **Authorization**: RBAC implementado
- **Encryption**: TLS 1.3 em produção
- **Secrets**: Vault ou environment variables
- **CORS**: Configurado para origins específicas

### Compatibility
- **Browsers**: Chrome 90+, Firefox 88+, Safari 14+
- **Node**: 18.x ou 20.x
- **Python**: 3.11 ou 3.12
- **Docker**: 20.x+
- **OS**: Linux, macOS, Windows (WSL2)

## 🔌 Integrações Externas

### APIs Primárias
```yaml
Perplexity:
  endpoint: https://api.perplexity.ai
  rate_limit: 50 req/min
  cost: ~$0.006/request

OpenAI:
  endpoint: https://api.openai.com
  models: [gpt-4-turbo, text-embedding-3-small]
  rate_limit: 10000 tokens/min
  cost: ~$0.01/1k tokens

Cohere:
  endpoint: https://api.cohere.ai
  model: command-r
  rate_limit: 100 req/min
  cost: ~$0.0005/request
```

### Modelos Locais
```yaml
Mistral:
  via: Ollama
  model: mistral:latest
  memory: 8GB RAM required

Llama:
  via: Ollama
  model: llama3.1:8b
  memory: 6GB RAM required
```

## 🐳 Docker Configuration

### Docker Compose Services
```yaml
services:
  frontend:
    build: ./frontend
    ports: ["4200:80"]
    environment:
      - API_URL=http://backend:8000

  backend:
    build: ./backend
    ports: ["8040:8000"]
    volumes:
      - ./backend:/app
    environment:
      - MONGODB_URI
      - REDIS_URL

  redis:
    image: redis:7-alpine
    ports: ["6379:6379"]

  ollama:
    image: ollama/ollama
    ports: ["11434:11434"]
    volumes:
      - ollama_data:/root/.ollama
```

## 🔐 Variáveis de Ambiente

### Obrigatórias
- `MONGODB_URI`: Connection string MongoDB
- `OPENAI_API_KEY`: Key para embeddings e análise
- `PERPLEXITY_API_KEY`: Key para pesquisa web
- `COHERE_API_KEY`: Key para geração de conteúdo

### Opcionais
- `REDIS_URL`: Default `redis://localhost:6379`
- `LOG_LEVEL`: Default `INFO`
- `ENABLE_METRICS`: Default `false`
- `SENTRY_DSN`: Para error tracking
- `WEBHOOK_URL`: Para notificações

## 📱 Requisitos de Sistema

### Desenvolvimento
- **CPU**: 4+ cores
- **RAM**: 16GB (8GB mínimo)
- **Storage**: 50GB SSD
- **GPU**: Não necessário

### Produção
- **CPU**: 8+ cores
- **RAM**: 32GB recomendado
- **Storage**: 100GB SSD
- **Network**: 100Mbps+
- **GPU**: Opcional para ML local

## 🔄 CI/CD Pipeline

### Build Steps
1. **Lint**: ESLint + Black
2. **Test**: Jest + Pytest
3. **Build**: Docker multi-stage
4. **Scan**: Security vulnerabilities
5. **Deploy**: Docker registry

### Deployment
```bash
# Production deployment
docker-compose -f docker-compose.prod.yml up -d

# Rolling update
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d --no-deps backend
```

## 🚨 Limitações Conhecidas

### Technical Debt
1. **Monolithic Backend**: Refatoração em progresso
2. **No Service Mesh**: Planejado para v2.0
3. **Limited Monitoring**: Apenas logs básicos
4. **No A/B Testing**: Framework não implementado

### Performance Bottlenecks
1. **Perplexity API**: Rate limits frequentes
2. **PDF Generation**: CPU intensive (WeasyPrint)
3. **Large Reports**: Memory spikes >2GB
4. **Concurrent Analysis**: Limited to 10

### Compatibility Issues
1. **Windows**: WeasyPrint requer GTK
2. **M1 Macs**: Algumas deps precisam Rosetta
3. **Node 21+**: Incompatibilidade com algumas deps Angular 