"""
Testes para TechStackService.

Testa a análise de stack tecnológica incluindo:
- Validação de requisições
- Processamento de resultados
- Cálculo de confidence score
- Tratamento de erros
- Normalização de dados
"""
import pytest
import json
from unittest.mock import Mock, AsyncMock, patch
from decimal import Decimal
from datetime import datetime

from app.services.research import TechStackService
from app.core import ResearchRequest, ResearchResult


class TestTechStackService:
    """Testes para o serviço de análise de tech stack."""

    @pytest.fixture
    def mock_provider(self):
        """Mock do provider para testes."""
        provider = Mock()
        provider.search = AsyncMock()
        return provider

    @pytest.fixture
    def service(self, mock_provider):
        """Instância do serviço para testes."""
        return TechStackService(mock_provider)

    @pytest.fixture
    def valid_request(self):
        """Request válido para testes."""
        return ResearchRequest(
            client_id="test-123",
            company_name="TechCorp",
            company_url="https://techcorp.com",
            additional_context={"industry": "tecnologia"}
        )

    @pytest.fixture
    def sample_tech_stack_response(self):
        """Resposta de exemplo do provider."""
        return {
            "frontend": {
                "frameworks": ["React", "Next.js"],
                "languages": ["TypeScript", "JavaScript"],
                "styling": ["Tailwind CSS"],
                "build_tools": ["Webpack"],
                "testing": ["Jest"],
                "state_management": ["Redux"]
            },
            "backend": {
                "languages": ["Python"],
                "frameworks": ["FastAPI"],
                "apis": ["REST"],
                "authentication": ["JWT"],
                "testing": ["Pytest"]
            },
            "database": {
                "sql": ["PostgreSQL"],
                "nosql": ["Redis"],
                "orm": ["SQLAlchemy"],
                "cache": ["Redis"]
            },
            "infrastructure": {
                "cloud": ["AWS"],
                "containers": ["Docker"],
                "ci_cd": ["GitHub Actions"],
                "monitoring": ["Datadog"],
                "cdn": ["Cloudflare"]
            },
            "analytics": {
                "tracking": ["Google Analytics"],
                "business_intelligence": [],
                "error_tracking": ["Sentry"],
                "performance": ["Lighthouse"]
            },
            "tools": {
                "cms": [],
                "ecommerce": [],
                "communication": ["Slack"],
                "project_management": ["Jira"],
                "version_control": ["Git", "GitHub"]
            }
        }

    def test_service_metadata(self, service):
        """Testa metadados do serviço."""
        assert service.get_name() == "tech_stack"
        assert service.get_version() == "1.0.0"
        assert service.get_cost() == Decimal("0.005")
        assert "stack tecnológica" in service.get_description()

    def test_required_and_optional_fields(self, service):
        """Testa campos obrigatórios e opcionais."""
        required = service.get_required_fields()
        assert "company_name" in required
        assert "company_url" in required
        assert len(required) == 2

        optional = service.get_optional_fields()
        assert "industry" in optional
        assert "company_size" in optional
        assert "additional_urls" in optional

    @pytest.mark.asyncio
    async def test_validate_request_success(self, service, valid_request):
        """Testa validação de request válido."""
        assert await service.validate_request(valid_request) is True

    @pytest.mark.asyncio
    async def test_validate_request_missing_name(self, service):
        """Testa validação com nome faltando."""
        request = ResearchRequest(
            client_id="test",
            company_name="",
            company_url="https://test.com"
        )
        assert await service.validate_request(request) is False

    @pytest.mark.asyncio
    async def test_validate_request_missing_url(self, service):
        """Testa validação com URL faltando."""
        request = ResearchRequest(
            client_id="test",
            company_name="Test Corp",
            company_url=""
        )
        assert await service.validate_request(request) is False

    @pytest.mark.asyncio
    async def test_validate_request_invalid_url(self, service):
        """Testa validação com URL inválida."""
        request = ResearchRequest(
            client_id="test",
            company_name="Test Corp",
            company_url="invalid-url"
        )
        assert await service.validate_request(request) is False

    @pytest.mark.asyncio
    async def test_execute_success(self, service, mock_provider, valid_request, sample_tech_stack_response):
        """Testa execução bem-sucedida."""
        # Configurar mock
        mock_provider.search.return_value = sample_tech_stack_response

        # Executar
        result = await service.execute(valid_request)

        # Verificar resultado
        assert result.success is True
        assert result.service_name == "tech_stack"
        assert result.cost == Decimal("0.005")
        assert result.confidence_score > 0.7
        assert "frontend" in result.data
        assert "backend" in result.data
        assert "metadata" in result.data

        # Verificar metadados
        metadata = result.data["metadata"]
        assert metadata["company_url"] == "https://techcorp.com"
        assert metadata["industry"] == "tecnologia"
        # Contagem das tecnologias no sample
        assert metadata["total_technologies_found"] == 29

    @pytest.mark.asyncio
    async def test_execute_json_string_response(self, service, mock_provider, valid_request, sample_tech_stack_response):
        """Testa com resposta como string JSON."""
        # Configurar mock para retornar string JSON
        mock_provider.search.return_value = json.dumps(
            sample_tech_stack_response)

        # Executar
        result = await service.execute(valid_request)

        # Verificar resultado
        assert result.success is True
        assert "React" in result.data["frontend"]["frameworks"]
        assert "FastAPI" in result.data["backend"]["frameworks"]

    @pytest.mark.asyncio
    async def test_execute_invalid_json(self, service, mock_provider, valid_request):
        """Testa com resposta JSON inválida."""
        # Configurar mock para retornar JSON inválido
        mock_provider.search.return_value = "invalid json {{"

        # Executar
        result = await service.execute(valid_request)

        # Verificar resultado - deve retornar estrutura vazia mas com sucesso
        assert result.success is True
        assert result.data["frontend"]["frameworks"] == []
        assert result.data["backend"]["languages"] == []

    @pytest.mark.asyncio
    async def test_execute_exception(self, service, mock_provider, valid_request):
        """Testa tratamento de exceção."""
        # Configurar mock para lançar exceção
        mock_provider.search.side_effect = Exception("API Error")

        # Executar
        result = await service.execute(valid_request)

        # Verificar resultado
        assert result.success is False
        assert result.error == "API Error"
        assert result.cost == Decimal("0")

    def test_get_sample_output(self, service):
        """Testa estrutura do output de exemplo."""
        sample = service.get_sample_output()

        # Verificar estrutura
        assert "frontend" in sample
        assert "backend" in sample
        assert "database" in sample
        assert "infrastructure" in sample
        assert "analytics" in sample
        assert "tools" in sample
        assert "metadata" in sample

        # Verificar conteúdo
        assert "React" in sample["frontend"]["frameworks"]
        assert "Python" in sample["backend"]["languages"]
        assert sample["metadata"]["confidence_score"] == 0.85

    def test_build_prompts(self, service):
        """Testa construção de prompts."""
        system_prompt = service._build_system_prompt()
        assert "especialista em análise de stack tecnológica" in system_prompt
        assert "Frontend e Backend" in system_prompt

        user_prompt = service._build_user_prompt(
            "TechCorp", "https://techcorp.com", "fintech")
        assert "TechCorp" in user_prompt
        assert "https://techcorp.com" in user_prompt
        assert "fintech" in user_prompt
        assert "Frontend" in user_prompt
        assert "Backend" in user_prompt
        assert "JSON estruturado" in user_prompt

    def test_process_result_valid_data(self, service, sample_tech_stack_response):
        """Testa processamento de dados válidos."""
        result = service._process_result(
            sample_tech_stack_response, "TechCorp")

        # Verificar estrutura processada
        assert result["frontend"]["frameworks"] == ["React", "Next.js"]
        assert result["backend"]["languages"] == ["Python"]
        assert result["database"]["sql"] == ["PostgreSQL"]

    def test_process_result_missing_categories(self, service):
        """Testa processamento com categorias faltando."""
        incomplete_data = {
            "frontend": {
                "frameworks": ["Vue.js"]
            }
            # Faltam outras categorias
        }

        result = service._process_result(incomplete_data, "TechCorp")

        # Deve ter todas as categorias, mesmo vazias
        assert "frontend" in result
        assert "backend" in result
        assert result["frontend"]["frameworks"] == ["Vue.js"]
        assert result["backend"]["languages"] == []

    def test_normalize_category(self, service):
        """Testa normalização de categoria."""
        category_data = {
            "frameworks": ["React", "Vue"],
            "languages": "JavaScript",  # String ao invés de lista
            "styling": ["Não detectado"],  # Deve ser filtrado
            "extra_field": ["ignored"]  # Campo não esperado
        }

        normalized = service._normalize_category(category_data, "frontend")

        assert normalized["frameworks"] == ["React", "Vue"]
        assert normalized["languages"] == [
            "JavaScript"]  # Convertido para lista
        assert normalized["styling"] == []  # "Não detectado" foi filtrado
        assert "extra_field" not in normalized  # Campo ignorado
        assert "build_tools" in normalized  # Campo esperado mas vazio

    def test_calculate_confidence_score_high(self, service, sample_tech_stack_response):
        """Testa cálculo de confidence score alto."""
        score = service._calculate_confidence_score(sample_tech_stack_response)
        assert score > 0.8  # Dados completos devem ter score alto

    def test_calculate_confidence_score_low(self, service):
        """Testa cálculo de confidence score baixo."""
        empty_data = service._get_empty_tech_stack()
        score = service._calculate_confidence_score(empty_data)
        assert score < 0.3  # Dados vazios devem ter score baixo

    def test_calculate_confidence_score_medium(self, service):
        """Testa cálculo de confidence score médio."""
        partial_data = {
            "frontend": {
                "frameworks": ["React"],
                "languages": ["JavaScript"]
            },
            "backend": {},
            "database": {},
            "infrastructure": {},
            "analytics": {},
            "tools": {}
        }

        score = service._calculate_confidence_score(partial_data)
        assert 0.3 <= score <= 0.7  # Score médio para dados parciais

    def test_count_technologies(self, service, sample_tech_stack_response):
        """Testa contagem de tecnologias."""
        count = service._count_technologies(sample_tech_stack_response)
        assert count == 29  # Total de tecnologias no sample

    def test_count_technologies_empty(self, service):
        """Testa contagem com dados vazios."""
        empty_data = service._get_empty_tech_stack()
        count = service._count_technologies(empty_data)
        assert count == 0

    def test_get_empty_tech_stack_structure(self, service):
        """Testa estrutura vazia de tech stack."""
        empty = service._get_empty_tech_stack()

        # Verificar todas as categorias
        assert all(cat in empty for cat in [
                   "frontend", "backend", "database", "infrastructure", "analytics", "tools"])

        # Verificar subcategorias do frontend
        assert all(key in empty["frontend"] for key in [
                   "frameworks", "languages", "styling", "build_tools", "testing", "state_management"])

        # Verificar que todas as listas estão vazias
        for category in empty.values():
            for subcategory in category.values():
                assert subcategory == []

    @pytest.mark.asyncio
    async def test_confidence_score_adjustment(self, service):
        """Testa ajustes no confidence score baseado em quantidade de tecnologias."""
        # Muitas tecnologias (>20) devem aumentar o score
        many_tech_data = {
            "frontend": {"frameworks": ["React"] * 10},
            "backend": {"languages": ["Python"] * 10},
            "database": {"sql": ["PostgreSQL"] * 5}
        }
        score_high = service._calculate_confidence_score(many_tech_data)

        # Poucas tecnologias (<5) devem diminuir o score
        few_tech_data = {
            "frontend": {"frameworks": ["React"]},
            "backend": {"languages": ["Python"]}
        }
        score_low = service._calculate_confidence_score(few_tech_data)

        # Score com muitas tecnologias deve ser maior
        assert score_high > score_low

    @pytest.mark.asyncio
    async def test_execute_without_industry(self, service, mock_provider):
        """Testa execução sem campo industry."""
        request = ResearchRequest(
            client_id="test",
            company_name="TechCorp",
            company_url="https://techcorp.com"
            # Sem industry
        )

        mock_provider.search.return_value = {
            "frontend": {"frameworks": ["React"]}}

        result = await service.execute(request)

        assert result.success is True
        assert result.data["metadata"]["industry"] == "não especificada"

    def test_create_error_result(self, service):
        """Testa criação de resultado de erro."""
        error_result = service._create_error_result("Test error", 1.5)

        assert error_result.success is False
        assert error_result.error == "Test error"
        assert error_result.cost == Decimal("0")
        assert error_result.processing_time_seconds == 1.5
        assert error_result.data == service._get_empty_tech_stack()
