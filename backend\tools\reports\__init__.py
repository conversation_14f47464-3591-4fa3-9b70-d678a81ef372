"""
Sistema de Reports Estruturado

Este módulo implementa um sistema completo de reports estruturado com:
- Geração automática de PDFs executivos  
- Histórico e versionamento de relatórios

Componentes:
- ReportManager: Coordenador principal do sistema
- EnhancedPDFGenerator: Criação de PDFs executivos com IA
"""

# Importações dos módulos ativos
try:

    __all__ = [
        "ReportManager",
        "PDFGenerator"
    ]
except ImportError as e:
    # Se algum módulo não está disponível, criar lista vazia
    __all__ = []
    import logging
    logger = logging.getLogger(__name__)
    logger.warning(f"Algumas importações não estão disponíveis: {e}")
