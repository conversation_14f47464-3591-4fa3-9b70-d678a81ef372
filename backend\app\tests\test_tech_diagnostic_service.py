"""
Testes unitários para o Tech Diagnostic Service.
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch
from decimal import Decimal
from datetime import datetime, UTC

from app.services.research.tech_diagnostic_service import TechDiagnosticService
from app.core import ResearchRequest, ResearchResult


@pytest.fixture
def mock_perplexity_provider():
    """Mock do provider Perplexity."""
    provider = Mock()
    provider.search = AsyncMock()
    return provider


@pytest.fixture
def tech_diagnostic_service(mock_perplexity_provider):
    """Instância do TechDiagnosticService para testes."""
    return TechDiagnosticService(mock_perplexity_provider)


@pytest.fixture
def valid_request():
    """Request válido para testes."""
    return ResearchRequest(
        client_id="test-client-123",
        company_url="https://example.com",
        company_name="Example Company",
        additional_context={
            "mobile_priority": True,
            "industry_type": "technology",
            "target_audience": "B2B"
        }
    )


class TestTechDiagnosticService:
    """Testes para o Tech Diagnostic Service."""

    def test_service_initialization(self, tech_diagnostic_service):
        """Testa a inicialização correta do serviço."""
        assert tech_diagnostic_service.get_name() == "tech_diagnostic"
        assert tech_diagnostic_service.get_version() == "1.0.0"
        assert tech_diagnostic_service.get_cost() == Decimal("0.010")
        assert "diagnóstico técnico completo" in tech_diagnostic_service.get_description()

    def test_required_fields(self, tech_diagnostic_service):
        """Testa os campos obrigatórios."""
        required = tech_diagnostic_service.get_required_fields()
        assert "company_name" in required
        assert "company_url" in required
        assert len(required) == 2

    def test_optional_fields(self, tech_diagnostic_service):
        """Testa os campos opcionais."""
        optional = tech_diagnostic_service.get_optional_fields()
        assert "mobile_priority" in optional
        assert "industry_type" in optional
        assert "target_audience" in optional

    @pytest.mark.asyncio
    async def test_validate_request_valid(self, tech_diagnostic_service, valid_request):
        """Testa validação de request válido."""
        is_valid = await tech_diagnostic_service.validate_request(valid_request)
        assert is_valid is True

    @pytest.mark.asyncio
    async def test_validate_request_missing_name(self, tech_diagnostic_service):
        """Testa validação com nome faltando."""
        request = ResearchRequest(
            client_id="test-123",
            company_url="https://example.com",
            company_name=""
        )
        is_valid = await tech_diagnostic_service.validate_request(request)
        assert is_valid is False

    @pytest.mark.asyncio
    async def test_validate_request_missing_url(self, tech_diagnostic_service):
        """Testa validação com URL faltando."""
        request = ResearchRequest(
            client_id="test-123",
            company_url="",
            company_name="Example Company"
        )
        is_valid = await tech_diagnostic_service.validate_request(request)
        assert is_valid is False

    @pytest.mark.asyncio
    async def test_validate_request_invalid_url(self, tech_diagnostic_service):
        """Testa validação com URL inválida."""
        request = ResearchRequest(
            client_id="test-123",
            company_url="example.com",  # Sem protocolo
            company_name="Example Company"
        )
        is_valid = await tech_diagnostic_service.validate_request(request)
        assert is_valid is False

    @pytest.mark.asyncio
    async def test_execute_success(self, tech_diagnostic_service, valid_request):
        """Testa execução bem-sucedida do serviço."""
        result = await tech_diagnostic_service.execute(valid_request)

        assert result.success is True
        assert result.service_name == "tech_diagnostic"
        assert result.service_version == "1.0.0"
        assert result.cost == Decimal("0.010")
        assert result.confidence_score == 0.95
        assert result.processing_time_seconds >= 0
        assert result.error is None

        # Verificar estrutura dos dados
        data = result.data
        assert "performance" in data
        assert "security" in data
        assert "accessibility" in data
        assert "seo" in data
        assert "best_practices" in data
        assert "overall_score" in data
        assert "top_recommendations" in data

    @pytest.mark.asyncio
    async def test_execute_performance_data(self, tech_diagnostic_service, valid_request):
        """Testa os dados de performance retornados."""
        result = await tech_diagnostic_service.execute(valid_request)
        performance = result.data["performance"]

        assert "score" in performance
        assert 60 <= performance["score"] <= 95

        # Core Web Vitals
        assert "core_web_vitals" in performance
        cwv = performance["core_web_vitals"]
        assert "lcp" in cwv
        assert "fid" in cwv
        assert "cls" in cwv

        # Verificar estrutura de cada métrica
        for metric in ["lcp", "fid", "cls"]:
            assert "value" in cwv[metric]
            assert "score" in cwv[metric]
            assert "unit" in cwv[metric]
            assert "description" in cwv[metric]

        # Outras métricas
        assert "metrics" in performance
        assert "opportunities" in performance
        assert "diagnostics" in performance

    @pytest.mark.asyncio
    async def test_execute_security_data(self, tech_diagnostic_service, valid_request):
        """Testa os dados de segurança retornados."""
        result = await tech_diagnostic_service.execute(valid_request)
        security = result.data["security"]

        assert "score" in security
        assert 70 <= security["score"] <= 100
        assert "https_enabled" in security
        assert "ssl_certificate" in security
        assert "security_headers" in security
        assert "vulnerabilities" in security
        assert "recommendations" in security

        # Verificar headers de segurança
        headers = security["security_headers"]
        assert "content_security_policy" in headers
        assert "x_frame_options" in headers
        assert "strict_transport_security" in headers

    @pytest.mark.asyncio
    async def test_execute_accessibility_data(self, tech_diagnostic_service, valid_request):
        """Testa os dados de acessibilidade retornados."""
        result = await tech_diagnostic_service.execute(valid_request)
        accessibility = result.data["accessibility"]

        assert "score" in accessibility
        assert 55 <= accessibility["score"] <= 90
        assert "wcag_level" in accessibility
        assert accessibility["wcag_level"] in ["A", "AA", "AAA"]
        assert "issues" in accessibility
        assert "passed_audits" in accessibility
        assert "manual_checks_needed" in accessibility

    @pytest.mark.asyncio
    async def test_execute_seo_data(self, tech_diagnostic_service, valid_request):
        """Testa os dados de SEO retornados."""
        result = await tech_diagnostic_service.execute(valid_request)
        seo = result.data["seo"]

        assert "score" in seo
        assert 65 <= seo["score"] <= 95
        assert "meta_tags" in seo
        assert "structured_data" in seo
        assert "sitemap" in seo
        assert "robots_txt" in seo
        assert "crawlability" in seo
        assert "recommendations" in seo

    @pytest.mark.asyncio
    async def test_execute_best_practices_data(self, tech_diagnostic_service, valid_request):
        """Testa os dados de melhores práticas retornados."""
        result = await tech_diagnostic_service.execute(valid_request)
        best_practices = result.data["best_practices"]

        assert "score" in best_practices
        assert 70 <= best_practices["score"] <= 92
        assert "uses_https" in best_practices
        assert "uses_http2" in best_practices
        assert "image_optimization" in best_practices
        assert "caching_strategy" in best_practices
        assert "minification" in best_practices
        assert "compression" in best_practices
        assert "recommendations" in best_practices

    @pytest.mark.asyncio
    async def test_execute_recommendations(self, tech_diagnostic_service, valid_request):
        """Testa as recomendações retornadas."""
        result = await tech_diagnostic_service.execute(valid_request)
        recommendations = result.data["top_recommendations"]

        assert isinstance(recommendations, list)
        assert len(recommendations) <= 3

        for rec in recommendations:
            assert "category" in rec
            assert "priority" in rec
            assert "title" in rec
            assert "description" in rec
            assert "estimated_impact" in rec
            assert "effort" in rec

    @pytest.mark.asyncio
    async def test_execute_technology_stack(self, tech_diagnostic_service, valid_request):
        """Testa a detecção de stack tecnológico."""
        result = await tech_diagnostic_service.execute(valid_request)
        tech_stack = result.data["technology_stack"]

        assert "detected" in tech_stack
        assert isinstance(tech_stack["detected"], list)
        assert len(tech_stack["detected"]) > 0
        assert "cdn" in tech_stack
        assert "analytics" in tech_stack

    @pytest.mark.asyncio
    async def test_execute_with_exception(self, tech_diagnostic_service, valid_request):
        """Testa tratamento de exceção durante execução."""
        # Forçar uma exceção
        with patch.object(tech_diagnostic_service, '_get_mock_diagnostic_data',
                          side_effect=Exception("Erro simulado")):
            result = await tech_diagnostic_service.execute(valid_request)

            assert result.success is False
            assert result.error == "Erro simulado"
            assert result.cost == Decimal("0")  # Não cobra em caso de erro
            assert result.data is None

    def test_get_sample_output(self, tech_diagnostic_service):
        """Testa o exemplo de saída."""
        sample = tech_diagnostic_service.get_sample_output()

        assert isinstance(sample, dict)
        assert "performance" in sample
        assert "security" in sample
        assert "accessibility" in sample
        assert "seo" in sample
        assert "best_practices" in sample
        assert "overall_score" in sample
        assert "recommendations" in sample

    def test_performance_opportunities_generation(self, tech_diagnostic_service):
        """Testa a geração de oportunidades de performance."""
        # Score alto (poucas oportunidades)
        opportunities = tech_diagnostic_service._generate_performance_opportunities(
            92)
        assert len(opportunities) == 0

        # Score médio
        opportunities = tech_diagnostic_service._generate_performance_opportunities(
            75)
        assert len(opportunities) == 2
        assert any("Optimize images" in opp["title"] for opp in opportunities)

        # Score baixo (muitas oportunidades)
        opportunities = tech_diagnostic_service._generate_performance_opportunities(
            65)
        assert len(opportunities) == 3

    def test_vulnerabilities_generation(self, tech_diagnostic_service):
        """Testa a geração de vulnerabilidades."""
        # Score alto (sem vulnerabilidades)
        vulns = tech_diagnostic_service._generate_vulnerabilities(85)
        assert len(vulns) == 0

        # Score baixo
        vulns = tech_diagnostic_service._generate_vulnerabilities(65)
        assert len(vulns) == 2
        assert any(v["severity"] == "high" for v in vulns)

    def test_accessibility_issues_generation(self, tech_diagnostic_service):
        """Testa a geração de problemas de acessibilidade."""
        # Score alto
        issues = tech_diagnostic_service._generate_accessibility_issues(92)
        assert len(issues) == 0

        # Score médio
        issues = tech_diagnostic_service._generate_accessibility_issues(70)
        assert len(issues) == 2

        # Score baixo
        issues = tech_diagnostic_service._generate_accessibility_issues(55)
        assert len(issues) == 3
        assert any(i["severity"] == "critical" for i in issues)

    def test_recommendations_generation(self, tech_diagnostic_service):
        """Testa a geração de recomendações por categoria."""
        # Security
        sec_recs = tech_diagnostic_service._generate_security_recommendations(
            75)
        assert len(sec_recs) == 2
        assert any("CSP" in rec for rec in sec_recs)

        # SEO
        seo_recs = tech_diagnostic_service._generate_seo_recommendations(70)
        assert len(seo_recs) == 2
        assert any("Schema" in rec for rec in seo_recs)

        # Best Practices
        bp_recs = tech_diagnostic_service._generate_best_practices_recommendations(
            70)
        assert len(bp_recs) == 2
        assert any("cache" in rec for rec in bp_recs)

    def test_top_recommendations_prioritization(self, tech_diagnostic_service):
        """Testa a priorização de recomendações principais."""
        # Cenário: Performance muito baixa
        recs = tech_diagnostic_service._generate_top_recommendations(
            perf=55, sec=85, a11y=80, seo=85, bp=80
        )

        assert len(recs) == 3
        assert any(r["category"] == "performance" for r in recs)
        assert any(r["priority"] == "critical" for r in recs)

        # Cenário: Segurança baixa
        recs = tech_diagnostic_service._generate_top_recommendations(
            perf=85, sec=65, a11y=80, seo=85, bp=80
        )

        assert any(r["category"] == "security" for r in recs)

        # Sempre tem quick-win
        assert any(r["category"] == "quick-win" for r in recs)

    def test_confidence_score_calculation(self, tech_diagnostic_service):
        """Testa o cálculo do score de confiança."""
        # Por enquanto sempre retorna 0.95 (mock)
        mock_data = {"test": "data"}
        score = tech_diagnostic_service._calculate_confidence_score(mock_data)
        assert score == 0.95

    @pytest.mark.asyncio
    async def test_overall_score_calculation(self, tech_diagnostic_service, valid_request):
        """Testa o cálculo do score geral."""
        result = await tech_diagnostic_service.execute(valid_request)
        data = result.data

        # Verificar que o overall_score é a média dos 5 scores
        expected_avg = (
            data["performance"]["score"] +
            data["security"]["score"] +
            data["accessibility"]["score"] +
            data["seo"]["score"] +
            data["best_practices"]["score"]
        ) / 5

        assert abs(data["overall_score"] - expected_avg) < 0.1

    def test_build_prompts(self, tech_diagnostic_service):
        """Testa a construção dos prompts."""
        system_prompt = tech_diagnostic_service._build_system_prompt()
        assert "especialista em análise técnica" in system_prompt
        assert "Core Web Vitals" in system_prompt
        assert "JSON" in system_prompt

        user_prompt = tech_diagnostic_service._build_user_prompt(
            "Example Company", "https://example.com"
        )
        assert "Example Company" in user_prompt
        assert "https://example.com" in user_prompt
        assert "análise técnica completa" in user_prompt
