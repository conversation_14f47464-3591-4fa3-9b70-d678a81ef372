"""
Pricing Analysis Service - Análise detalhada de estratégias de precificação

Este serviço analisa modelos de precificação, competitividade de preços,
elasticidade e estratégias de monetização para empresas.

Custo: $0.005 por execução
"""
import json
import logging
from typing import Dict, Any, List, Optional
from decimal import Decimal
from datetime import datetime, timezone

from app.core import (
    IResearchService,
    ResearchRequest,
    ResearchResult,
    Timer
)

logger = logging.getLogger(__name__)


class PricingAnalysisService(IResearchService):
    """
    Serviço especializado em análise de estratégias de precificação.

    Analisa e avalia:
    - Modelos de precificação (freemium, subscription, usage-based, etc.)
    - Competitividade de preços no mercado
    - Elasticidade e sensibilidade de preço
    - Estratégias de monetização e upsell
    - Previsão de receita e ROI potencial
    """

    def __init__(self, provider):
        """
        Args:
            provider: Instância do provider (Perplexity, SearchSociety, etc)
        """
        self.provider = provider
        self._version = "1.0.0"
        self._cost = Decimal("0.005")

    def get_name(self) -> str:
        return "pricing_analysis"

    def get_version(self) -> str:
        return self._version

    def get_description(self) -> str:
        return "Análise de estratégias de precificação incluindo modelos, competitividade, elasticidade e monetização"

    def get_cost(self) -> Decimal:
        return self._cost

    def get_required_fields(self) -> List[str]:
        return ["company_name", "company_url"]

    def get_optional_fields(self) -> List[str]:
        return ["industry", "target_market", "company_size", "competitors"]

    async def validate_request(self, request: ResearchRequest) -> bool:
        """Valida se a requisição tem os dados necessários."""
        if not request.company_name or not request.company_url:
            logger.error("Nome da empresa e URL são obrigatórios")
            return False

        # Validar formato da URL
        url = request.company_url.lower()
        if not url.startswith(('http://', 'https://')):
            logger.error(f"URL inválida: {request.company_url}")
            return False

        return True

    async def execute(self, request: ResearchRequest) -> ResearchResult:
        """Executa a análise de pricing."""
        with Timer() as timer:
            try:
                empresa = request.company_name
                site = request.company_url

                # AIDEV-NOTE: Extrair campos opcionais do request
                industria = getattr(request, 'industry', None)
                if industria is None and request.additional_context:
                    industria = request.additional_context.get(
                        'industry', 'não especificada')
                if industria is None:
                    industria = 'não especificada'

                target_market = getattr(request, 'target_market', 'B2B/B2C')
                if request.additional_context:
                    target_market = request.additional_context.get(
                        'target_market', target_market)

                # Mock mode para desenvolvimento
                if hasattr(self.provider, '_mock_mode') and self.provider._mock_mode:
                    logger.info(
                        f"[MOCK] Retornando análise de pricing mock para {empresa}")
                    processed_data = self._get_mock_data(empresa, industria)
                else:
                    # AIDEV-NOTE: Construir prompts especializados para análise de pricing
                    system_prompt = self._build_system_prompt()
                    user_prompt = self._build_user_prompt(
                        empresa, site, industria, target_market)

                    logger.info(
                        f"Executando análise de pricing para {empresa}")

                    # Fazer requisição ao provider
                    raw_result = await self.provider.search(
                        system_prompt=system_prompt,
                        user_prompt=user_prompt,
                        temperature=0.7,
                        max_tokens=2000
                    )

                    # Processar e estruturar resultado
                    processed_data = self._process_result(raw_result, empresa)

                # Calcular scores
                confidence_score = self._calculate_confidence_score(
                    processed_data)
                pricing_maturity_score = self._calculate_pricing_maturity_score(
                    processed_data)

                # Adicionar metadados
                processed_data['metadata'] = {
                    'analysis_date': datetime.now(timezone.utc).isoformat(),
                    'company_url': site,
                    'industry': industria,
                    'target_market': target_market,
                    'confidence_score': confidence_score,
                    'pricing_maturity_score': pricing_maturity_score
                }

                # Gerar recomendações
                processed_data['recommendations'] = self._generate_recommendations(
                    processed_data, industria
                )

                return ResearchResult(
                    service_name=self.get_name(),
                    service_version=self.get_version(),
                    timestamp=datetime.now(timezone.utc),
                    cost=self._cost,
                    success=True,
                    data=processed_data,
                    processing_time_seconds=timer.elapsed,
                    confidence_score=confidence_score
                )

            except json.JSONDecodeError as e:
                logger.error(f"Erro ao processar JSON: {str(e)}")
                return self._create_error_result(
                    "Erro ao processar resposta do modelo",
                    timer.elapsed
                )
            except Exception as e:
                logger.error(
                    f"Erro na análise de pricing: {str(e)}", exc_info=True)
                return self._create_error_result(str(e), timer.elapsed)

    def get_sample_output(self) -> Dict[str, Any]:
        """Retorna exemplo de saída do serviço."""
        return {
            "pricing_models": {
                "primary_model": "subscription",
                "models_used": ["subscription", "usage-based", "freemium"],
                "pricing_tiers": [
                    {
                        "name": "Free",
                        "price": 0,
                        "features": ["Basic features", "Community support"],
                        "limitations": ["10 users", "1GB storage"]
                    },
                    {
                        "name": "Pro",
                        "price": 49,
                        "currency": "USD",
                        "billing_cycle": "monthly",
                        "features": ["All features", "Priority support", "API access"],
                        "limitations": ["100 users", "100GB storage"]
                    },
                    {
                        "name": "Enterprise",
                        "price": "custom",
                        "features": ["Custom features", "Dedicated support", "SLA"],
                        "limitations": ["Unlimited"]
                    }
                ]
            },
            "competitive_analysis": {
                "market_position": "premium",
                "price_comparison": {
                    "vs_market_average": "+25%",
                    "vs_direct_competitors": "+15%",
                    "justification": "Superior features and support"
                },
                "competitors": [
                    {
                        "name": "Competitor A",
                        "pricing_range": "$29-$199/month",
                        "model": "subscription"
                    },
                    {
                        "name": "Competitor B",
                        "pricing_range": "$39-$299/month",
                        "model": "subscription + usage"
                    }
                ]
            },
            "pricing_strategy": {
                "elasticity": "moderate",
                "value_proposition": "premium quality and features",
                "monetization_strategies": [
                    "Tiered pricing for different segments",
                    "Usage-based pricing for enterprise",
                    "Freemium for user acquisition"
                ],
                "upsell_opportunities": [
                    "Free to Pro conversion",
                    "Pro to Enterprise expansion",
                    "Add-on services"
                ]
            },
            "revenue_analysis": {
                "arpu_estimate": "$156/month",
                "ltv_estimate": "$3,744",
                "cac_payback_period": "6 months",
                "revenue_growth_potential": "high"
            },
            "metadata": {
                "analysis_date": "2025-01-30T10:00:00Z",
                "company_url": "https://example.com",
                "industry": "SaaS",
                "target_market": "B2B",
                "confidence_score": 0.85,
                "pricing_maturity_score": 0.78
            },
            "recommendations": [
                {
                    "category": "optimization",
                    "suggestion": "Consider introducing annual billing with 15-20% discount",
                    "impact": "high",
                    "effort": "low"
                },
                {
                    "category": "expansion",
                    "suggestion": "Add usage-based pricing for API calls",
                    "impact": "medium",
                    "effort": "medium"
                }
            ]
        }

    def _build_system_prompt(self) -> str:
        """Constrói o prompt do sistema para análise de pricing."""
        return """Você é um especialista em estratégia de precificação e monetização com experiência em:
- Modelos de precificação (SaaS, marketplace, e-commerce, serviços)
- Análise competitiva e posicionamento de mercado
- Elasticidade de preço e psicologia do consumidor
- Estratégias de monetização e maximização de receita
- Análise financeira e métricas de negócio

Sua tarefa é analisar profundamente a estratégia de precificação de uma empresa, considerando:
1. Modelos de precificação utilizados
2. Estrutura de preços e tiers
3. Competitividade no mercado
4. Estratégias de monetização
5. Oportunidades de otimização

Você deve fornecer uma análise objetiva e baseada em dados."""

    def _build_user_prompt(self, empresa: str, site: str, industria: str, target_market: str) -> str:
        """Constrói o prompt do usuário com dados específicos."""
        return f"""Analise a estratégia de precificação da empresa {empresa} ({site}).
Indústria: {industria}
Mercado-alvo: {target_market}

Forneça uma análise completa incluindo:

1. **Modelos de Precificação**:
   - Modelo principal utilizado (subscription, one-time, usage-based, freemium, etc.)
   - Estrutura de preços e tiers disponíveis
   - Features e limitações de cada tier

2. **Análise Competitiva**:
   - Posicionamento de preço (budget, médio, premium)
   - Comparação com concorrentes diretos
   - Justificativa para diferenças de preço

3. **Estratégia de Pricing**:
   - Elasticidade estimada
   - Proposta de valor
   - Estratégias de monetização
   - Oportunidades de upsell/cross-sell

4. **Análise de Receita**:
   - ARPU estimado
   - LTV estimado
   - Potencial de crescimento

IMPORTANTE:
- Baseie-se em informações públicas disponíveis
- Se não encontrar preços exatos, estime com base no mercado
- Indique nível de certeza nas estimativas
- Use "Não disponível" quando não houver informação

Formato de resposta JSON:
{{
    "pricing_models": {{
        "primary_model": "tipo do modelo",
        "models_used": ["lista de modelos"],
        "pricing_tiers": [
            {{
                "name": "nome do tier",
                "price": valor_numerico_ou_custom,
                "currency": "USD",
                "billing_cycle": "monthly/annual",
                "features": ["lista de features"],
                "limitations": ["lista de limitações"]
            }}
        ]
    }},
    "competitive_analysis": {{
        "market_position": "budget/medium/premium",
        "price_comparison": {{
            "vs_market_average": "porcentagem",
            "vs_direct_competitors": "porcentagem",
            "justification": "razão"
        }},
        "competitors": [
            {{
                "name": "nome",
                "pricing_range": "faixa de preço",
                "model": "modelo"
            }}
        ]
    }},
    "pricing_strategy": {{
        "elasticity": "low/moderate/high",
        "value_proposition": "descrição",
        "monetization_strategies": ["lista de estratégias"],
        "upsell_opportunities": ["lista de oportunidades"]
    }},
    "revenue_analysis": {{
        "arpu_estimate": "valor estimado",
        "ltv_estimate": "valor estimado",
        "cac_payback_period": "período",
        "revenue_growth_potential": "low/medium/high"
    }}
}}"""

    def _process_result(self, raw_data: Any, empresa: str) -> Dict[str, Any]:
        """Processa e valida o resultado da análise."""
        # Parse JSON se necessário
        if isinstance(raw_data, str):
            try:
                data = json.loads(raw_data)
            except json.JSONDecodeError:
                logger.warning(f"Resposta não é JSON válido para {empresa}")
                return self._get_empty_pricing_analysis()
        else:
            data = raw_data

        # Validar e normalizar estrutura
        result = self._get_empty_pricing_analysis()

        # AIDEV-NOTE: Processar cada seção garantindo estrutura consistente
        sections = ['pricing_models', 'competitive_analysis',
                    'pricing_strategy', 'revenue_analysis']

        for section in sections:
            if section in data and isinstance(data[section], dict):
                result[section] = self._normalize_section(
                    data[section], section)
            else:
                logger.warning(f"Seção {section} não encontrada ou inválida")

        return result

    def _normalize_section(self, section_data: Dict, section_name: str) -> Dict[str, Any]:
        """Normaliza dados de uma seção específica."""
        if section_name == 'pricing_models':
            return {
                'primary_model': section_data.get('primary_model', 'unknown'),
                'models_used': section_data.get('models_used', []),
                'pricing_tiers': self._normalize_pricing_tiers(
                    section_data.get('pricing_tiers', [])
                )
            }
        elif section_name == 'competitive_analysis':
            return {
                'market_position': section_data.get('market_position', 'unknown'),
                'price_comparison': section_data.get('price_comparison', {}),
                'competitors': section_data.get('competitors', [])
            }
        elif section_name == 'pricing_strategy':
            return {
                'elasticity': section_data.get('elasticity', 'unknown'),
                'value_proposition': section_data.get('value_proposition', ''),
                'monetization_strategies': section_data.get('monetization_strategies', []),
                'upsell_opportunities': section_data.get('upsell_opportunities', [])
            }
        elif section_name == 'revenue_analysis':
            return {
                'arpu_estimate': section_data.get('arpu_estimate', 'N/A'),
                'ltv_estimate': section_data.get('ltv_estimate', 'N/A'),
                'cac_payback_period': section_data.get('cac_payback_period', 'N/A'),
                'revenue_growth_potential': section_data.get('revenue_growth_potential', 'unknown')
            }

        return section_data

    def _normalize_pricing_tiers(self, tiers: List[Any]) -> List[Dict[str, Any]]:
        """Normaliza informações de tiers de preço."""
        normalized_tiers = []

        for tier in tiers:
            if isinstance(tier, dict):
                normalized_tier = {
                    'name': tier.get('name', 'Unknown'),
                    'price': tier.get('price', 0),
                    'currency': tier.get('currency', 'USD'),
                    'billing_cycle': tier.get('billing_cycle', 'monthly'),
                    'features': tier.get('features', []),
                    'limitations': tier.get('limitations', [])
                }
                normalized_tiers.append(normalized_tier)

        return normalized_tiers

    def _calculate_confidence_score(self, data: Dict[str, Any]) -> float:
        """Calcula score de confiança baseado na completude dos dados."""
        total_checks = 0
        passed_checks = 0

        # Verificar pricing_models
        if 'pricing_models' in data:
            total_checks += 3
            if data['pricing_models'].get('primary_model') != 'unknown':
                passed_checks += 1
            if len(data['pricing_models'].get('models_used', [])) > 0:
                passed_checks += 1
            if len(data['pricing_models'].get('pricing_tiers', [])) > 0:
                passed_checks += 1

        # Verificar competitive_analysis
        if 'competitive_analysis' in data:
            total_checks += 2
            if data['competitive_analysis'].get('market_position') != 'unknown':
                passed_checks += 1
            if len(data['competitive_analysis'].get('competitors', [])) > 0:
                passed_checks += 1

        # Verificar pricing_strategy
        if 'pricing_strategy' in data:
            total_checks += 2
            if data['pricing_strategy'].get('elasticity') != 'unknown':
                passed_checks += 1
            if len(data['pricing_strategy'].get('monetization_strategies', [])) > 0:
                passed_checks += 1

        # Verificar revenue_analysis
        if 'revenue_analysis' in data:
            total_checks += 2
            if data['revenue_analysis'].get('arpu_estimate') != 'N/A':
                passed_checks += 1
            if data['revenue_analysis'].get('revenue_growth_potential') != 'unknown':
                passed_checks += 1

        return round(passed_checks / total_checks if total_checks > 0 else 0.0, 2)

    def _calculate_pricing_maturity_score(self, data: Dict[str, Any]) -> float:
        """
        Calcula score de maturidade da estratégia de pricing.

        Considera:
        - Diversidade de modelos de precificação (25%)
        - Estrutura de tiers bem definida (25%)
        - Análise competitiva clara (25%)
        - Estratégias de monetização avançadas (25%)
        """
        score = 0.0

        # Diversidade de modelos (25%)
        models_used = len(
            data.get('pricing_models', {}).get('models_used', []))
        if models_used >= 3:
            score += 0.25
        elif models_used == 2:
            score += 0.15
        elif models_used == 1:
            score += 0.10

        # Estrutura de tiers (25%)
        tiers = data.get('pricing_models', {}).get('pricing_tiers', [])
        if len(tiers) >= 3:
            score += 0.25
        elif len(tiers) == 2:
            score += 0.15
        elif len(tiers) == 1:
            score += 0.10

        # Análise competitiva (25%)
        competitors = data.get('competitive_analysis',
                               {}).get('competitors', [])
        if len(competitors) >= 2 and data.get('competitive_analysis', {}).get('market_position') != 'unknown':
            score += 0.25
        elif len(competitors) >= 1:
            score += 0.15

        # Estratégias de monetização (25%)
        strategies = len(data.get('pricing_strategy', {}).get(
            'monetization_strategies', []))
        if strategies >= 3:
            score += 0.25
        elif strategies == 2:
            score += 0.15
        elif strategies == 1:
            score += 0.10

        return round(score, 2)

    def _generate_recommendations(self, data: Dict[str, Any], industria: str) -> List[Dict[str, Any]]:
        """Gera recomendações baseadas na análise."""
        recommendations = []

        # AIDEV-NOTE: Lógica de recomendações baseada em gaps identificados

        # Verificar se tem poucos tiers
        tiers = data.get('pricing_models', {}).get('pricing_tiers', [])
        if len(tiers) < 3:
            recommendations.append({
                'category': 'expansion',
                'suggestion': 'Considere adicionar mais opções de tiers para segmentar melhor o mercado',
                'impact': 'high',
                'effort': 'medium'
            })

        # Verificar se não tem billing anual
        has_annual = any(tier.get('billing_cycle') ==
                         'annual' for tier in tiers)
        if not has_annual and len(tiers) > 0:
            recommendations.append({
                'category': 'optimization',
                'suggestion': 'Implemente opção de billing anual com desconto de 15-20%',
                'impact': 'high',
                'effort': 'low'
            })

        # Verificar elasticidade
        elasticity = data.get('pricing_strategy', {}).get('elasticity', '')
        if elasticity == 'high':
            recommendations.append({
                'category': 'testing',
                'suggestion': 'Realize A/B testing de preços para encontrar o ponto ótimo',
                'impact': 'medium',
                'effort': 'medium'
            })

        # Sempre incluir pelo menos uma recomendação genérica
        if not recommendations:
            recommendations.append({
                'category': 'analysis',
                'suggestion': 'Realize análise detalhada de willingness-to-pay dos clientes',
                'impact': 'medium',
                'effort': 'high'
            })

        return recommendations

    def _get_empty_pricing_analysis(self) -> Dict[str, Any]:
        """Retorna estrutura vazia de análise de pricing."""
        return {
            "pricing_models": {
                "primary_model": "unknown",
                "models_used": [],
                "pricing_tiers": []
            },
            "competitive_analysis": {
                "market_position": "unknown",
                "price_comparison": {},
                "competitors": []
            },
            "pricing_strategy": {
                "elasticity": "unknown",
                "value_proposition": "",
                "monetization_strategies": [],
                "upsell_opportunities": []
            },
            "revenue_analysis": {
                "arpu_estimate": "N/A",
                "ltv_estimate": "N/A",
                "cac_payback_period": "N/A",
                "revenue_growth_potential": "unknown"
            }
        }

    def _get_mock_data(self, empresa: str, industria: str) -> Dict[str, Any]:
        """Retorna dados mock para desenvolvimento."""
        # AIDEV-NOTE: Mock data rico para diferentes cenários
        mock_scenarios = {
            'default': {
                "pricing_models": {
                    "primary_model": "subscription",
                    "models_used": ["subscription", "freemium"],
                    "pricing_tiers": [
                        {
                            "name": "Free",
                            "price": 0,
                            "currency": "USD",
                            "billing_cycle": "monthly",
                            "features": ["Basic features", "Community support", "1 user"],
                            "limitations": ["Limited API calls", "No advanced features"]
                        },
                        {
                            "name": "Professional",
                            "price": 99,
                            "currency": "USD",
                            "billing_cycle": "monthly",
                            "features": ["All features", "Priority support", "Unlimited users", "API access"],
                            "limitations": ["Fair use policy"]
                        },
                        {
                            "name": "Enterprise",
                            "price": "custom",
                            "currency": "USD",
                            "billing_cycle": "annual",
                            "features": ["Custom features", "Dedicated support", "SLA", "Training"],
                            "limitations": []
                        }
                    ]
                },
                "competitive_analysis": {
                    "market_position": "premium",
                    "price_comparison": {
                        "vs_market_average": "+30%",
                        "vs_direct_competitors": "+20%",
                        "justification": "Superior technology and customer support"
                    },
                    "competitors": [
                        {
                            "name": "CompetitorTech",
                            "pricing_range": "$49-$299/month",
                            "model": "subscription"
                        },
                        {
                            "name": "MarketLeader",
                            "pricing_range": "$79-$499/month",
                            "model": "subscription + usage"
                        }
                    ]
                },
                "pricing_strategy": {
                    "elasticity": "moderate",
                    "value_proposition": "Enterprise-grade solution with superior ROI",
                    "monetization_strategies": [
                        "Freemium for user acquisition",
                        "Value-based pricing for enterprise",
                        "Add-on services for additional revenue"
                    ],
                    "upsell_opportunities": [
                        "Free to Professional conversion",
                        "Professional to Enterprise expansion",
                        "Additional seats and storage",
                        "Premium support packages"
                    ]
                },
                "revenue_analysis": {
                    "arpu_estimate": "$189/month",
                    "ltv_estimate": "$4,536",
                    "cac_payback_period": "7 months",
                    "revenue_growth_potential": "high"
                }
            },
            'saas': {
                "pricing_models": {
                    "primary_model": "subscription",
                    "models_used": ["subscription", "usage-based", "freemium"],
                    "pricing_tiers": [
                        {
                            "name": "Starter",
                            "price": 29,
                            "currency": "USD",
                            "billing_cycle": "monthly",
                            "features": ["Core features", "Email support", "5 users"],
                            "limitations": ["1,000 API calls/month", "Basic analytics"]
                        },
                        {
                            "name": "Growth",
                            "price": 149,
                            "currency": "USD",
                            "billing_cycle": "monthly",
                            "features": ["Advanced features", "Chat support", "50 users", "Advanced analytics"],
                            "limitations": ["10,000 API calls/month"]
                        },
                        {
                            "name": "Scale",
                            "price": 499,
                            "currency": "USD",
                            "billing_cycle": "monthly",
                            "features": ["All features", "Phone support", "Unlimited users", "Custom integrations"],
                            "limitations": ["100,000 API calls/month"]
                        }
                    ]
                },
                "competitive_analysis": {
                    "market_position": "medium",
                    "price_comparison": {
                        "vs_market_average": "+5%",
                        "vs_direct_competitors": "-10%",
                        "justification": "Competitive pricing with better feature set"
                    },
                    "competitors": [
                        {
                            "name": "SaaSCompetitor1",
                            "pricing_range": "$39-$599/month",
                            "model": "subscription"
                        },
                        {
                            "name": "SaaSCompetitor2",
                            "pricing_range": "$25-$399/month",
                            "model": "subscription"
                        }
                    ]
                },
                "pricing_strategy": {
                    "elasticity": "high",
                    "value_proposition": "Best value for growing teams",
                    "monetization_strategies": [
                        "Land and expand strategy",
                        "Usage-based pricing for overages",
                        "Annual billing discounts"
                    ],
                    "upsell_opportunities": [
                        "Seat expansion",
                        "API usage upgrades",
                        "Add-on modules"
                    ]
                },
                "revenue_analysis": {
                    "arpu_estimate": "$142/month",
                    "ltv_estimate": "$3,408",
                    "cac_payback_period": "5 months",
                    "revenue_growth_potential": "very high"
                }
            },
            'ecommerce': {
                "pricing_models": {
                    "primary_model": "marketplace",
                    "models_used": ["transaction-based", "subscription", "freemium"],
                    "pricing_tiers": [
                        {
                            "name": "Basic Store",
                            "price": 0,
                            "currency": "USD",
                            "billing_cycle": "monthly",
                            "features": ["Online store", "Basic themes", "2% transaction fee"],
                            "limitations": ["Limited customization", "Basic reports"]
                        },
                        {
                            "name": "Professional",
                            "price": 79,
                            "currency": "USD",
                            "billing_cycle": "monthly",
                            "features": ["Advanced themes", "1% transaction fee", "Advanced reports"],
                            "limitations": ["Standard support"]
                        },
                        {
                            "name": "Plus",
                            "price": 299,
                            "currency": "USD",
                            "billing_cycle": "monthly",
                            "features": ["Custom themes", "0.5% transaction fee", "Priority support"],
                            "limitations": []
                        }
                    ]
                },
                "competitive_analysis": {
                    "market_position": "budget",
                    "price_comparison": {
                        "vs_market_average": "-20%",
                        "vs_direct_competitors": "-15%",
                        "justification": "Competitive pricing to gain market share"
                    },
                    "competitors": [
                        {
                            "name": "BigCommerce",
                            "pricing_range": "$95-$400/month",
                            "model": "subscription"
                        },
                        {
                            "name": "WooCommerce",
                            "pricing_range": "$0-$300/month",
                            "model": "freemium + hosting"
                        }
                    ]
                },
                "pricing_strategy": {
                    "elasticity": "high",
                    "value_proposition": "Affordable e-commerce for everyone",
                    "monetization_strategies": [
                        "Transaction fees on lower tiers",
                        "App marketplace revenue share",
                        "Payment processing fees"
                    ],
                    "upsell_opportunities": [
                        "Reduced transaction fees",
                        "Premium themes and apps",
                        "Marketing tools"
                    ]
                },
                "revenue_analysis": {
                    "arpu_estimate": "$98/month",
                    "ltv_estimate": "$2,352",
                    "cac_payback_period": "4 months",
                    "revenue_growth_potential": "medium"
                }
            }
        }

        # Selecionar cenário baseado na indústria
        if 'saas' in industria.lower() or 'software' in industria.lower():
            return mock_scenarios['saas']
        elif 'commerce' in industria.lower() or 'retail' in industria.lower():
            return mock_scenarios['ecommerce']
        else:
            return mock_scenarios['default']

    def _create_error_result(self, error_message: str, elapsed_time: float) -> ResearchResult:
        """Cria resultado de erro padronizado."""
        return ResearchResult(
            service_name=self.get_name(),
            service_version=self.get_version(),
            timestamp=datetime.now(timezone.utc),
            cost=Decimal("0"),
            success=False,
            error=error_message,
            processing_time_seconds=elapsed_time,
            data=self._get_empty_pricing_analysis()
        )
