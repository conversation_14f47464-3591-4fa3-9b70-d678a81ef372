# Serviços de Pesquisa Modulares

Este diretório contém os serviços de pesquisa modulares do ScopeAI. Cada serviço é independente, tem seu próprio custo e pode ser executado individualmente ou em conjunto.

## 📋 Visão Geral

### Motivação
- **Antes**: `perplexity.py` monolítico com 2.070 linhas, custo fixo de $0.05
- **Agora**: 11 serviços independentes (~200 linhas cada), custo flexível $0.006-$0.05
- **Benefícios**: Usuário escolhe o que pesquisar, economia de custos, execução paralela

### Arquitetura
```
services/
├── research/
│   ├── basic_dossier_service.py      # $0.006 - <PERSON><PERSON><PERSON> básico
│   ├── swot_analysis_service.py      # $0.005 - Análise SWOT expandida
│   ├── tech_stack_service.py         # $0.005 - <PERSON>ack tecnológico
│   ├── funding_history_service.py    # $0.008 - Histórico de funding
│   ├── market_research_service.py    # $0.007 - Pesquisa de mercado
│   ├── digital_presence_service.py   # $0.006 - Presença digital
│   ├── partnerships_service.py       # $0.005 - Parcerias
│   ├── pricing_analysis_service.py   # $0.005 - Análise de pricing
│   ├── business_model_service.py     # $0.006 - Modelo de negócio
│   ├── channels_reviews_service.py   # $0.005 - Canais e reviews
│   └── tech_diagnostic_service.py    # $0.010 - Diagnóstico técnico
└── research_orchestrator.py          # Coordenador central
```

## 🚀 Como Criar um Novo Serviço

### 1. Template Base

```python
"""
[Nome do Serviço] - [Descrição breve]

Este serviço [o que faz].

Custo: $[custo] por execução
"""
import json
import logging
from typing import Dict, Any, List, Optional
from decimal import Decimal
from datetime import datetime

from app.core import (
    IResearchService,
    ResearchRequest,
    ResearchResult,
    Timer
)

logger = logging.getLogger(__name__)


class [NomeDoServico]Service(IResearchService):
    """
    [Descrição detalhada do serviço]
    """
    
    def __init__(self, provider):
        """
        Args:
            provider: Instância do provider (Perplexity, SearchSociety, etc)
        """
        self.provider = provider
        self._version = "1.0.0"
        self._cost = Decimal("[custo]")  # Ex: Decimal("0.005")
        
    def get_name(self) -> str:
        return "[nome_unico_do_servico]"  # Ex: "tech_stack"
        
    def get_version(self) -> str:
        return self._version
        
    def get_description(self) -> str:
        return "[Descrição do que o serviço retorna]"
        
    def get_cost(self) -> Decimal:
        return self._cost
        
    def get_required_fields(self) -> List[str]:
        # Campos obrigatórios do ResearchRequest
        return ["company_name", "company_url"]
        
    def get_optional_fields(self) -> List[str]:
        # Campos opcionais que o serviço pode usar
        return ["city", "state", "industry"]
        
    async def validate_request(self, request: ResearchRequest) -> bool:
        """Valida se a requisição tem os dados necessários."""
        # Implementar validação específica
        if not request.company_name or not request.company_url:
            return False
        return True
        
    async def execute(self, request: ResearchRequest) -> ResearchResult:
        """Executa a pesquisa."""
        with Timer() as timer:
            try:
                # 1. Preparar dados
                empresa = request.company_name
                site = request.company_url
                
                # 2. Construir prompts
                system_prompt = self._build_system_prompt()
                user_prompt = self._build_user_prompt(empresa, site)
                
                # 3. Fazer requisição ao provider
                logger.info(f"Executando {self.get_name()} para {empresa}")
                result_data = await self.provider.search(
                    system_prompt=system_prompt,
                    user_prompt=user_prompt,
                    temperature=0.7
                )
                
                # 4. Processar resultado
                processed_data = self._process_result(result_data)
                
                # 5. Calcular confidence score
                confidence_score = self._calculate_confidence_score(processed_data)
                
                return ResearchResult(
                    service_name=self.get_name(),
                    service_version=self.get_version(),
                    timestamp=datetime.utcnow(),
                    cost=self._cost,
                    success=True,
                    data=processed_data,
                    processing_time_seconds=timer.elapsed,
                    confidence_score=confidence_score
                )
                
            except Exception as e:
                logger.error(f"Erro em {self.get_name()}: {str(e)}")
                return ResearchResult(
                    service_name=self.get_name(),
                    service_version=self.get_version(),
                    timestamp=datetime.utcnow(),
                    cost=Decimal("0"),
                    success=False,
                    error=str(e),
                    processing_time_seconds=timer.elapsed
                )
                
    def get_sample_output(self) -> Dict[str, Any]:
        """Retorna exemplo de saída."""
        return {
            # Estrutura esperada da saída
        }
        
    def _build_system_prompt(self) -> str:
        """Constrói o prompt do sistema."""
        return "Você é um especialista em..."
        
    def _build_user_prompt(self, empresa: str, site: str) -> str:
        """Constrói o prompt do usuário."""
        return f"Analise {empresa}..."
        
    def _process_result(self, raw_data: Any) -> Dict[str, Any]:
        """Processa e valida o resultado."""
        # Implementar processamento
        return raw_data
        
    def _calculate_confidence_score(self, data: Dict[str, Any]) -> float:
        """Calcula score de confiança (0.0 a 1.0)."""
        # Implementar cálculo baseado na completude
        return 0.85
```

### 2. Padrões de Implementação

#### Validação de Request
```python
async def validate_request(self, request: ResearchRequest) -> bool:
    # Verificar campos obrigatórios
    if not request.company_name or not request.company_url:
        return False
        
    # Validações específicas do serviço
    if self.get_name() == "tech_stack":
        # Tech stack precisa de URL válida
        if not request.company_url.startswith(('http://', 'https://')):
            return False
            
    return True
```

#### Processamento de Resultados
```python
def _process_result(self, raw_data: Any) -> Dict[str, Any]:
    # 1. Parse JSON se necessário
    if isinstance(raw_data, str):
        try:
            data = json.loads(raw_data)
        except json.JSONDecodeError:
            logger.error("Resposta inválida")
            return self._get_empty_result()
    else:
        data = raw_data
        
    # 2. Validar estrutura
    required_keys = ["campo1", "campo2", "campo3"]
    for key in required_keys:
        if key not in data:
            data[key] = self._get_default_for_key(key)
            
    return data
```

#### Cálculo de Confidence Score
```python
def _calculate_confidence_score(self, data: Dict[str, Any]) -> float:
    """
    Baseie o score na completude e qualidade dos dados.
    """
    total_checks = 0
    passed_checks = 0
    
    # Verificar campos principais
    checks = [
        # (descrição, função de verificação)
        ("tem_campo_x", lambda d: "campo_x" in d and d["campo_x"]),
        ("lista_não_vazia", lambda d: len(d.get("lista", [])) > 0),
        ("valor_válido", lambda d: d.get("valor", 0) > 0)
    ]
    
    for _, check_func in checks:
        total_checks += 1
        try:
            if check_func(data):
                passed_checks += 1
        except:
            pass
            
    return passed_checks / total_checks if total_checks > 0 else 0.0
```

### 3. Registrar o Serviço

Em `research_orchestrator.py`, função `_auto_register_services`:

```python
def _auto_register_services(registry: ServiceRegistry) -> None:
    """Auto-registra todos os serviços disponíveis."""
    from app.services.research import (
        BasicDossierService,
        SwotAnalysisService,  # Novo serviço
        # ... outros serviços
    )
    
    # Criar provider (será injetado no futuro)
    from app.infrastructure.providers import PerplexityProvider
    provider = PerplexityProvider()
    
    # Registrar serviços
    registry.register(BasicDossierService(provider))
    registry.register(SwotAnalysisService(provider))  # Novo
    # ... outros registros
```

### 4. Adicionar ao __init__.py

Em `services/research/__init__.py`:

```python
from .basic_dossier_service import BasicDossierService
from .swot_analysis_service import SwotAnalysisService  # Novo

__all__ = [
    'BasicDossierService',
    'SwotAnalysisService',  # Novo
]
```

## 🧪 Testando o Serviço

### Template de Teste

```python
"""
Testes para [NomeDoServico]Service.
"""
import pytest
from unittest.mock import Mock, AsyncMock
from decimal import Decimal

from app.services.research import [NomeDoServico]Service
from app.core import ResearchRequest, ResearchResult


class Test[NomeDoServico]Service:
    
    @pytest.fixture
    def mock_provider(self):
        provider = Mock()
        provider.search = AsyncMock()
        return provider
        
    @pytest.fixture
    def service(self, mock_provider):
        return [NomeDoServico]Service(mock_provider)
        
    def test_metadata(self, service):
        assert service.get_name() == "[nome_servico]"
        assert service.get_cost() == Decimal("[custo]")
        
    @pytest.mark.asyncio
    async def test_execute_success(self, service, mock_provider):
        # Configurar mock
        mock_provider.search.return_value = {"resultado": "teste"}
        
        # Executar
        request = ResearchRequest(
            client_id="test",
            company_name="Test Corp",
            company_url="https://test.com"
        )
        result = await service.execute(request)
        
        # Verificar
        assert result.success is True
        assert result.cost == Decimal("[custo]")
```

## 📊 Métricas de Qualidade

Cada serviço deve ter:
- ✅ Menos de 300 linhas de código
- ✅ Cobertura de testes > 80%
- ✅ Complexidade ciclomática < 10
- ✅ Docstrings completas
- ✅ Type hints em 100% do código
- ✅ Tratamento de erros robusto
- ✅ Logs informativos
- ✅ Confidence score baseado em dados

## 🔄 Fluxo de Execução

1. **Cliente faz requisição** → API recebe lista de serviços desejados
2. **Orchestrator valida** → Verifica se serviços existem e request é válido
3. **Execução paralela** → Até 5 serviços simultâneos por padrão
4. **Consolidação** → Resultados agregados com métricas
5. **Resposta** → JSON com dados de todos os serviços + resumo

## 💡 Dicas de Implementação

### 1. Prompts Eficazes
- Seja específico sobre o formato de saída (JSON)
- Inclua exemplos concretos no prompt
- Use "Dado não encontrado" para campos vazios
- Solicite dados verificáveis e atualizados

### 2. Otimização de Custos
- Cache resultados quando possível (Redis)
- Agrupe requisições similares
- Use temperature baixa (0.5-0.7) para consistência
- Implemente retry com backoff exponencial

### 3. Melhores Práticas
- Um serviço = uma responsabilidade
- Falhe graciosamente (retorne dados parciais)
- Log de todas as operações importantes
- Métricas de performance (Timer)
- Validação rigorosa de inputs

## 📈 Roadmap

### Implementados
- [x] basic_dossier_service.py
- [x] swot_analysis_service.py
- [x] tech_stack_service.py
- [x] funding_history_service.py
- [x] market_research_service.py
- [x] digital_presence_service.py
- [x] partnerships_service.py
- [x] pricing_analysis_service.py
- [x] business_model_service.py

### Próximos (Prioridade Alta)
- [ ] channels_reviews_service.py
- [ ] tech_diagnostic_service.py

## 🔗 Recursos

- [Interfaces Base](../core/interfaces.py)
- [Research Orchestrator](../research_orchestrator.py)
- [Exemplo Completo](./basic_dossier_service.py)
- [Testes de Exemplo](../../tests/test_basic_dossier_service.py) 