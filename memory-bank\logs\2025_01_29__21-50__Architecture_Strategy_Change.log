# Architecture Strategy Change Log

**Data**: 2025-01-29 21:50  
**Executor**: <PERSON> (com AI Assistant)  
**Tipo**: Mudança de Estratégia Arquitetural

## Contexto
Durante a revisão da TAREFA 1.1 (Setup Clean Architecture Structure), foi identificado que a abordagem de Clean Architecture com DDD seria overengineering para o projeto ScopeAI.

## Decisão
Adotar **Arquitetura Simples** conforme definido em `.cursor/rules/development/python.mdc`:
- Estrutura pragmática: app/routes, services, core, config, tests
- Foco em simplicidade e evolução incremental
- Sem layers complexos (domain, application, infrastructure)

## Mudanças Principais

### 1. Estrutura de Diretórios
```
backend/
├── app/
│   ├── config/           # Configurações
│   ├── routes/           # Endpoints FastAPI
│   ├── services/         # Lógica de negócio
│   │   └── research/     # 11 serviços de pesquisa modular
│   ├── core/             # Compartilhado
│   └── tests/            # Testes
```

### 2. Serviços de Pesquisa Modulares
- **De**: perplexity.py monolítico (2.070 linhas)
- **Para**: 11 serviços independentes (~200 linhas cada)
- **Benefício**: Usuário escolhe o que pesquisar, economia de custos

### 3. Custos Flexíveis
- **Antes**: $0.05 fixo por análise completa
- **Depois**: $0.006-0.05 baseado nas pesquisas escolhidas

## Arquivos Atualizados
1. `memory-bank/activeContext.md` - v1.0.2
2. `memory-bank/progress.md` - v1.0.2  
3. `.projectrules` - Removida referência a DDD/Hexagonal
4. `README.md` - Mantido (foco em uso, não arquitetura interna)

## Impacto
- Redução de complexidade significativa
- Maior velocidade de desenvolvimento
- Facilita onboarding de novos desenvolvedores
- Mantém todos os benefícios (modularidade, testabilidade)

## Próximos Passos
1. Implementar TAREFA 1.1 com estrutura simples
2. Criar interface base para research services
3. Implementar primeiro serviço modular como exemplo
4. Migrar incrementalmente os God Files

## Validação
- [x] Memory bank atualizado
- [x] activeContext.md reflete nova direção
- [x] progress.md atualizado com nova abordagem
- [x] .projectrules alinhado com python.mdc
- [x] Estratégia documentada

---
*Log gerado automaticamente durante atualização do memory bank* 