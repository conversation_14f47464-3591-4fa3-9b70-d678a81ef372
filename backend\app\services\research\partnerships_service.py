"""
Partnership Analysis Service

This service analyzes a company's partnership ecosystem including commercial partnerships,
technology integrations, and strategic alliances.

Cost: $0.005 per analysis
"""

from typing import Dict, Any, Optional, List
from datetime import datetime, UTC
from decimal import Decimal
import logging
import json

from ...core.interfaces import IResearchService, IResearchProvider, ResearchRequest, ResearchResult
from ...core.exceptions import ResearchException

logger = logging.getLogger(__name__)


class PartnershipAnalysis:
    """Represents the result of a partnership ecosystem analysis"""

    def __init__(
        self,
        commercial_partnerships: Dict[str, Any],
        technology_partnerships: Dict[str, Any],
        strategic_partnerships: Dict[str, Any],
        partnership_ecosystem_score: float,
        recommendations: List[str],
        confidence_score: float,
        raw_data: Optional[Dict[str, Any]] = None
    ):
        self.commercial_partnerships = commercial_partnerships
        self.technology_partnerships = technology_partnerships
        self.strategic_partnerships = strategic_partnerships
        self.partnership_ecosystem_score = partnership_ecosystem_score
        self.recommendations = recommendations
        self.confidence_score = confidence_score
        self.raw_data = raw_data or {}
        self.timestamp = datetime.now(UTC)
        self.service_name = "partnerships"
        self.cost = 0.005

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format"""
        return {
            "service_name": self.service_name,
            "timestamp": self.timestamp.isoformat(),
            "cost": self.cost,
            "confidence_score": self.confidence_score,
            "partnership_ecosystem_score": self.partnership_ecosystem_score,
            "commercial_partnerships": self.commercial_partnerships,
            "technology_partnerships": self.technology_partnerships,
            "strategic_partnerships": self.strategic_partnerships,
            "recommendations": self.recommendations,
            "raw_data": self.raw_data
        }


class PartnershipService(IResearchService):
    """
    Service for analyzing company's partnership ecosystem

    Analyzes:
    - Commercial partnerships (distributors, resellers, suppliers)
    - Technology partnerships (integrations, APIs, certifications)
    - Strategic partnerships (investors, joint ventures, co-marketing)
    """

    def __init__(self, provider: Optional[IResearchProvider] = None):
        """
        Initialize the service

        Args:
            provider: Research provider for data fetching (optional)
        """
        self.provider = provider
        self.cost_per_analysis = 0.005
        self.service_name = "partnerships"
        self.version = "1.0.0"

    # Implementação dos métodos abstratos da IResearchService
    def get_name(self) -> str:
        """Retorna o nome único do serviço."""
        return self.service_name

    def get_version(self) -> str:
        """Retorna a versão do serviço."""
        return self.version

    def get_description(self) -> str:
        """Retorna uma descrição do que o serviço faz."""
        return (
            "Analisa o ecossistema de parcerias de uma empresa incluindo parcerias comerciais "
            "(distribuidores, revendedores), tecnológicas (integrações, APIs) e estratégicas "
            "(investidores, joint ventures). Fornece score de maturidade e recomendações."
        )

    def get_cost(self) -> Decimal:
        """Retorna o custo estimado em USD para executar este serviço."""
        return Decimal(str(self.cost_per_analysis))

    def get_required_fields(self) -> List[str]:
        """Retorna lista de campos obrigatórios no ResearchRequest."""
        return ["company_name"]

    def get_optional_fields(self) -> List[str]:
        """Retorna lista de campos opcionais que o serviço pode usar."""
        return ["company_url", "industry", "additional_context"]

    async def validate_request(self, request: ResearchRequest) -> bool:
        """
        Valida se a requisição tem todos os dados necessários.

        Args:
            request: Requisição de pesquisa

        Returns:
            True se válida, False caso contrário
        """
        # Verifica se tem o campo obrigatório
        if not request.company_name or request.company_name.strip() == "":
            logger.error("Company name is required")
            return False

        return True

    async def execute(self, request: ResearchRequest) -> ResearchResult:
        """
        Executa a pesquisa e retorna o resultado.

        Args:
            request: Requisição de pesquisa com dados do cliente

        Returns:
            ResearchResult com os dados coletados ou erro
        """
        start_time = datetime.now(UTC)

        try:
            # Valida a requisição
            if not await self.validate_request(request):
                return ResearchResult(
                    service_name=self.get_name(),
                    service_version=self.get_version(),
                    timestamp=datetime.now(UTC),
                    cost=Decimal("0"),
                    success=False,
                    error="Invalid request: company_name is required",
                    processing_time_seconds=0
                )

            # Prepara o contexto
            context = {
                "industry": request.additional_context.get("industry", "unknown") if request.additional_context else "unknown"
            }
            if request.company_url:
                context["website"] = request.company_url
            if request.additional_context:
                context.update(request.additional_context)

            # Executa a análise
            analysis = await self.analyze(request.company_name, context)

            # Calcula tempo de processamento
            processing_time = (datetime.now(UTC) - start_time).total_seconds()

            # Retorna resultado de sucesso
            return ResearchResult(
                service_name=self.get_name(),
                service_version=self.get_version(),
                timestamp=analysis.timestamp,
                cost=self.get_cost(),
                success=True,
                data=analysis.to_dict(),
                processing_time_seconds=processing_time,
                confidence_score=analysis.confidence_score / 100  # Converte para 0-1
            )

        except Exception as e:
            logger.error(f"Error executing partnership analysis: {str(e)}")
            processing_time = (datetime.now(UTC) - start_time).total_seconds()

            return ResearchResult(
                service_name=self.get_name(),
                service_version=self.get_version(),
                timestamp=datetime.now(UTC),
                cost=Decimal("0"),
                success=False,
                error=str(e),
                processing_time_seconds=processing_time
            )

    def get_sample_output(self) -> Dict[str, Any]:
        """
        Retorna um exemplo do formato de saída esperado.

        Útil para documentação e testes.
        """
        return {
            "service_name": "partnerships",
            "timestamp": "2025-01-30T12:00:00Z",
            "cost": 0.005,
            "confidence_score": 82.5,
            "partnership_ecosystem_score": 72.5,
            "commercial_partnerships": {
                "distributors": [
                    {
                        "name": "Tech Distributor LATAM",
                        "type": "distributor",
                        "regions": ["Brazil", "Argentina", "Chile"],
                        "since": "2021",
                        "exclusive": False
                    }
                ],
                "resellers": [
                    {
                        "name": "Cloud Solutions Partner",
                        "type": "reseller",
                        "tier": "Gold",
                        "certifications": ["Cloud Expert", "Security Pro"]
                    }
                ],
                "suppliers": [
                    {
                        "name": "AWS",
                        "type": "infrastructure",
                        "critical": True,
                        "dependency_level": "high"
                    }
                ],
                "revenue_impact": "25-30%",
                "partner_network_size": 45,
                "geographic_coverage": ["LATAM", "North America"]
            },
            "technology_partnerships": {
                "integrations": [
                    {
                        "partner": "Salesforce",
                        "type": "CRM Integration",
                        "api_level": "deep",
                        "users": 5000
                    }
                ],
                "certifications": [
                    "Microsoft Gold Partner",
                    "AWS Advanced Technology Partner"
                ],
                "marketplace_presence": [
                    "AWS Marketplace",
                    "Microsoft AppSource"
                ],
                "api_ecosystem": {
                    "public_apis": 5,
                    "partner_apis": 12,
                    "developers": 200
                }
            },
            "strategic_partnerships": {
                "investors": [
                    {
                        "name": "Sequoia Capital",
                        "type": "VC",
                        "round": "Series B",
                        "strategic_value": "high"
                    }
                ],
                "joint_ventures": [
                    {
                        "partner": "Enterprise Corp",
                        "focus": "Enterprise Solutions",
                        "started": "2023"
                    }
                ],
                "co_marketing": [
                    "Microsoft",
                    "Google Cloud"
                ],
                "media_partners": [
                    "TechCrunch",
                    "InfoMoney"
                ]
            },
            "recommendations": [
                "Expandir rede de parceiros tecnológicos na Europa",
                "Desenvolver programa formal de certificação para parceiros",
                "Buscar parceria estratégica com player de cybersecurity"
            ]
        }

    async def analyze(
        self,
        company_name: str,
        context: Optional[Dict[str, Any]] = None
    ) -> PartnershipAnalysis:
        """
        Analyze company's partnership ecosystem

        Args:
            company_name: Name of the company to analyze
            context: Additional context (industry, website, etc.)

        Returns:
            PartnershipAnalysis object with comprehensive partnership metrics
        """
        logger.info(f"Starting partnership analysis for {company_name}")

        try:
            # AIDEV-NOTE: Mock implementation - replace with actual provider call
            if self.provider:
                query = self._build_analysis_query(company_name, context)
                raw_data = await self.provider.search(query, max_tokens=2000)
            else:
                raw_data = self._get_mock_data(company_name)

            # Parse and structure the analysis
            commercial_partnerships = self._analyze_commercial_partnerships(
                raw_data, context)
            technology_partnerships = self._analyze_technology_partnerships(
                raw_data, context)
            strategic_partnerships = self._analyze_strategic_partnerships(
                raw_data, context)

            # Calculate partnership ecosystem score
            partnership_ecosystem_score = self._calculate_partnership_ecosystem_score(
                commercial_partnerships, technology_partnerships, strategic_partnerships
            )

            # Generate recommendations
            recommendations = self._generate_recommendations(
                commercial_partnerships, technology_partnerships, strategic_partnerships
            )

            # Calculate confidence score
            confidence_score = self._calculate_confidence_score(
                commercial_partnerships, technology_partnerships, strategic_partnerships
            )

            return PartnershipAnalysis(
                commercial_partnerships=commercial_partnerships,
                technology_partnerships=technology_partnerships,
                strategic_partnerships=strategic_partnerships,
                partnership_ecosystem_score=partnership_ecosystem_score,
                recommendations=recommendations,
                confidence_score=confidence_score,
                raw_data=raw_data
            )

        except Exception as e:
            logger.error(f"Error analyzing partnerships: {str(e)}")
            raise ResearchException(
                f"Failed to analyze partnerships: {str(e)}")

    def _build_analysis_query(self, company_name: str, context: Optional[Dict[str, Any]]) -> str:
        """Build query for partnership analysis"""
        industry = context.get(
            'industry', 'technology') if context else 'technology'
        website = context.get('website', '') if context else ''

        return f"""
        Analyze the partnership ecosystem of {company_name} {f'({industry} industry)' if industry != 'unknown' else ''} {f'website: {website}' if website else ''}:
        
        1. Commercial Partnerships:
           - Distributors and distribution agreements
           - Resellers and channel partners
           - Key suppliers and vendors
           - Revenue contribution from partners
           - Geographic coverage through partners
           - Exclusive vs non-exclusive agreements
        
        2. Technology Partnerships:
           - Technology integrations and APIs
           - Certification partnerships
           - Marketplace presence
           - Joint product development
           - Technology stack dependencies
           - Developer ecosystem
        
        3. Strategic Partnerships:
           - Investors and funding partners
           - Joint ventures and alliances
           - Co-marketing agreements
           - Media and PR partnerships
           - Industry associations
           - Academic partnerships
        
        Provide specific partner names, partnership types, and strategic value where available.
        Include partnership duration, exclusivity, and business impact metrics.
        """

    def _analyze_commercial_partnerships(self, data: Dict[str, Any], context: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Extract and analyze commercial partnerships"""
        # AIDEV-NOTE: Structured commercial partnership analysis with revenue impact
        distributors = []
        resellers = []
        suppliers = []

        # Extract distributors
        distributor_data = data.get("distributors", [])
        if isinstance(distributor_data, list):
            for dist in distributor_data:
                if isinstance(dist, dict):
                    distributors.append({
                        "name": dist.get("name", "Unknown Distributor"),
                        "type": "distributor",
                        "regions": dist.get("regions", []),
                        "since": dist.get("since", "unknown"),
                        "exclusive": dist.get("exclusive", False),
                        "performance": dist.get("performance", "good"),
                        "revenue_contribution": dist.get("revenue_contribution", "5-10%")
                    })

        # Extract resellers and channel partners
        reseller_data = data.get("resellers", [])
        if isinstance(reseller_data, list):
            for res in reseller_data:
                if isinstance(res, dict):
                    resellers.append({
                        "name": res.get("name", "Unknown Reseller"),
                        "type": res.get("type", "reseller"),
                        "tier": res.get("tier", "standard"),
                        "certifications": res.get("certifications", []),
                        "specializations": res.get("specializations", []),
                        "annual_sales": res.get("annual_sales", "unknown")
                    })

        # Extract key suppliers and vendors
        supplier_data = data.get("suppliers", [])
        if isinstance(supplier_data, list):
            for sup in supplier_data:
                if isinstance(sup, dict):
                    suppliers.append({
                        "name": sup.get("name", "Unknown Supplier"),
                        "type": sup.get("type", "technology"),
                        "critical": sup.get("critical", False),
                        "dependency_level": sup.get("dependency_level", "medium"),
                        "contract_type": sup.get("contract_type", "annual"),
                        "alternatives": sup.get("alternatives", [])
                    })

        # Calculate partner network metrics
        total_partners = len(distributors) + len(resellers) + len(suppliers)

        # Extract geographic coverage from partners
        geographic_regions = set()
        for dist in distributors:
            geographic_regions.update(dist.get("regions", []))

        # Estimate revenue impact
        revenue_impact = data.get("partner_revenue_impact", "20-30%")
        if not revenue_impact and total_partners > 0:
            # Estimate based on partner count
            if total_partners > 50:
                revenue_impact = "40-50%"
            elif total_partners > 20:
                revenue_impact = "25-35%"
            elif total_partners > 10:
                revenue_impact = "15-25%"
            else:
                revenue_impact = "5-15%"

        return {
            "distributors": distributors,
            "resellers": resellers,
            "suppliers": suppliers,
            "revenue_impact": revenue_impact,
            "partner_network_size": total_partners,
            "geographic_coverage": sorted(list(geographic_regions)),
            "channel_strategy": self._determine_channel_strategy(distributors, resellers),
            "partner_performance": {
                "top_performers": data.get("top_performing_partners", []),
                "growth_rate": data.get("partner_growth_rate", "10-15%"),
                "churn_rate": data.get("partner_churn_rate", "5-10%")
            },
            "partner_programs": {
                "has_partner_portal": data.get("has_partner_portal", total_partners > 10),
                "certification_program": data.get("has_certification", total_partners > 20),
                "incentive_structure": data.get("incentive_type", "revenue-based"),
                "support_level": data.get("partner_support", "standard")
            }
        }

    def _determine_channel_strategy(self, distributors: List[Dict], resellers: List[Dict]) -> str:
        """Determine the channel strategy based on partner mix"""
        if len(distributors) > 5 and len(resellers) > 10:
            return "multi-tier"
        elif len(distributors) > 0 and len(resellers) == 0:
            return "distributor-focused"
        elif len(resellers) > 0 and len(distributors) == 0:
            return "direct-reseller"
        elif len(distributors) == 0 and len(resellers) == 0:
            return "direct-sales"
        else:
            return "hybrid"

    def _analyze_technology_partnerships(self, data: Dict[str, Any], context: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Extract and analyze technology partnerships"""
        # AIDEV-NOTE: Technology partnership analysis focusing on integrations and ecosystem
        integrations = []
        certifications = []
        marketplace_presence = []

        # Extract technology integrations
        integration_data = data.get("integrations", [])
        if isinstance(integration_data, list):
            for integ in integration_data:
                if isinstance(integ, dict):
                    integrations.append({
                        "partner": integ.get("partner", "Unknown Partner"),
                        "type": integ.get("type", "API Integration"),
                        "api_level": integ.get("api_level", "standard"),
                        "users": integ.get("users", 0),
                        "data_sync": integ.get("data_sync", "bidirectional"),
                        "launched": integ.get("launched", "unknown"),
                        "documentation": integ.get("has_documentation", True)
                    })

        # Extract certifications and technology partnerships
        cert_data = data.get("certifications", [])
        if isinstance(cert_data, list):
            certifications = cert_data
        elif isinstance(cert_data, dict):
            # Convert dict entries to list
            for partner, cert_level in cert_data.items():
                certifications.append(f"{partner} - {cert_level}")

        # Common technology certifications if not provided
        if not certifications and data.get("has_certifications", False):
            certifications = self._infer_certifications(context)

        # Extract marketplace presence
        marketplace_data = data.get("marketplace_presence", [])
        if isinstance(marketplace_data, list):
            marketplace_presence = marketplace_data
        elif data.get("in_marketplaces", False):
            # Infer common marketplaces based on industry
            marketplace_presence = self._infer_marketplaces(context)

        # API ecosystem metrics
        api_ecosystem = {
            "public_apis": data.get("public_apis", 0),
            "partner_apis": data.get("partner_apis", 0),
            "developers": data.get("developer_count", 0),
            "api_calls_monthly": data.get("api_calls", 0),
            "documentation_quality": data.get("api_docs_quality", "good"),
            "sandbox_available": data.get("has_sandbox", True),
            "sdk_languages": data.get("sdk_languages", ["Python", "JavaScript", "Java"])
        }

        # Technology stack dependencies
        tech_dependencies = {
            "cloud_providers": data.get("cloud_providers", ["AWS"]),
            "core_technologies": data.get("core_tech", []),
            "integration_platforms": data.get("integration_platforms", []),
            "dependency_risk": self._assess_tech_dependency_risk(data)
        }

        # Developer ecosystem
        developer_ecosystem = {
            "developer_portal": data.get("has_dev_portal", len(integrations) > 5),
            "community_size": data.get("developer_community", 0),
            "github_stars": data.get("github_stars", 0),
            "npm_downloads": data.get("npm_downloads", 0),
            "hackathons": data.get("hackathons_hosted", 0),
            "developer_support": data.get("dev_support_level", "community")
        }

        return {
            "integrations": integrations,
            "certifications": certifications,
            "marketplace_presence": marketplace_presence,
            "api_ecosystem": api_ecosystem,
            "tech_dependencies": tech_dependencies,
            "developer_ecosystem": developer_ecosystem,
            "integration_maturity": self._calculate_integration_maturity(integrations, api_ecosystem),
            "partnership_benefits": {
                "expanded_functionality": len(integrations) > 10,
                "market_reach": len(marketplace_presence) > 3,
                "technical_credibility": len(certifications) > 2,
                "ecosystem_lock_in": api_ecosystem["developers"] > 100
            }
        }

    def _infer_certifications(self, context: Optional[Dict[str, Any]]) -> List[str]:
        """Infer likely certifications based on industry context"""
        industry = context.get(
            "industry", "technology") if context else "technology"

        cert_map = {
            "technology": ["ISO 27001", "SOC 2 Type II"],
            "saas": ["ISO 27001", "SOC 2 Type II", "GDPR Compliant"],
            "finance": ["PCI DSS", "ISO 27001", "SOX Compliant"],
            "healthcare": ["HIPAA", "HITRUST", "ISO 27001"],
            "ecommerce": ["PCI DSS", "ISO 27001"],
            "enterprise": ["ISO 27001", "SOC 2", "ISO 9001"]
        }

        return cert_map.get(industry.lower(), ["ISO 27001"])

    def _infer_marketplaces(self, context: Optional[Dict[str, Any]]) -> List[str]:
        """Infer likely marketplace presence based on context"""
        industry = context.get(
            "industry", "technology") if context else "technology"

        marketplace_map = {
            "saas": ["AWS Marketplace", "Microsoft AppSource", "Google Cloud Marketplace"],
            "ecommerce": ["Shopify App Store", "WooCommerce Marketplace"],
            "crm": ["Salesforce AppExchange", "HubSpot App Marketplace"],
            "productivity": ["Slack App Directory", "Microsoft Teams Apps", "Atlassian Marketplace"],
            "technology": ["AWS Marketplace", "Azure Marketplace"]
        }

        return marketplace_map.get(industry.lower(), ["AWS Marketplace"])

    def _assess_tech_dependency_risk(self, data: Dict[str, Any]) -> str:
        """Assess technology dependency risk level"""
        critical_deps = data.get("critical_dependencies", 0)
        alternatives = data.get("has_alternatives", True)
        vendor_lock_in = data.get("vendor_lock_in", False)

        if critical_deps > 5 or vendor_lock_in:
            return "high"
        elif critical_deps > 2 and not alternatives:
            return "medium"
        else:
            return "low"

    def _calculate_integration_maturity(self, integrations: List[Dict], api_ecosystem: Dict[str, Any]) -> str:
        """Calculate integration maturity level"""
        integration_count = len(integrations)
        api_count = api_ecosystem.get(
            "public_apis", 0) + api_ecosystem.get("partner_apis", 0)
        developers = api_ecosystem.get("developers", 0)

        if integration_count > 20 and api_count > 10 and developers > 500:
            return "advanced"
        elif integration_count > 10 and api_count > 5 and developers > 100:
            return "mature"
        elif integration_count > 5 or api_count > 2:
            return "developing"
        else:
            return "basic"

    def _analyze_strategic_partnerships(self, data: Dict[str, Any], context: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Extract and analyze strategic partnerships"""
        # AIDEV-NOTE: Strategic partnership analysis for investors, JVs, and alliances
        investors = []
        joint_ventures = []
        co_marketing = []
        media_partners = []

        # Extract investor partnerships
        investor_data = data.get("investors", [])
        if isinstance(investor_data, list):
            for inv in investor_data:
                if isinstance(inv, dict):
                    investors.append({
                        "name": inv.get("name", "Unknown Investor"),
                        "type": inv.get("type", "VC"),
                        "round": inv.get("round", "unknown"),
                        "strategic_value": inv.get("strategic_value", "medium"),
                        "board_seat": inv.get("board_seat", False),
                        "investment_amount": inv.get("amount", "undisclosed"),
                        "year": inv.get("year", "unknown")
                    })

        # Extract joint ventures and strategic alliances
        jv_data = data.get("joint_ventures", [])
        if isinstance(jv_data, list):
            for jv in jv_data:
                if isinstance(jv, dict):
                    joint_ventures.append({
                        "partner": jv.get("partner", "Unknown Partner"),
                        "focus": jv.get("focus", "general"),
                        "started": jv.get("started", "unknown"),
                        "structure": jv.get("structure", "partnership"),
                        "revenue_share": jv.get("revenue_share", "confidential"),
                        "geographic_focus": jv.get("regions", []),
                        "success_metrics": jv.get("metrics", [])
                    })

        # Extract co-marketing partnerships
        comarketing_data = data.get("co_marketing", [])
        if isinstance(comarketing_data, list):
            co_marketing = comarketing_data
        elif isinstance(comarketing_data, dict):
            co_marketing = list(comarketing_data.keys())

        # Extract media and PR partnerships
        media_data = data.get("media_partners", [])
        if isinstance(media_data, list):
            media_partners = media_data
        elif isinstance(media_data, dict):
            media_partners = list(media_data.keys())

        # Industry associations and memberships
        associations = {
            "industry_associations": data.get("associations", []),
            "standards_bodies": data.get("standards_bodies", []),
            "consortiums": data.get("consortiums", []),
            "trade_organizations": data.get("trade_orgs", [])
        }

        # Academic and research partnerships
        academic_partnerships = {
            "universities": data.get("university_partners", []),
            "research_programs": data.get("research_programs", []),
            "internship_programs": data.get("internships", False),
            "sponsored_research": data.get("sponsored_research", []),
            "talent_pipeline": data.get("talent_pipeline", False)
        }

        # Government and institutional partnerships
        institutional = {
            "government_contracts": data.get("gov_contracts", []),
            "public_sector_partners": data.get("public_partners", []),
            "ngo_partnerships": data.get("ngo_partners", []),
            "social_impact": data.get("social_impact_programs", [])
        }

        # Strategic partnership metrics
        partnership_impact = {
            "market_expansion": self._assess_market_expansion(joint_ventures, co_marketing),
            "brand_leverage": len(co_marketing) > 3 or len(media_partners) > 2,
            "innovation_acceleration": len(academic_partnerships.get("research_programs", [])) > 0,
            "risk_mitigation": len(investors) > 2 and len(joint_ventures) > 0,
            "competitive_advantage": self._assess_competitive_advantage(data)
        }

        return {
            "investors": investors,
            "joint_ventures": joint_ventures,
            "co_marketing": co_marketing,
            "media_partners": media_partners,
            "associations": associations,
            "academic_partnerships": academic_partnerships,
            "institutional": institutional,
            "partnership_impact": partnership_impact,
            "strategic_focus": self._determine_strategic_focus(investors, joint_ventures, associations),
            "partnership_stage": self._assess_partnership_stage(investors, joint_ventures)
        }

    def _assess_market_expansion(self, joint_ventures: List[Dict], co_marketing: List[str]) -> str:
        """Assess market expansion impact from partnerships"""
        geographic_reach = set()
        for jv in joint_ventures:
            geographic_reach.update(jv.get("geographic_focus", []))

        if len(geographic_reach) > 5 or len(co_marketing) > 5:
            return "high"
        elif len(geographic_reach) > 2 or len(co_marketing) > 2:
            return "medium"
        else:
            return "low"

    def _assess_competitive_advantage(self, data: Dict[str, Any]) -> str:
        """Assess competitive advantage from strategic partnerships"""
        exclusive_partnerships = data.get("exclusive_partnerships", 0)
        strategic_investors = sum(1 for inv in data.get("investors", [])
                                  if isinstance(inv, dict) and inv.get("strategic_value") == "high")
        unique_partnerships = data.get("unique_partnerships", 0)

        if exclusive_partnerships > 3 or strategic_investors > 2 or unique_partnerships > 5:
            return "strong"
        elif exclusive_partnerships > 1 or strategic_investors > 0 or unique_partnerships > 2:
            return "moderate"
        else:
            return "weak"

    def _determine_strategic_focus(self, investors: List[Dict], joint_ventures: List[Dict],
                                   associations: Dict[str, Any]) -> str:
        """Determine primary strategic focus of partnerships"""
        if len(investors) > len(joint_ventures) * 2:
            return "growth-focused"
        elif len(joint_ventures) > len(investors):
            return "expansion-focused"
        elif sum(len(v) for v in associations.values()) > 5:
            return "ecosystem-focused"
        else:
            return "balanced"

    def _assess_partnership_stage(self, investors: List[Dict], joint_ventures: List[Dict]) -> str:
        """Assess the maturity stage of strategic partnerships"""
        total_strategic = len(investors) + len(joint_ventures)

        # Check for late-stage indicators
        late_stage_investors = sum(1 for inv in investors
                                   if inv.get("round", "").lower() in ["series c", "series d", "series e", "ipo"])

        if late_stage_investors > 0 or total_strategic > 10:
            return "mature"
        elif total_strategic > 5:
            return "growth"
        elif total_strategic > 0:
            return "early"
        else:
            return "nascent"

    def _calculate_partnership_ecosystem_score(
        self,
        commercial: Dict[str, Any],
        technology: Dict[str, Any],
        strategic: Dict[str, Any]
    ) -> float:
        """Calculate overall partnership ecosystem maturity score (0-100)"""
        # AIDEV-NOTE: Weighted scoring system - Commercial 35%, Technology 30%, Strategic 35%

        # Commercial partnership score (0-100)
        commercial_score = self._score_commercial_partnerships(commercial)

        # Technology partnership score (0-100)
        technology_score = self._score_technology_partnerships(technology)

        # Strategic partnership score (0-100)
        strategic_score = self._score_strategic_partnerships(strategic)

        # Apply weights: Commercial 35%, Technology 30%, Strategic 35%
        weighted_score = (
            commercial_score * 0.35 +
            technology_score * 0.30 +
            strategic_score * 0.35
        )

        return round(weighted_score, 1)

    def _score_commercial_partnerships(self, commercial: Dict[str, Any]) -> float:
        """Score commercial partnership maturity (0-100)"""
        score = 0.0

        # Partner network size (25 points)
        partner_count = commercial.get("partner_network_size", 0)
        if partner_count >= 50:
            score += 25
        elif partner_count >= 20:
            score += 20
        elif partner_count >= 10:
            score += 15
        elif partner_count >= 5:
            score += 10
        elif partner_count > 0:
            score += 5

        # Geographic coverage (20 points)
        coverage = commercial.get("geographic_coverage", [])
        if len(coverage) >= 10:
            score += 20
        elif len(coverage) >= 5:
            score += 15
        elif len(coverage) >= 3:
            score += 10
        elif len(coverage) > 0:
            score += 5

        # Revenue impact (20 points)
        revenue_impact = commercial.get("revenue_impact", "0%")
        if "40" in revenue_impact or "50" in revenue_impact:
            score += 20
        elif "25" in revenue_impact or "30" in revenue_impact or "35" in revenue_impact:
            score += 15
        elif "15" in revenue_impact or "20" in revenue_impact:
            score += 10
        elif "5" in revenue_impact or "10" in revenue_impact:
            score += 5

        # Channel strategy (15 points)
        strategy = commercial.get("channel_strategy", "direct-sales")
        if strategy == "multi-tier":
            score += 15
        elif strategy == "hybrid":
            score += 10
        elif strategy in ["distributor-focused", "direct-reseller"]:
            score += 5

        # Partner programs (20 points)
        programs = commercial.get("partner_programs", {})
        if programs.get("has_partner_portal", False):
            score += 5
        if programs.get("certification_program", False):
            score += 10
        if programs.get("support_level") in ["premium", "dedicated"]:
            score += 5

        return min(100, score)

    def _score_technology_partnerships(self, technology: Dict[str, Any]) -> float:
        """Score technology partnership maturity (0-100)"""
        score = 0.0

        # Integration count and maturity (30 points)
        integrations = technology.get("integrations", [])
        maturity = technology.get("integration_maturity", "basic")

        if len(integrations) >= 20:
            score += 20
        elif len(integrations) >= 10:
            score += 15
        elif len(integrations) >= 5:
            score += 10
        elif len(integrations) > 0:
            score += 5

        if maturity == "advanced":
            score += 10
        elif maturity == "mature":
            score += 7
        elif maturity == "developing":
            score += 4

        # API ecosystem (25 points)
        api_eco = technology.get("api_ecosystem", {})
        total_apis = api_eco.get("public_apis", 0) + \
            api_eco.get("partner_apis", 0)
        developers = api_eco.get("developers", 0)

        if total_apis >= 15:
            score += 10
        elif total_apis >= 10:
            score += 7
        elif total_apis >= 5:
            score += 5
        elif total_apis > 0:
            score += 3

        if developers >= 1000:
            score += 15
        elif developers >= 500:
            score += 10
        elif developers >= 100:
            score += 7
        elif developers >= 50:
            score += 5
        elif developers > 0:
            score += 3

        # Certifications and marketplace (25 points)
        certs = technology.get("certifications", [])
        marketplaces = technology.get("marketplace_presence", [])

        if len(certs) >= 5:
            score += 12
        elif len(certs) >= 3:
            score += 8
        elif len(certs) >= 1:
            score += 5

        if len(marketplaces) >= 5:
            score += 13
        elif len(marketplaces) >= 3:
            score += 10
        elif len(marketplaces) >= 1:
            score += 5

        # Developer ecosystem (20 points)
        dev_eco = technology.get("developer_ecosystem", {})
        if dev_eco.get("developer_portal", False):
            score += 8
        if dev_eco.get("community_size", 0) > 100:
            score += 6
        if dev_eco.get("hackathons", 0) > 0:
            score += 6

        return min(100, score)

    def _score_strategic_partnerships(self, strategic: Dict[str, Any]) -> float:
        """Score strategic partnership maturity (0-100)"""
        score = 0.0

        # Investor quality and quantity (30 points)
        investors = strategic.get("investors", [])
        high_value_investors = sum(1 for inv in investors
                                   if inv.get("strategic_value") == "high")

        if len(investors) >= 5:
            score += 15
        elif len(investors) >= 3:
            score += 10
        elif len(investors) >= 1:
            score += 5

        if high_value_investors >= 2:
            score += 15
        elif high_value_investors == 1:
            score += 10

        # Joint ventures and alliances (25 points)
        jvs = strategic.get("joint_ventures", [])
        if len(jvs) >= 5:
            score += 25
        elif len(jvs) >= 3:
            score += 20
        elif len(jvs) >= 1:
            score += 10

        # Marketing and media partnerships (20 points)
        co_marketing = strategic.get("co_marketing", [])
        media = strategic.get("media_partners", [])

        if len(co_marketing) >= 5:
            score += 10
        elif len(co_marketing) >= 2:
            score += 7
        elif len(co_marketing) > 0:
            score += 4

        if len(media) >= 3:
            score += 10
        elif len(media) >= 1:
            score += 5

        # Partnership impact (25 points)
        impact = strategic.get("partnership_impact", {})
        if impact.get("market_expansion") == "high":
            score += 8
        elif impact.get("market_expansion") == "medium":
            score += 5

        if impact.get("competitive_advantage") == "strong":
            score += 9
        elif impact.get("competitive_advantage") == "moderate":
            score += 5

        if impact.get("brand_leverage", False):
            score += 4
        if impact.get("innovation_acceleration", False):
            score += 4

        return min(100, score)

    def _generate_recommendations(
        self,
        commercial: Dict[str, Any],
        technology: Dict[str, Any],
        strategic: Dict[str, Any]
    ) -> List[str]:
        """Generate actionable recommendations based on partnership analysis"""
        recommendations = []

        # Commercial partnership recommendations
        partner_count = commercial.get("partner_network_size", 0)
        geographic_coverage = commercial.get("geographic_coverage", [])
        channel_strategy = commercial.get("channel_strategy", "direct-sales")

        if partner_count < 10:
            recommendations.append(
                "Expandir rede de parceiros comerciais para aumentar alcance de mercado - "
                "meta: 20+ parceiros nos próximos 12 meses"
            )

        if len(geographic_coverage) < 3:
            recommendations.append(
                "Desenvolver parcerias regionais para expansão geográfica - "
                "focar em mercados de alto crescimento na América Latina"
            )

        if channel_strategy == "direct-sales" and partner_count < 5:
            recommendations.append(
                "Implementar estratégia de canal indireto com distribuidores e revendedores "
                "para escalar vendas sem aumentar equipe comercial"
            )

        # Technology partnership recommendations
        integrations = technology.get("integrations", [])
        certifications = technology.get("certifications", [])
        api_ecosystem = technology.get("api_ecosystem", {})
        developers = api_ecosystem.get("developers", 0)

        if len(integrations) < 5:
            recommendations.append(
                "Priorizar integrações com plataformas líderes de mercado (Salesforce, "
                "Microsoft 365, Google Workspace) para aumentar valor do produto"
            )

        if len(certifications) < 2:
            recommendations.append(
                "Buscar certificações técnicas com grandes players (AWS, Microsoft, Google) "
                "para aumentar credibilidade e acesso a programas de parceiros"
            )

        if developers < 100 and api_ecosystem.get("public_apis", 0) > 0:
            recommendations.append(
                "Investir em programa de desenvolvedores com documentação aprimorada, "
                "SDKs e hackathons para criar ecossistema de inovação"
            )

        # Strategic partnership recommendations
        investors = strategic.get("investors", [])
        joint_ventures = strategic.get("joint_ventures", [])
        partnership_stage = strategic.get("partnership_stage", "nascent")

        if len(investors) == 0 and partnership_stage in ["nascent", "early"]:
            recommendations.append(
                "Buscar investidor estratégico do setor para além de capital - "
                "acesso a clientes, expertise e credibilidade de mercado"
            )

        if len(joint_ventures) == 0:
            recommendations.append(
                "Explorar joint ventures com empresas complementares para "
                "acelerar entrada em novos mercados ou segmentos"
            )

        # Limit to top 5 most relevant recommendations
        return recommendations[:5]

    def _calculate_confidence_score(
        self,
        commercial: Dict[str, Any],
        technology: Dict[str, Any],
        strategic: Dict[str, Any]
    ) -> float:
        """Calculate confidence score based on data completeness (0-100)"""
        # AIDEV-NOTE: Confidence based on data completeness and quality
        scores = []

        # Commercial data completeness (33.3%)
        commercial_fields = 0
        if commercial.get("distributors"):
            commercial_fields += 1
        if commercial.get("resellers"):
            commercial_fields += 1
        if commercial.get("suppliers"):
            commercial_fields += 1
        if commercial.get("revenue_impact"):
            commercial_fields += 1
        if commercial.get("geographic_coverage"):
            commercial_fields += 1
        if commercial.get("partner_performance"):
            commercial_fields += 1
        commercial_completeness = (commercial_fields / 6) * 100
        scores.append(commercial_completeness)

        # Technology data completeness (33.3%)
        tech_fields = 0
        if technology.get("integrations"):
            tech_fields += 1
        if technology.get("certifications"):
            tech_fields += 1
        if technology.get("marketplace_presence"):
            tech_fields += 1
        if technology.get("api_ecosystem", {}).get("public_apis", 0) > 0:
            tech_fields += 1
        if technology.get("developer_ecosystem", {}).get("developers", 0) > 0:
            tech_fields += 1
        if technology.get("tech_dependencies"):
            tech_fields += 1
        tech_completeness = (tech_fields / 6) * 100
        scores.append(tech_completeness)

        # Strategic data completeness (33.3%)
        strategic_fields = 0
        if strategic.get("investors"):
            strategic_fields += 1
        if strategic.get("joint_ventures"):
            strategic_fields += 1
        if strategic.get("co_marketing"):
            strategic_fields += 1
        if strategic.get("media_partners"):
            strategic_fields += 1
        if strategic.get("associations"):
            strategic_fields += 1
        if strategic.get("academic_partnerships"):
            strategic_fields += 1
        strategic_completeness = (strategic_fields / 6) * 100
        scores.append(strategic_completeness)

        # Calculate average confidence
        confidence = sum(scores) / len(scores)

        # Apply quality adjustments
        # High partner count increases confidence
        if commercial.get("partner_network_size", 0) > 20:
            confidence = min(100, confidence + 5)

        # Multiple integration types increase confidence
        if len(technology.get("integrations", [])) > 10:
            confidence = min(100, confidence + 5)

        # Strategic investors increase confidence
        if any(inv.get("strategic_value") == "high" for inv in strategic.get("investors", [])):
            confidence = min(100, confidence + 5)

        return round(confidence, 1)

    def _get_mock_data(self, company_name: str) -> Dict[str, Any]:
        """Get mock data for testing and development"""
        # AIDEV-NOTE: Rich mock data based on real B2B partnership examples

        # Different mock scenarios based on company name
        if "startup" in company_name.lower():
            return self._get_startup_mock_data()
        elif "enterprise" in company_name.lower():
            return self._get_enterprise_mock_data()
        else:
            return self._get_default_mock_data()

    def _get_default_mock_data(self) -> Dict[str, Any]:
        """Default mock data for medium-sized B2B SaaS company"""
        return {
            # Commercial partnerships
            "distributors": [
                {
                    "name": "TechDistributor LATAM",
                    "regions": ["Brazil", "Argentina", "Chile", "Mexico"],
                    "since": "2022",
                    "exclusive": False,
                    "performance": "excellent",
                    "revenue_contribution": "15-20%"
                },
                {
                    "name": "CloudPartners Europe",
                    "regions": ["Germany", "France", "UK"],
                    "since": "2023",
                    "exclusive": True,
                    "performance": "good",
                    "revenue_contribution": "10-15%"
                }
            ],
            "resellers": [
                {
                    "name": "Digital Solutions Inc",
                    "type": "VAR",
                    "tier": "Gold",
                    "certifications": ["Sales Pro", "Technical Expert"],
                    "specializations": ["Financial Services", "Healthcare"],
                    "annual_sales": "$2.5M"
                },
                {
                    "name": "Cloud Integrators",
                    "type": "MSP",
                    "tier": "Silver",
                    "certifications": ["Cloud Specialist"],
                    "annual_sales": "$1.8M"
                }
            ],
            "suppliers": [
                {
                    "name": "Amazon Web Services",
                    "type": "infrastructure",
                    "critical": True,
                    "dependency_level": "high",
                    "contract_type": "enterprise agreement",
                    "alternatives": ["Google Cloud", "Azure"]
                },
                {
                    "name": "Stripe",
                    "type": "payment processing",
                    "critical": True,
                    "dependency_level": "medium",
                    "alternatives": ["PayPal", "Square"]
                }
            ],
            "partner_revenue_impact": "25-30%",
            "top_performing_partners": ["TechDistributor LATAM", "Digital Solutions Inc"],
            "partner_growth_rate": "15-20%",
            "partner_churn_rate": "5-8%",
            "has_partner_portal": True,
            "has_certification": True,
            "incentive_type": "revenue-based",
            "partner_support": "dedicated",

            # Technology partnerships
            "integrations": [
                {
                    "partner": "Salesforce",
                    "type": "CRM Integration",
                    "api_level": "deep",
                    "users": 8500,
                    "data_sync": "bidirectional",
                    "launched": "2021",
                    "has_documentation": True
                },
                {
                    "partner": "Slack",
                    "type": "Communication Platform",
                    "api_level": "standard",
                    "users": 5200,
                    "launched": "2022"
                },
                {
                    "partner": "Microsoft Teams",
                    "type": "Communication Platform",
                    "api_level": "standard",
                    "users": 6100,
                    "launched": "2022"
                },
                {
                    "partner": "HubSpot",
                    "type": "Marketing Automation",
                    "api_level": "deep",
                    "users": 3200,
                    "launched": "2023"
                },
                {
                    "partner": "Jira",
                    "type": "Project Management",
                    "api_level": "standard",
                    "users": 4100,
                    "launched": "2021"
                },
                {
                    "partner": "Google Workspace",
                    "type": "Productivity Suite",
                    "api_level": "standard",
                    "users": 7300,
                    "launched": "2020"
                }
            ],
            "certifications": [
                "AWS Advanced Technology Partner",
                "Microsoft Gold Partner",
                "SOC 2 Type II",
                "ISO 27001"
            ],
            "marketplace_presence": [
                "AWS Marketplace",
                "Microsoft AppSource",
                "Salesforce AppExchange"
            ],
            "public_apis": 8,
            "partner_apis": 15,
            "developer_count": 350,
            "api_calls": 2500000,
            "api_docs_quality": "excellent",
            "has_sandbox": True,
            "sdk_languages": ["Python", "JavaScript", "Java", "Ruby", "Go"],
            "cloud_providers": ["AWS", "Azure"],
            "core_tech": ["Python", "React", "PostgreSQL", "Redis"],
            "integration_platforms": ["Zapier", "Make"],
            "has_dev_portal": True,
            "developer_community": 350,
            "github_stars": 1250,
            "npm_downloads": 45000,
            "hackathons_hosted": 2,
            "dev_support_level": "dedicated",

            # Strategic partnerships
            "investors": [
                {
                    "name": "Sequoia Capital",
                    "type": "VC",
                    "round": "Series B",
                    "strategic_value": "high",
                    "board_seat": True,
                    "amount": "$25M",
                    "year": "2023"
                },
                {
                    "name": "Andreessen Horowitz",
                    "type": "VC",
                    "round": "Series A",
                    "strategic_value": "high",
                    "board_seat": False,
                    "amount": "$12M",
                    "year": "2022"
                }
            ],
            "joint_ventures": [
                {
                    "partner": "TechCorp Solutions",
                    "focus": "Enterprise Security Suite",
                    "started": "2023",
                    "structure": "50/50 JV",
                    "regions": ["North America", "Europe"]
                }
            ],
            "co_marketing": [
                "Microsoft",
                "AWS",
                "Salesforce",
                "Google Cloud"
            ],
            "media_partners": [
                "TechCrunch",
                "VentureBeat",
                "InfoWorld"
            ],
            "associations": ["Cloud Security Alliance", "SaaS Industry Association"],
            "standards_bodies": ["ISO", "SOC"],
            "university_partners": ["MIT", "Stanford"],
            "research_programs": ["AI for Business"],
            "internships": True,
            "exclusive_partnerships": 2,
            "unique_partnerships": 4
        }

    def _get_startup_mock_data(self) -> Dict[str, Any]:
        """Mock data for early-stage startup"""
        return {
            "distributors": [],
            "resellers": [
                {
                    "name": "StartupPartner Co",
                    "type": "reseller",
                    "tier": "bronze",
                    "certifications": [],
                    "annual_sales": "$250K"
                }
            ],
            "suppliers": [
                {
                    "name": "AWS",
                    "type": "infrastructure",
                    "critical": True,
                    "dependency_level": "high",
                    "contract_type": "startup credits"
                }
            ],
            "partner_revenue_impact": "5-10%",
            "integrations": [
                {
                    "partner": "Stripe",
                    "type": "Payment Gateway",
                    "api_level": "standard",
                    "users": 150
                }
            ],
            "certifications": ["AWS Startup Partner"],
            "marketplace_presence": [],
            "public_apis": 2,
            "partner_apis": 0,
            "developer_count": 10,
            "investors": [
                {
                    "name": "Y Combinator",
                    "type": "Accelerator",
                    "round": "Pre-seed",
                    "strategic_value": "medium",
                    "amount": "$500K",
                    "year": "2024"
                }
            ],
            "joint_ventures": [],
            "co_marketing": [],
            "media_partners": ["Product Hunt"],
            "associations": []
        }

    def _get_enterprise_mock_data(self) -> Dict[str, Any]:
        """Mock data for large enterprise"""
        return {
            "distributors": [
                {
                    "name": "Global Tech Distribution",
                    "regions": ["Americas", "EMEA", "APAC"],
                    "since": "2015",
                    "exclusive": False,
                    "performance": "excellent",
                    "revenue_contribution": "30-35%"
                }
            ],
            "resellers": [
                {
                    "name": "Accenture",
                    "type": "GSI",
                    "tier": "Strategic",
                    "certifications": ["Master Partner", "Co-Innovation"],
                    "annual_sales": "$50M+"
                },
                {
                    "name": "Deloitte",
                    "type": "GSI",
                    "tier": "Strategic",
                    "certifications": ["Implementation Expert"],
                    "annual_sales": "$45M+"
                },
                {
                    "name": "IBM Global Services",
                    "type": "GSI",
                    "tier": "Strategic",
                    "annual_sales": "$38M+"
                }
            ],
            "suppliers": [
                {
                    "name": "Microsoft Azure",
                    "type": "infrastructure",
                    "critical": True,
                    "dependency_level": "high",
                    "contract_type": "enterprise agreement"
                },
                {
                    "name": "Oracle",
                    "type": "database",
                    "critical": True,
                    "dependency_level": "high"
                }
            ],
            "partner_revenue_impact": "40-50%",
            "integrations": [
                {
                    "partner": "SAP",
                    "type": "ERP Integration",
                    "api_level": "deep",
                    "users": 50000
                },
                {
                    "partner": "Oracle",
                    "type": "Database Integration",
                    "api_level": "deep",
                    "users": 45000
                },
                {
                    "partner": "ServiceNow",
                    "type": "ITSM Integration",
                    "api_level": "deep",
                    "users": 35000
                }
            ],
            "certifications": [
                "ISO 27001",
                "SOC 2 Type II",
                "FedRAMP",
                "HIPAA",
                "PCI DSS Level 1",
                "Microsoft Co-Sell Ready"
            ],
            "marketplace_presence": [
                "AWS Marketplace",
                "Azure Marketplace",
                "Google Cloud Marketplace",
                "Red Hat Marketplace",
                "IBM Cloud Catalog"
            ],
            "public_apis": 25,
            "partner_apis": 50,
            "developer_count": 2500,
            "investors": [
                {
                    "name": "TPG Capital",
                    "type": "PE",
                    "round": "Acquisition",
                    "strategic_value": "high",
                    "board_seat": True,
                    "amount": "$2.5B",
                    "year": "2021"
                }
            ],
            "joint_ventures": [
                {
                    "partner": "Microsoft",
                    "focus": "Cloud Migration Services",
                    "started": "2020"
                },
                {
                    "partner": "AWS",
                    "focus": "Industry Cloud Solutions",
                    "started": "2021"
                },
                {
                    "partner": "Google",
                    "focus": "AI/ML Platform",
                    "started": "2022"
                }
            ],
            "co_marketing": [
                "Microsoft",
                "AWS",
                "Google",
                "Oracle",
                "SAP",
                "IBM",
                "Salesforce"
            ],
            "media_partners": [
                "Forbes",
                "Wall Street Journal",
                "Financial Times",
                "Bloomberg",
                "CNBC"
            ],
            "associations": [
                "Business Software Alliance",
                "Cloud Security Alliance",
                "Open Source Initiative",
                "Linux Foundation"
            ],
            "university_partners": [
                "MIT",
                "Stanford",
                "Carnegie Mellon",
                "Harvard Business School"
            ]
        }


# Exemplo de uso
if __name__ == "__main__":
    import asyncio

    async def main():
        # Criar instância do serviço
        service = PartnershipService()

        # Criar requisição de teste
        from app.core.interfaces import ResearchRequest

        request = ResearchRequest(
            client_id="test-client-123",
            company_name="Empresa Teste",
            company_url="https://empresa-teste.com.br",
            additional_context={
                "industry": "SaaS",
                "size": "medium"
            }
        )

        # Executar análise
        result = await service.execute(request)

        # Exibir resultado
        if result.success:
            print(f"✓ Análise concluída com sucesso!")
            if result.data and 'partnership_ecosystem_score' in result.data:
                print(
                    f"  Score do ecossistema: {result.data['partnership_ecosystem_score']:.1f}/100")
            if result.confidence_score is not None:
                print(f"  Confiança: {result.confidence_score * 100:.1f}%")
            print(f"  Custo: ${result.cost}")
            print(f"  Tempo: {result.processing_time_seconds:.1f}s")
        else:
            print(f"✗ Erro na análise: {result.error}")

    asyncio.run(main())
