"""
Funding History Service - Serviço para análise de histórico de captação de recursos.

Este serviço analisa o histórico de captação de recursos de empresas incluindo:
- Rounds de investimento e valores
- Perfil e qualidade dos investidores  
- Crescimento de valuation
- Velocidade e momentum de funding
- Comparações com mercado
- Métricas de saúde financeira

Custo: $0.008 por execução
"""
import json
import logging
from typing import Dict, Any, List, Optional
from decimal import Decimal
from datetime import datetime, UTC

from app.core import (
    IResearchService,
    ResearchRequest,
    ResearchResult,
    ResearchProviderError,
    Timer
)

logger = logging.getLogger(__name__)


class FundingHistoryService(IResearchService):
    """
    Serviço para análise de histórico de funding de empresas.

    Utiliza Perplexity API para coletar informações sobre rounds de investimento,
    investidores, valuations e análise de momentum de captação.
    """

    def __init__(self, perplexity_provider):
        """
        Args:
            perplexity_provider: Instância do provider Perplexity
        """
        self.provider = perplexity_provider
        self._version = "1.0.0"
        self._cost = Decimal("0.008")

    def get_name(self) -> str:
        return "funding_history"

    def get_version(self) -> str:
        return self._version

    def get_description(self) -> str:
        return (
            "Analisa histórico de captação de recursos incluindo rounds de investimento, "
            "investidores, valuations, métricas de crescimento e comparações com mercado."
        )

    def get_cost(self) -> Decimal:
        return self._cost

    def get_required_fields(self) -> List[str]:
        return ["company_name", "company_url"]

    def get_optional_fields(self) -> List[str]:
        return ["city", "state", "country", "include_competitor_comparison", "include_investor_analysis"]

    async def validate_request(self, request: ResearchRequest) -> bool:
        """Valida se a requisição tem os campos necessários."""
        # Verifica campos obrigatórios
        if not request.company_name or not request.company_url:
            return False

        # Nome da empresa deve ter pelo menos 2 caracteres
        if len(request.company_name.strip()) < 2:
            return False

        return True

    async def execute(self, request: ResearchRequest) -> ResearchResult:
        """
        Executa a análise de histórico de funding.

        Args:
            request: Requisição com dados da empresa

        Returns:
            ResearchResult com análise de funding
        """
        with Timer() as timer:
            try:
                # Preparar dados
                empresa = request.company_name
                site = request.company_url

                # Extrair opções do contexto adicional
                include_competitors = True
                include_investors = True
                target_currency = "both"

                if request.additional_context:
                    include_competitors = request.additional_context.get(
                        "include_competitor_comparison", True)
                    include_investors = request.additional_context.get(
                        "include_investor_analysis", True)
                    target_currency = request.additional_context.get(
                        "target_currency", "both")

                # Construir prompt do sistema
                system_content = self._build_system_prompt()

                # Construir prompt do usuário
                user_content = self._build_user_prompt(
                    empresa=empresa,
                    site=site,
                    include_competitors=include_competitors,
                    include_investors=include_investors,
                    target_currency=target_currency
                )

                # Fazer requisição ao provider
                logger.info(f"Analisando histórico de funding para {empresa}")
                result_data = await self.provider.search(
                    system_prompt=system_content,
                    user_prompt=user_content,
                    temperature=0.1  # Baixa temperatura para dados factuais
                )

                # Processar resultado
                processed_data = self._process_result(result_data)

                # Calcular score de confiança baseado na completude dos dados
                confidence_score = self._calculate_confidence_score(
                    processed_data)

                return ResearchResult(
                    service_name=self.get_name(),
                    service_version=self.get_version(),
                    timestamp=datetime.now(UTC),
                    cost=self._cost,
                    success=True,
                    data=processed_data,
                    processing_time_seconds=timer.elapsed,
                    confidence_score=confidence_score
                )

            except Exception as e:
                logger.error(
                    f"Erro ao analisar histórico de funding: {str(e)}")
                return ResearchResult(
                    service_name=self.get_name(),
                    service_version=self.get_version(),
                    timestamp=datetime.now(UTC),
                    cost=Decimal("0"),  # Não cobra em caso de erro
                    success=False,
                    error=str(e),
                    processing_time_seconds=timer.elapsed
                )

    def get_sample_output(self) -> Dict[str, Any]:
        """Retorna exemplo de saída do serviço."""
        return {
            "funding_rounds": [
                {
                    "round_type": "seed",
                    "amount_usd": 1000000,
                    "amount_brl": 5000000,
                    "date": "2023-06",
                    "lead_investors": ["Investor A"],
                    "all_investors": ["Investor A", "Investor B"],
                    "valuation_usd": 10000000,
                    "valuation_brl": 50000000
                },
                {
                    "round_type": "series_a",
                    "amount_usd": 5000000,
                    "amount_brl": 25000000,
                    "date": "2024-01",
                    "lead_investors": ["Investor C"],
                    "all_investors": ["Investor C", "Investor A", "Investor D"],
                    "valuation_usd": 30000000,
                    "valuation_brl": *********
                }
            ],
            "key_investors": [
                {
                    "name": "Investor A",
                    "tier": "tier_1",
                    "portfolio_companies": ["Company X", "Company Y"],
                    "notable_exits": ["Exit 1"],
                    "focus_areas": ["SaaS", "Fintech"],
                    "geographic_focus": ["Brazil", "Latin America"]
                }
            ],
            "metrics": {
                "total_raised_usd": 6000000,
                "total_raised_brl": 30000000,
                "number_of_rounds": 2,
                "average_round_size_usd": 3000000,
                "average_time_between_rounds_months": 7,
                "valuation_growth_rate": 3.0,
                "last_round_date": "2024-01",
                "months_since_last_round": 11,
                "total_investors": 4,
                "tier_1_investors": 1,
                "repeat_investors": ["Investor A"],
                "international_investors": 1,
                "funding_vs_competitors": "Above average",
                "funding_velocity": "accelerating",
                "estimated_runway_months": 18,
                "burn_rate_estimate": "R$ 500k/month",
                "funding_efficiency_score": 7.5
            },
            "analysis": {
                "strengths": [
                    "Strong tier-1 investor backing",
                    "Consistent funding momentum"
                ],
                "concerns": [
                    "Long time since last round",
                    "High burn rate"
                ],
                "investor_quality_assessment": "High quality mix of tier-1 VCs and strategic investors",
                "funding_momentum": "Strong momentum with accelerating round sizes",
                "next_round_prediction": "Series B likely in Q2 2025, targeting $15-20M",
                "strategic_recommendations": [
                    "Focus on revenue growth to justify Series B valuation",
                    "Consider strategic investors for next round"
                ]
            },
            "market_context": {
                "market_funding_trend": "Growing",
                "average_round_sizes": {"series_a": 5000000},
                "competitor_funding": {"Competitor A": 10000000}
            },
            "data_sources": ["Public records", "News articles", "Company website"],
            "data_gaps": ["Exact valuation for seed round", "Full investor list for Series A"]
        }

    def _build_system_prompt(self) -> str:
        """Constrói o prompt do sistema para o provider."""
        return (
            "Você é um analista especializado em venture capital e investimentos em startups. "
            "Sua missão é analisar detalhadamente o histórico de captação de recursos de empresas, "
            "sempre respondendo APENAS em JSON válido, sem comentários ou texto fora do objeto JSON. "
            "Forneça análise factual e baseada em dados sobre rounds de investimento, investidores, "
            "valuations e métricas de crescimento. "
            "Se algum dado não for encontrado, indique em 'data_gaps'. "
            "O texto deve estar em português do Brasil, tom profissional, pronto para uso em relatório de investimento."
        )

    def _build_user_prompt(
        self,
        empresa: str,
        site: str,
        include_competitors: bool = True,
        include_investors: bool = True,
        target_currency: str = "both"
    ) -> str:
        """Constrói o prompt do usuário com os dados da empresa."""
        # AIDEV-NOTE: Prompt estruturado para análise detalhada de funding com foco em dados factuais

        currency_instruction = ""
        if target_currency == "both":
            currency_instruction = "Forneça valores em USD e BRL quando possível."
        elif target_currency == "usd":
            currency_instruction = "Forneça todos os valores em USD."
        else:
            currency_instruction = "Forneça todos os valores em BRL."

        investor_section = ""
        if include_investors:
            investor_section = """
  "key_investors": [
    {
      "name": "Nome do Investidor",
      "tier": "tier_1|tier_2|tier_3|corporate|government|crowdfunding|unknown",
      "portfolio_companies": ["Empresa 1", "Empresa 2"],
      "notable_exits": ["Exit 1"],
      "focus_areas": ["SaaS", "Fintech"],
      "geographic_focus": ["Brazil", "Latin America"]
    }
  ],"""

        competitor_section = ""
        if include_competitors:
            competitor_section = """
    "funding_vs_competitors": "Above average|Average|Below average",
    "market_context": {
      "market_funding_trend": "Growing|Stable|Declining",
      "average_round_sizes": {"series_a": 5000000},
      "competitor_funding": {"Concorrente A": 10000000}
    },"""

        return f"""Analise o histórico de captação de recursos da empresa {empresa}, site {site}.

{currency_instruction}

Forneça uma análise completa seguindo EXATAMENTE esta estrutura JSON:
{{
  "funding_rounds": [
    {{
      "round_type": "pre_seed|seed|series_a|series_b|series_c|series_d|series_e_plus|bridge|debt|grant|ico|ipo|acquisition|other",
      "amount_usd": 1000000,
      "amount_brl": 5000000,
      "date": "YYYY-MM",
      "lead_investors": ["Lead Investor Name"],
      "all_investors": ["Investor 1", "Investor 2"],
      "valuation_usd": 10000000,
      "valuation_brl": 50000000
    }}
  ],{investor_section}
  "metrics": {{
    "total_raised_usd": 6000000,
    "total_raised_brl": 30000000,
    "number_of_rounds": 2,
    "average_round_size_usd": 3000000,
    "average_time_between_rounds_months": 12,
    "valuation_growth_rate": 3.0,
    "last_round_date": "YYYY-MM",
    "months_since_last_round": 11,
    "total_investors": 8,
    "tier_1_investors": 2,
    "repeat_investors": ["Investor A"],
    "international_investors": 3,{competitor_section}
    "funding_velocity": "accelerating|steady|slowing",
    "estimated_runway_months": 18,
    "burn_rate_estimate": "R$ 500k/month",
    "funding_efficiency_score": 7.5
  }},
  "analysis": {{
    "strengths": [
      "Ponto forte 1",
      "Ponto forte 2"
    ],
    "concerns": [
      "Preocupação 1",
      "Preocupação 2"
    ],
    "investor_quality_assessment": "Avaliação detalhada da qualidade dos investidores",
    "funding_momentum": "Análise do momentum de captação",
    "next_round_prediction": "Previsão para próximo round",
    "strategic_recommendations": [
      "Recomendação 1",
      "Recomendação 2"
    ]
  }},
  "data_sources": ["Fonte 1", "Fonte 2"],
  "data_gaps": ["Dado não encontrado 1", "Dado não encontrado 2"]
}}

IMPORTANTE:
1. Liste TODOS os rounds de investimento conhecidos em ordem cronológica
2. Forneça valores precisos quando disponíveis
3. Identifique investidores líderes e participantes
4. Calcule métricas de crescimento e velocidade
5. Compare com padrões de mercado quando possível
6. Indique claramente dados não encontrados em 'data_gaps'
"""

    def _process_result(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """Processa e valida o resultado do provider."""
        # Se o provider retornou um string JSON, fazer parse
        if isinstance(raw_data, str):
            try:
                data = json.loads(raw_data)
            except json.JSONDecodeError:
                logger.error("Resposta não é JSON válido")
                return self._get_empty_result()
        else:
            data = raw_data

        # Validar estrutura esperada
        required_keys = [
            "funding_rounds", "metrics", "analysis",
            "data_sources", "data_gaps"
        ]

        for key in required_keys:
            if key not in data:
                logger.warning(f"Campo obrigatório ausente: {key}")
                data[key] = self._get_default_value_for_key(key)

        # Processar funding rounds
        if "funding_rounds" in data and isinstance(data["funding_rounds"], list):
            processed_rounds = []
            for round_data in data["funding_rounds"]:
                if isinstance(round_data, dict):
                    # Normalizar tipo de round
                    if "round_type" in round_data:
                        round_data["round_type"] = self._normalize_round_type(
                            round_data["round_type"])
                    processed_rounds.append(round_data)
            data["funding_rounds"] = processed_rounds

        # Garantir que metrics seja um dict
        if not isinstance(data.get("metrics"), dict):
            data["metrics"] = {}

        # Garantir que analysis seja um dict
        if not isinstance(data.get("analysis"), dict):
            data["analysis"] = {}

        return data

    def _normalize_round_type(self, round_type: str) -> str:
        """Normaliza tipo de round para valores padrão."""
        round_type = round_type.lower().strip()

        mapping = {
            "pre-seed": "pre_seed",
            "preseed": "pre_seed",
            "seed": "seed",
            "serie a": "series_a",
            "série a": "series_a",
            "series a": "series_a",
            "serie b": "series_b",
            "série b": "series_b",
            "series b": "series_b",
            "serie c": "series_c",
            "série c": "series_c",
            "series c": "series_c",
            "serie d": "series_d",
            "série d": "series_d",
            "series d": "series_d",
            "bridge": "bridge",
            "debt": "debt",
            "grant": "grant",
            "ico": "ico",
            "ipo": "ipo",
            "acquisition": "acquisition",
            "aquisição": "acquisition"
        }

        return mapping.get(round_type, "other")

    def _calculate_confidence_score(self, data: Dict[str, Any]) -> float:
        """
        Calcula score de confiança baseado na completude dos dados.

        Returns:
            Score entre 0.0 e 1.0
        """
        total_points = 0
        earned_points = 0

        # Funding rounds (40% do score)
        if "funding_rounds" in data and isinstance(data["funding_rounds"], list):
            rounds = data["funding_rounds"]
            if len(rounds) > 0:
                total_points += 40
                # Pontos por completude dos rounds
                rounds_with_amount = sum(1 for r in rounds if r.get(
                    "amount_usd") or r.get("amount_brl"))
                rounds_with_date = sum(1 for r in rounds if r.get("date"))
                rounds_with_investors = sum(1 for r in rounds if r.get(
                    "lead_investors") or r.get("all_investors"))

                completeness = (rounds_with_amount + rounds_with_date +
                                rounds_with_investors) / (len(rounds) * 3)
                earned_points += 40 * completeness
        else:
            total_points += 40

        # Metrics (30% do score)
        if "metrics" in data and isinstance(data["metrics"], dict):
            total_points += 30
            metrics = data["metrics"]
            important_metrics = [
                "total_raised_usd", "total_raised_brl", "number_of_rounds",
                "valuation_growth_rate", "funding_velocity", "last_round_date"
            ]
            filled_metrics = sum(
                1 for m in important_metrics if metrics.get(m) is not None)
            earned_points += 30 * (filled_metrics / len(important_metrics))
        else:
            total_points += 30

        # Analysis (20% do score)
        if "analysis" in data and isinstance(data["analysis"], dict):
            total_points += 20
            analysis = data["analysis"]
            analysis_fields = [
                "strengths", "concerns", "investor_quality_assessment",
                "funding_momentum", "strategic_recommendations"
            ]
            filled_analysis = sum(
                1 for f in analysis_fields if analysis.get(f))
            earned_points += 20 * (filled_analysis / len(analysis_fields))
        else:
            total_points += 20

        # Data quality (10% do score)
        total_points += 10
        if "data_gaps" in data and isinstance(data["data_gaps"], list):
            # Penalizar por gaps de dados
            gap_penalty = min(len(data["data_gaps"]) * 2, 10)
            earned_points += 10 - gap_penalty
        else:
            earned_points += 10

        # Calcular score final
        confidence = earned_points / total_points if total_points > 0 else 0.0
        return round(confidence, 2)

    def _get_empty_result(self) -> Dict[str, Any]:
        """Retorna estrutura vazia quando não há dados."""
        return {
            "funding_rounds": [],
            "key_investors": [],
            "metrics": {
                "total_raised_usd": None,
                "total_raised_brl": None,
                "number_of_rounds": 0,
                "funding_velocity": "unknown"
            },
            "analysis": {
                "strengths": [],
                "concerns": [],
                "investor_quality_assessment": "Dados não disponíveis",
                "funding_momentum": "Dados não disponíveis",
                "strategic_recommendations": []
            },
            "market_context": {},
            "data_sources": [],
            "data_gaps": ["Dados de funding não encontrados"]
        }

    def _get_default_value_for_key(self, key: str) -> Any:
        """Retorna valor padrão para campos ausentes."""
        defaults = {
            "funding_rounds": [],
            "key_investors": [],
            "metrics": {
                "total_raised_usd": None,
                "total_raised_brl": None,
                "number_of_rounds": 0
            },
            "analysis": {
                "strengths": [],
                "concerns": [],
                "investor_quality_assessment": "",
                "funding_momentum": "",
                "strategic_recommendations": []
            },
            "market_context": {},
            "data_sources": [],
            "data_gaps": []
        }
        return defaults.get(key, [])
