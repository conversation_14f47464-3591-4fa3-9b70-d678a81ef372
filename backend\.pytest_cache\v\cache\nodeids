["app/tests/test_business_model_service.py::TestBusinessModelService::test_business_model_canvas_structure", "app/tests/test_business_model_service.py::TestBusinessModelService::test_confidence_score_calculation", "app/tests/test_business_model_service.py::TestBusinessModelService::test_distribution_model_structure", "app/tests/test_business_model_service.py::TestBusinessModelService::test_empty_additional_context", "app/tests/test_business_model_service.py::TestBusinessModelService::test_error_handling_exception", "app/tests/test_business_model_service.py::TestBusinessModelService::test_error_handling_json_decode", "app/tests/test_business_model_service.py::TestBusinessModelService::test_execute_mock_mode", "app/tests/test_business_model_service.py::TestBusinessModelService::test_maturity_score_calculation", "app/tests/test_business_model_service.py::TestBusinessModelService::test_metadata_fields", "app/tests/test_business_model_service.py::TestBusinessModelService::test_mock_data_scenarios", "app/tests/test_business_model_service.py::TestBusinessModelService::test_optional_fields", "app/tests/test_business_model_service.py::TestBusinessModelService::test_processing_time_tracking", "app/tests/test_business_model_service.py::TestBusinessModelService::test_real_mode_execution", "app/tests/test_business_model_service.py::TestBusinessModelService::test_recommendations_generation", "app/tests/test_business_model_service.py::TestBusinessModelService::test_recommendations_impact_ordering", "app/tests/test_business_model_service.py::TestBusinessModelService::test_required_fields", "app/tests/test_business_model_service.py::TestBusinessModelService::test_revenue_model_analysis", "app/tests/test_business_model_service.py::TestBusinessModelService::test_sample_output_structure", "app/tests/test_business_model_service.py::TestBusinessModelService::test_service_initialization", "app/tests/test_business_model_service.py::TestBusinessModelService::test_unit_economics_validation", "app/tests/test_business_model_service.py::TestBusinessModelService::test_validate_request_invalid_url", "app/tests/test_business_model_service.py::TestBusinessModelService::test_validate_request_missing_name", "app/tests/test_business_model_service.py::TestBusinessModelService::test_validate_request_valid", "app/tests/test_channels_reviews_service.py::TestChannelsReviewsService::test_calculate_channel_effectiveness_score", "app/tests/test_channels_reviews_service.py::TestChannelsReviewsService::test_calculate_confidence_score", "app/tests/test_channels_reviews_service.py::TestChannelsReviewsService::test_calculate_online_reputation_score", "app/tests/test_channels_reviews_service.py::TestChannelsReviewsService::test_error_result_creation", "app/tests/test_channels_reviews_service.py::TestChannelsReviewsService::test_execute_json_decode_error", "app/tests/test_channels_reviews_service.py::TestChannelsReviewsService::test_execute_provider_exception", "app/tests/test_channels_reviews_service.py::TestChannelsReviewsService::test_execute_success", "app/tests/test_channels_reviews_service.py::TestChannelsReviewsService::test_execute_with_mock_mode", "app/tests/test_channels_reviews_service.py::TestChannelsReviewsService::test_execute_with_optional_fields", "app/tests/test_channels_reviews_service.py::TestChannelsReviewsService::test_generate_recommendations", "app/tests/test_channels_reviews_service.py::TestChannelsReviewsService::test_get_mock_data_selection", "app/tests/test_channels_reviews_service.py::TestChannelsReviewsService::test_get_sample_output_structure", "app/tests/test_channels_reviews_service.py::TestChannelsReviewsService::test_normalize_sentiment_distribution", "app/tests/test_channels_reviews_service.py::TestChannelsReviewsService::test_process_result_invalid_json", "app/tests/test_channels_reviews_service.py::TestChannelsReviewsService::test_recommendations_priority_sorting", "app/tests/test_channels_reviews_service.py::TestChannelsReviewsService::test_service_metadata", "app/tests/test_channels_reviews_service.py::TestChannelsReviewsService::test_validate_request_invalid_url", "app/tests/test_channels_reviews_service.py::TestChannelsReviewsService::test_validate_request_missing_name", "app/tests/test_channels_reviews_service.py::TestChannelsReviewsService::test_validate_request_valid", "app/tests/test_digital_presence_service.py::TestDigitalPresenceService::test_analyze_content_strategy", "app/tests/test_digital_presence_service.py::TestDigitalPresenceService::test_analyze_error_handling", "app/tests/test_digital_presence_service.py::TestDigitalPresenceService::test_analyze_online_reputation", "app/tests/test_digital_presence_service.py::TestDigitalPresenceService::test_analyze_seo_metrics", "app/tests/test_digital_presence_service.py::TestDigitalPresenceService::test_analyze_social_media", "app/tests/test_digital_presence_service.py::TestDigitalPresenceService::test_analyze_website", "app/tests/test_digital_presence_service.py::TestDigitalPresenceService::test_analyze_with_mock_data", "app/tests/test_digital_presence_service.py::TestDigitalPresenceService::test_analyze_with_provider", "app/tests/test_digital_presence_service.py::TestDigitalPresenceService::test_build_analysis_query", "app/tests/test_digital_presence_service.py::TestDigitalPresenceService::test_calculate_confidence_score", "app/tests/test_digital_presence_service.py::TestDigitalPresenceService::test_calculate_digital_maturity", "app/tests/test_digital_presence_service.py::TestDigitalPresenceService::test_empty_social_platforms", "app/tests/test_digital_presence_service.py::TestDigitalPresenceService::test_generate_recommendations", "app/tests/test_digital_presence_service.py::TestDigitalPresenceService::test_init", "app/tests/test_digital_presence_service.py::TestDigitalPresenceService::test_init_with_provider", "app/tests/test_digital_presence_service.py::TestDigitalPresenceService::test_integration_flow", "app/tests/test_digital_presence_service.py::TestDigitalPresenceService::test_partial_data_handling", "app/tests/test_digital_presence_service.py::TestDigitalPresenceService::test_score_seo_maturity", "app/tests/test_digital_presence_service.py::TestDigitalPresenceService::test_to_dict_conversion", "app/tests/test_funding_history_service.py::TestFundingHistoryService::test_confidence_score_calculation", "app/tests/test_funding_history_service.py::TestFundingHistoryService::test_confidence_score_with_gaps", "app/tests/test_funding_history_service.py::TestFundingHistoryService::test_currency_options", "app/tests/test_funding_history_service.py::TestFundingHistoryService::test_exclude_competitors", "app/tests/test_funding_history_service.py::TestFundingHistoryService::test_exclude_investors", "app/tests/test_funding_history_service.py::TestFundingHistoryService::test_execute_success", "app/tests/test_funding_history_service.py::TestFundingHistoryService::test_execute_with_error", "app/tests/test_funding_history_service.py::TestFundingHistoryService::test_execute_with_invalid_json", "app/tests/test_funding_history_service.py::TestFundingHistoryService::test_execute_with_json_string", "app/tests/test_funding_history_service.py::TestFundingHistoryService::test_execute_with_partial_data", "app/tests/test_funding_history_service.py::TestFundingHistoryService::test_optional_fields", "app/tests/test_funding_history_service.py::TestFundingHistoryService::test_processing_time", "app/tests/test_funding_history_service.py::TestFundingHistoryService::test_required_fields", "app/tests/test_funding_history_service.py::TestFundingHistoryService::test_round_type_normalization", "app/tests/test_funding_history_service.py::TestFundingHistoryService::test_sample_output_structure", "app/tests/test_funding_history_service.py::TestFundingHistoryService::test_service_metadata", "app/tests/test_funding_history_service.py::TestFundingHistoryService::test_validate_request_missing_name", "app/tests/test_funding_history_service.py::TestFundingHistoryService::test_validate_request_missing_url", "app/tests/test_funding_history_service.py::TestFundingHistoryService::test_validate_request_short_name", "app/tests/test_funding_history_service.py::TestFundingHistoryService::test_validate_request_valid", "app/tests/test_market_research_service.py::TestMarketResearchService::test_assess_data_completeness", "app/tests/test_market_research_service.py::TestMarketResearchService::test_build_context", "app/tests/test_market_research_service.py::TestMarketResearchService::test_build_context_empty_request", "app/tests/test_market_research_service.py::TestMarketResearchService::test_build_context_with_additional_context", "app/tests/test_market_research_service.py::TestMarketResearchService::test_confidence_score_calculation_complete_data", "app/tests/test_market_research_service.py::TestMarketResearchService::test_confidence_score_calculation_minimal_data", "app/tests/test_market_research_service.py::TestMarketResearchService::test_confidence_score_calculation_partial_data", "app/tests/test_market_research_service.py::TestMarketResearchService::test_data_completeness_levels[0.2-<PERSON><PERSON>]", "app/tests/test_market_research_service.py::TestMarketResearchService::test_data_completeness_levels[0.5-Baixo]", "app/tests/test_market_research_service.py::TestMarketResearchService::test_data_completeness_levels[0.7-M\\xe9dio]", "app/tests/test_market_research_service.py::TestMarketResearchService::test_data_completeness_levels[0.9-Alto]", "app/tests/test_market_research_service.py::TestMarketResearchService::test_execute_success", "app/tests/test_market_research_service.py::TestMarketResearchService::test_execute_with_invalid_json", "app/tests/test_market_research_service.py::TestMarketResearchService::test_execute_with_json_string_response", "app/tests/test_market_research_service.py::TestMarketResearchService::test_execute_with_partial_data", "app/tests/test_market_research_service.py::TestMarketResearchService::test_execute_with_provider_error", "app/tests/test_market_research_service.py::TestMarketResearchService::test_execute_with_special_characters", "app/tests/test_market_research_service.py::TestMarketResearchService::test_get_empty_result", "app/tests/test_market_research_service.py::TestMarketResearchService::test_process_competitors_with_missing_fields", "app/tests/test_market_research_service.py::TestMarketResearchService::test_required_and_optional_fields", "app/tests/test_market_research_service.py::TestMarketResearchService::test_sample_output_structure", "app/tests/test_market_research_service.py::TestMarketResearchService::test_service_metadata", "app/tests/test_market_research_service.py::TestMarketResearchService::test_validate_request_invalid_url", "app/tests/test_market_research_service.py::TestMarketResearchService::test_validate_request_missing_company_name", "app/tests/test_market_research_service.py::TestMarketResearchService::test_validate_request_success", "app/tests/test_partnerships_service.py::TestPartnershipService::test_analyze_commercial_partnerships", "app/tests/test_partnerships_service.py::TestPartnershipService::test_analyze_error_handling", "app/tests/test_partnerships_service.py::TestPartnershipService::test_analyze_strategic_partnerships", "app/tests/test_partnerships_service.py::TestPartnershipService::test_analyze_technology_partnerships", "app/tests/test_partnerships_service.py::TestPartnershipService::test_analyze_with_mock_data", "app/tests/test_partnerships_service.py::TestPartnershipService::test_analyze_with_provider", "app/tests/test_partnerships_service.py::TestPartnershipService::test_build_analysis_query", "app/tests/test_partnerships_service.py::TestPartnershipService::test_calculate_confidence_score", "app/tests/test_partnerships_service.py::TestPartnershipService::test_calculate_partnership_ecosystem_score", "app/tests/test_partnerships_service.py::TestPartnershipService::test_channel_strategy_determination", "app/tests/test_partnerships_service.py::TestPartnershipService::test_empty_data_handling", "app/tests/test_partnerships_service.py::TestPartnershipService::test_generate_recommendations", "app/tests/test_partnerships_service.py::TestPartnershipService::test_init", "app/tests/test_partnerships_service.py::TestPartnershipService::test_init_with_provider", "app/tests/test_partnerships_service.py::TestPartnershipService::test_integration_flow", "app/tests/test_partnerships_service.py::TestPartnershipService::test_mock_data_variations", "app/tests/test_partnerships_service.py::TestPartnershipService::test_score_commercial_partnerships", "app/tests/test_partnerships_service.py::TestPartnershipService::test_score_technology_partnerships", "app/tests/test_partnerships_service.py::TestPartnershipService::test_to_dict_conversion", "app/tests/test_pricing_analysis_service.py::TestPricingAnalysisService::test_build_prompts", "app/tests/test_pricing_analysis_service.py::TestPricingAnalysisService::test_calculate_confidence_score_high", "app/tests/test_pricing_analysis_service.py::TestPricingAnalysisService::test_calculate_confidence_score_low", "app/tests/test_pricing_analysis_service.py::TestPricingAnalysisService::test_calculate_confidence_score_medium", "app/tests/test_pricing_analysis_service.py::TestPricingAnalysisService::test_calculate_pricing_maturity_score", "app/tests/test_pricing_analysis_service.py::TestPricingAnalysisService::test_calculate_pricing_maturity_score_low", "app/tests/test_pricing_analysis_service.py::TestPricingAnalysisService::test_create_error_result", "app/tests/test_pricing_analysis_service.py::TestPricingAnalysisService::test_execute_exception", "app/tests/test_pricing_analysis_service.py::TestPricingAnalysisService::test_execute_invalid_json", "app/tests/test_pricing_analysis_service.py::TestPricingAnalysisService::test_execute_json_string_response", "app/tests/test_pricing_analysis_service.py::TestPricingAnalysisService::test_execute_success", "app/tests/test_pricing_analysis_service.py::TestPricingAnalysisService::test_execute_with_mock_mode", "app/tests/test_pricing_analysis_service.py::TestPricingAnalysisService::test_execute_without_target_market", "app/tests/test_pricing_analysis_service.py::TestPricingAnalysisService::test_generate_recommendations_few_tiers", "app/tests/test_pricing_analysis_service.py::TestPricingAnalysisService::test_generate_recommendations_high_elasticity", "app/tests/test_pricing_analysis_service.py::TestPricingAnalysisService::test_get_empty_pricing_analysis_structure", "app/tests/test_pricing_analysis_service.py::TestPricingAnalysisService::test_get_mock_data_default", "app/tests/test_pricing_analysis_service.py::TestPricingAnalysisService::test_get_mock_data_ecommerce", "app/tests/test_pricing_analysis_service.py::TestPricingAnalysisService::test_get_mock_data_saas", "app/tests/test_pricing_analysis_service.py::TestPricingAnalysisService::test_get_sample_output", "app/tests/test_pricing_analysis_service.py::TestPricingAnalysisService::test_normalize_pricing_tiers", "app/tests/test_pricing_analysis_service.py::TestPricingAnalysisService::test_process_result_missing_sections", "app/tests/test_pricing_analysis_service.py::TestPricingAnalysisService::test_process_result_valid_data", "app/tests/test_pricing_analysis_service.py::TestPricingAnalysisService::test_required_and_optional_fields", "app/tests/test_pricing_analysis_service.py::TestPricingAnalysisService::test_service_metadata", "app/tests/test_pricing_analysis_service.py::TestPricingAnalysisService::test_validate_request_invalid_url", "app/tests/test_pricing_analysis_service.py::TestPricingAnalysisService::test_validate_request_missing_name", "app/tests/test_pricing_analysis_service.py::TestPricingAnalysisService::test_validate_request_missing_url", "app/tests/test_pricing_analysis_service.py::TestPricingAnalysisService::test_validate_request_success", "app/tests/test_swot_analysis_service.py::TestSwotAnalysisService::test_service_metadata", "app/tests/test_tech_diagnostic_service.py::TestTechDiagnosticService::test_accessibility_issues_generation", "app/tests/test_tech_diagnostic_service.py::TestTechDiagnosticService::test_build_prompts", "app/tests/test_tech_diagnostic_service.py::TestTechDiagnosticService::test_confidence_score_calculation", "app/tests/test_tech_diagnostic_service.py::TestTechDiagnosticService::test_execute_accessibility_data", "app/tests/test_tech_diagnostic_service.py::TestTechDiagnosticService::test_execute_best_practices_data", "app/tests/test_tech_diagnostic_service.py::TestTechDiagnosticService::test_execute_performance_data", "app/tests/test_tech_diagnostic_service.py::TestTechDiagnosticService::test_execute_recommendations", "app/tests/test_tech_diagnostic_service.py::TestTechDiagnosticService::test_execute_security_data", "app/tests/test_tech_diagnostic_service.py::TestTechDiagnosticService::test_execute_seo_data", "app/tests/test_tech_diagnostic_service.py::TestTechDiagnosticService::test_execute_success", "app/tests/test_tech_diagnostic_service.py::TestTechDiagnosticService::test_execute_technology_stack", "app/tests/test_tech_diagnostic_service.py::TestTechDiagnosticService::test_execute_with_exception", "app/tests/test_tech_diagnostic_service.py::TestTechDiagnosticService::test_get_sample_output", "app/tests/test_tech_diagnostic_service.py::TestTechDiagnosticService::test_optional_fields", "app/tests/test_tech_diagnostic_service.py::TestTechDiagnosticService::test_overall_score_calculation", "app/tests/test_tech_diagnostic_service.py::TestTechDiagnosticService::test_performance_opportunities_generation", "app/tests/test_tech_diagnostic_service.py::TestTechDiagnosticService::test_recommendations_generation", "app/tests/test_tech_diagnostic_service.py::TestTechDiagnosticService::test_required_fields", "app/tests/test_tech_diagnostic_service.py::TestTechDiagnosticService::test_service_initialization", "app/tests/test_tech_diagnostic_service.py::TestTechDiagnosticService::test_top_recommendations_prioritization", "app/tests/test_tech_diagnostic_service.py::TestTechDiagnosticService::test_validate_request_invalid_url", "app/tests/test_tech_diagnostic_service.py::TestTechDiagnosticService::test_validate_request_missing_name", "app/tests/test_tech_diagnostic_service.py::TestTechDiagnosticService::test_validate_request_missing_url", "app/tests/test_tech_diagnostic_service.py::TestTechDiagnosticService::test_validate_request_valid", "app/tests/test_tech_diagnostic_service.py::TestTechDiagnosticService::test_vulnerabilities_generation", "app/tests/test_tech_stack_service.py::TestTechStackService::test_build_prompts", "app/tests/test_tech_stack_service.py::TestTechStackService::test_calculate_confidence_score_high", "app/tests/test_tech_stack_service.py::TestTechStackService::test_calculate_confidence_score_low", "app/tests/test_tech_stack_service.py::TestTechStackService::test_calculate_confidence_score_medium", "app/tests/test_tech_stack_service.py::TestTechStackService::test_confidence_score_adjustment", "app/tests/test_tech_stack_service.py::TestTechStackService::test_count_technologies", "app/tests/test_tech_stack_service.py::TestTechStackService::test_count_technologies_empty", "app/tests/test_tech_stack_service.py::TestTechStackService::test_create_error_result", "app/tests/test_tech_stack_service.py::TestTechStackService::test_execute_exception", "app/tests/test_tech_stack_service.py::TestTechStackService::test_execute_invalid_json", "app/tests/test_tech_stack_service.py::TestTechStackService::test_execute_json_string_response", "app/tests/test_tech_stack_service.py::TestTechStackService::test_execute_success", "app/tests/test_tech_stack_service.py::TestTechStackService::test_execute_without_industry", "app/tests/test_tech_stack_service.py::TestTechStackService::test_get_empty_tech_stack_structure", "app/tests/test_tech_stack_service.py::TestTechStackService::test_get_sample_output", "app/tests/test_tech_stack_service.py::TestTechStackService::test_normalize_category", "app/tests/test_tech_stack_service.py::TestTechStackService::test_process_result_missing_categories", "app/tests/test_tech_stack_service.py::TestTechStackService::test_process_result_valid_data", "app/tests/test_tech_stack_service.py::TestTechStackService::test_required_and_optional_fields", "app/tests/test_tech_stack_service.py::TestTechStackService::test_service_metadata", "app/tests/test_tech_stack_service.py::TestTechStackService::test_validate_request_invalid_url", "app/tests/test_tech_stack_service.py::TestTechStackService::test_validate_request_missing_name", "app/tests/test_tech_stack_service.py::TestTechStackService::test_validate_request_missing_url", "app/tests/test_tech_stack_service.py::TestTechStackService::test_validate_request_success"]