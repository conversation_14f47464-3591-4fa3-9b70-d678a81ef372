"""
Analisador de Performance usando Google Lighthouse

Executa análises automatizadas de:
- Performance (Core Web Vitals)
- Acessibilidade
- SEO técnico
- Best practices

Versão melhorada com:
- Async/await
- Integração MongoDB
- Melhor tratamento de erros
- Cache de resultados
"""

import json
import asyncio
import subprocess
from typing import Dict, Any, Optional
import re
import os
import logging
from datetime import datetime, timezone, timedelta
from motor.motor_asyncio import AsyncIOMotorDatabase
from bson import ObjectId

from .interfaces import LighthouseData, LighthouseMetrics

logger = logging.getLogger(__name__)


class LighthouseAnalyzer:
    """
    Analisador de performance usando Google Lighthouse

    Versão async com integração MongoDB e cache de resultados
    """

    def __init__(self, db: AsyncIOMotorDatabase, chrome_flags: Optional[str] = None):
        """
        Inicializa o analisador Lighthouse

        Args:
            db: Conexão MongoDB assíncrona
            chrome_flags: Flags adicionais para o Chrome
        """
        self.db = db
        self.collection = db.diagnostic_results
        self.chrome_flags = chrome_flags or (
            "--headless --no-sandbox --disable-gpu --disable-dev-shm-usage "
            "--disable-background-timer-throttling --disable-backgrounding-occluded-windows "
            "--disable-renderer-backgrounding"
        )

        # Cache settings
        self.cache_duration = timedelta(hours=24)

        # Detectar Chrome
        self.chrome_path = self._detect_chrome_path()

        # AIDEV-NOTE: Verificação assíncrona movida para método check_availability()
        self.lighthouse_available = None

    async def check_availability(self) -> bool:
        """
        Verifica se Lighthouse está disponível (versão async)

        Returns:
            True se disponível, False caso contrário
        """
        if self.lighthouse_available is not None:
            return self.lighthouse_available

        try:
            process = await asyncio.create_subprocess_exec(
                "lighthouse", "--version",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await asyncio.wait_for(
                process.communicate(),
                timeout=10
            )

            if process.returncode == 0:
                version = stdout.decode().strip()
                logger.info(f"🔍 Lighthouse encontrado - Versão: {version}")
                self.lighthouse_available = True
                return True

        except (asyncio.TimeoutError, FileNotFoundError, Exception) as e:
            logger.error(f"❌ Lighthouse não encontrado: {str(e)}")

        self.lighthouse_available = False
        return False

    def _detect_chrome_path(self) -> Optional[str]:
        """Detecta automaticamente o caminho do Chrome/Chromium"""
        # Verificar variáveis de ambiente
        chrome_env_vars = [
            'CHROME_PATH', 'CHROME_BIN',
            'LIGHTHOUSE_CHROMIUM_PATH', 'PUPPETEER_EXECUTABLE_PATH'
        ]

        for env_var in chrome_env_vars:
            chrome_path = os.environ.get(env_var)
            if chrome_path and os.path.isfile(chrome_path):
                return chrome_path

        # Procurar em locais comuns
        common_paths = [
            '/usr/bin/google-chrome-stable',
            '/usr/bin/google-chrome',
            '/usr/bin/chromium-browser',
            '/usr/bin/chromium',
            '/opt/google/chrome/chrome',
            '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
            # Windows paths
            'C:/Program Files/Google/Chrome/Application/chrome.exe',
            'C:/Program Files (x86)/Google/Chrome/Application/chrome.exe'
        ]

        for path in common_paths:
            if os.path.isfile(path):
                return path

        return None

    async def get_cached_result(self, url: str, client_id: str) -> Optional[LighthouseData]:
        """
        Busca resultado em cache no MongoDB

        Args:
            url: URL analisada
            client_id: ID do cliente

        Returns:
            Dados do Lighthouse se encontrado e válido
        """
        try:
            # Buscar resultado mais recente
            cutoff_time = datetime.now(timezone.utc) - self.cache_duration

            result = await self.collection.find_one({
                "client_id": ObjectId(client_id),
                "url": url,
                "timestamp": {"$gte": cutoff_time},
                "lighthouse_data._real_data": True,
                "status": "completed"
            })

            if result and "lighthouse_data" in result:
                logger.info(f"✅ Cache hit para Lighthouse analysis de {url}")
                return result["lighthouse_data"]

        except Exception as e:
            logger.error(f"Erro ao buscar cache: {str(e)}")

        return None

    async def run_analysis(self, url: str, client_id: str, force_refresh: bool = False) -> LighthouseData:
        """
        Executa análise REAL do Lighthouse para uma URL

        Args:
            url: URL do website para análise
            client_id: ID do cliente para vinculação
            force_refresh: Forçar nova análise ignorando cache

        Returns:
            Dados estruturados da análise Lighthouse
        """
        if not self._is_valid_url(url):
            raise ValueError(f"URL inválida: {url}")

        # Verificar disponibilidade
        if not await self.check_availability():
            raise Exception(
                "❌ ERRO CRÍTICO: Lighthouse não está disponível! "
                "Instale com: npm install -g lighthouse"
            )

        # Verificar cache se não forçar refresh
        if not force_refresh:
            cached = await self.get_cached_result(url, client_id)
            if cached:
                return cached

        logger.info(f"🚀 Iniciando análise REAL do Lighthouse para: {url}")

        try:
            # AIDEV-NOTE: Executar Lighthouse em processo separado (ainda sync por limitação do subprocess)
            result = await self._run_lighthouse_process(url)

            # Extrair dados estruturados
            structured_data = self._extract_structured_data(result)
            structured_data["_real_data"] = True
            structured_data["_lighthouse_version"] = await self._get_lighthouse_version()

            # Salvar no MongoDB
            await self._save_to_mongodb(url, client_id, structured_data)

            logger.info(
                f"📈 Análise processada - Score Performance: {structured_data['performance']['score']}%"
            )

            return structured_data

        except Exception as e:
            logger.error(f"Erro na análise Lighthouse: {str(e)}")
            raise

    async def _run_lighthouse_process(self, url: str) -> Dict[str, Any]:
        """Executa o processo Lighthouse de forma assíncrona"""
        cmd = [
            "lighthouse",
            url,
            "--output=json",
            "--only-categories=performance,accessibility,seo,best-practices",
            f"--chrome-flags={self.chrome_flags}",
            "--quiet",
            "--no-enable-error-reporting"
        ]

        if self.chrome_path:
            cmd.extend(["--chrome-path", self.chrome_path])

        logger.debug(f"📊 Executando: {' '.join(cmd[:3])}...")

        # Configurar ambiente
        env = os.environ.copy()
        if self.chrome_path:
            env['CHROME_PATH'] = self.chrome_path
            env['PUPPETEER_EXECUTABLE_PATH'] = self.chrome_path

        # AIDEV-TODO: Considerar usar asyncio.create_subprocess_exec quando possível
        # Por ora, executar em thread pool para não bloquear
        loop = asyncio.get_event_loop()

        def run_subprocess():
            return subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=180,
                env=env
            )

        result = await loop.run_in_executor(None, run_subprocess)

        if result.returncode != 0:
            error_msg = result.stderr or "Erro desconhecido"
            raise Exception(
                f"Lighthouse falhou com código {result.returncode}: {error_msg}")

        logger.info("✅ Análise Lighthouse concluída com sucesso!")

        try:
            return json.loads(result.stdout)
        except json.JSONDecodeError as e:
            raise Exception(
                f"JSON inválido retornado pelo Lighthouse: {str(e)}")

    async def _get_lighthouse_version(self) -> str:
        """Obtém versão do Lighthouse instalado (async)"""
        try:
            process = await asyncio.create_subprocess_exec(
                "lighthouse", "--version",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, _ = await asyncio.wait_for(
                process.communicate(),
                timeout=5
            )

            return stdout.decode().strip() if process.returncode == 0 else "unknown"

        except Exception:
            return "unknown"

    def _is_valid_url(self, url: str) -> bool:
        """Valida se a URL está em formato correto"""
        if not url or not isinstance(url, str):
            return False

        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            # domain...
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)

        return bool(url_pattern.match(url))

    def _extract_structured_data(self, lighthouse_data: Dict[str, Any]) -> LighthouseData:
        """
        Extrai dados estruturados do resultado Lighthouse

        Args:
            lighthouse_data: Dados brutos do Lighthouse

        Returns:
            Dados estruturados no formato esperado
        """
        try:
            # Lighthouse pode retornar dados diretamente ou dentro de 'lhr'
            if "lhr" in lighthouse_data:
                data = lighthouse_data["lhr"]
            else:
                data = lighthouse_data

            categories = data.get("categories", {})
            audits = data.get("audits", {})

            # Extrair scores (0-1 -> 0-100)
            performance_score = int(categories.get(
                "performance", {}).get("score", 0) * 100)
            accessibility_score = int(categories.get(
                "accessibility", {}).get("score", 0) * 100)
            seo_score = int(categories.get("seo", {}).get("score", 0) * 100)
            best_practices_score = int(categories.get(
                "best-practices", {}).get("score", 0) * 100)

            logger.debug(
                f"📊 Scores: Performance={performance_score}%, "
                f"Acessibilidade={accessibility_score}%, SEO={seo_score}%, "
                f"Best Practices={best_practices_score}%"
            )

            # Extrair dados detalhados
            return {
                "performance": {
                    "score": performance_score,
                    "metricas_core": self._extract_core_web_vitals(audits),
                    "oportunidades": self._extract_opportunities(audits),
                    "diagnosticos": self._extract_diagnostics(audits)
                },
                "accessibility": {
                    "score": accessibility_score,
                    "issues": self._extract_accessibility_issues(audits),
                    "recomendacoes": self._extract_accessibility_recommendations(audits)
                },
                "seo_tecnico": {
                    "score": seo_score,
                    **self._extract_seo_data(audits)
                },
                "best_practices": {
                    "score": best_practices_score,
                    **self._extract_best_practices_data(audits)
                },
                "_real_data": True,
                "_lighthouse_version": "pending"
            }

        except Exception as e:
            logger.error(f"❌ Erro ao extrair dados estruturados: {str(e)}")
            raise

    def _extract_core_web_vitals(self, audits: Dict[str, Any]) -> LighthouseMetrics:
        """Extrai métricas Core Web Vitals"""
        try:
            return {
                "fcp": int(audits.get("first-contentful-paint", {}).get("numericValue", 0)),
                "lcp": int(audits.get("largest-contentful-paint", {}).get("numericValue", 0)),
                "cls": round(audits.get("cumulative-layout-shift", {}).get("numericValue", 0), 3),
                "fid": int(audits.get("max-potential-fid", {}).get("numericValue", 0)),
                "ttfb": int(audits.get("server-response-time", {}).get("numericValue", 0)),
                "speed_index": int(audits.get("speed-index", {}).get("numericValue", 0)),
                "time_to_interactive": int(audits.get("interactive", {}).get("numericValue", 0)),
                "total_blocking_time": int(audits.get("total-blocking-time", {}).get("numericValue", 0))
            }
        except Exception:
            return {
                "fcp": 0,
                "lcp": 0,
                "cls": 0.0,
                "fid": 0,
                "ttfb": 0,
                "speed_index": 0,
                "time_to_interactive": 0,
                "total_blocking_time": 0
            }

    def _extract_opportunities(self, audits: Dict[str, Any]) -> list:
        """Extrai oportunidades de melhoria"""
        opportunities = []

        opportunity_audits = [
            "render-blocking-resources", "unused-css-rules", "unused-javascript",
            "modern-image-formats", "offscreen-images", "unminified-css",
            "unminified-javascript", "efficiently-encode-images", "uses-responsive-images"
        ]

        for audit_id in opportunity_audits:
            audit = audits.get(audit_id, {})
            if audit.get("score", 1) < 1:
                opportunities.append(audit.get("title", audit_id))

        return opportunities[:10]  # Limite de 10 oportunidades

    def _extract_diagnostics(self, audits: Dict[str, Any]) -> list:
        """Extrai diagnósticos de problemas"""
        diagnostics = []

        diagnostic_audits = [
            "mainthread-work-breakdown", "bootup-time", "uses-long-cache-ttl",
            "total-byte-weight", "dom-size", "font-display", "critical-request-chains"
        ]

        for audit_id in diagnostic_audits:
            audit = audits.get(audit_id, {})
            if audit.get("score", 1) < 0.9:
                diagnostics.append(audit.get("title", audit_id))

        return diagnostics[:8]

    def _extract_accessibility_issues(self, audits: Dict[str, Any]) -> list:
        """Extrai issues de acessibilidade"""
        issues = []

        accessibility_audits = [
            "color-contrast", "image-alt", "form-field-multiple-labels",
            "heading-order", "link-name", "button-name", "aria-valid-attr",
            "aria-roles", "duplicate-id-aria"
        ]

        for audit_id in accessibility_audits:
            audit = audits.get(audit_id, {})
            if audit.get("score", 1) < 1:
                issues.append(audit.get("title", audit_id))

        return issues[:10]

    def _extract_accessibility_recommendations(self, audits: Dict[str, Any]) -> list:
        """Gera recomendações de acessibilidade baseadas nas issues"""
        recommendations = []

        if audits.get("color-contrast", {}).get("score", 1) < 1:
            recommendations.append(
                "Melhorar contraste de cores para WCAG 2.1 AA")
        if audits.get("image-alt", {}).get("score", 1) < 1:
            recommendations.append(
                "Adicionar textos alternativos descritivos em imagens")
        if audits.get("heading-order", {}).get("score", 1) < 1:
            recommendations.append("Corrigir hierarquia de cabeçalhos (H1-H6)")
        if audits.get("link-name", {}).get("score", 1) < 1:
            recommendations.append("Adicionar textos descritivos em links")
        if audits.get("button-name", {}).get("score", 1) < 1:
            recommendations.append(
                "Garantir que botões tenham nomes acessíveis")

        # AIDEV-NOTE: Adicionar mais recomendações baseadas em WCAG 2.1
        return recommendations[:7]

    def _extract_seo_data(self, audits: Dict[str, Any]) -> Dict[str, Any]:
        """Extrai dados de SEO"""
        try:
            return {
                "meta_tags": "adequado" if audits.get("meta-description", {}).get("score", 0) >= 1 else "necessita melhoria",
                "estrutura_html": "adequada" if audits.get("document-title", {}).get("score", 0) >= 1 else "necessita melhoria",
                "mobile_friendly": "sim" if audits.get("viewport", {}).get("score", 0) >= 1 else "necessita ajustes",
                "crawlable": "sim" if audits.get("crawlable-anchors", {}).get("score", 0) >= 1 else "problemas detectados",
                "indexable": "sim" if audits.get("is-crawlable", {}).get("score", 0) >= 1 else "bloqueado"
            }
        except Exception:
            return {
                "meta_tags": "não analisado",
                "estrutura_html": "não analisado",
                "mobile_friendly": "não analisado"
            }

    def _extract_best_practices_data(self, audits: Dict[str, Any]) -> Dict[str, Any]:
        """Extrai dados de best practices"""
        try:
            vulnerabilities = []

            if audits.get("no-vulnerable-libraries", {}).get("score", 1) < 1:
                vulnerabilities.append(
                    "Bibliotecas JavaScript com vulnerabilidades conhecidas")
            if audits.get("csp-xss", {}).get("score", 1) < 1:
                vulnerabilities.append("Content Security Policy inadequada")
            if audits.get("errors-in-console", {}).get("score", 1) < 1:
                vulnerabilities.append("Erros JavaScript no console")

            return {
                "https": audits.get("is-on-https", {}).get("score", 0) >= 1,
                "vulnerabilidades": vulnerabilities,
                "console_errors": audits.get("errors-in-console", {}).get("score", 0) < 1,
                "uses_http2": True  # AIDEV-TODO: Detectar HTTP/2 de forma mais precisa
            }
        except Exception:
            return {
                "https": False,
                "vulnerabilidades": []
            }

    async def _save_to_mongodb(self, url: str, client_id: str, lighthouse_data: LighthouseData) -> None:
        """
        Salva resultado da análise no MongoDB

        Args:
            url: URL analisada
            client_id: ID do cliente
            lighthouse_data: Dados do Lighthouse
        """
        try:
            # Criar ou atualizar documento
            await self.collection.update_one(
                {
                    "client_id": ObjectId(client_id),
                    "url": url
                },
                {
                    "$set": {
                        "lighthouse_data": lighthouse_data,
                        "timestamp": datetime.now(timezone.utc),
                        "status": "completed"
                    },
                    "$setOnInsert": {
                        "client_id": ObjectId(client_id),
                        "url": url
                    }
                },
                upsert=True
            )

            logger.info(
                f"✅ Dados Lighthouse salvos no MongoDB para cliente {client_id}")

        except Exception as e:
            logger.error(f"Erro ao salvar no MongoDB: {str(e)}")
            # Não propagar erro - análise ainda é válida mesmo sem cache
