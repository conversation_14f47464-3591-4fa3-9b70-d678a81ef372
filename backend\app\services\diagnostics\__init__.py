"""
Serviços de Diagnóstico Técnico

Este módulo contém serviços para análise técnica de websites:
- Lighthouse: Análise de performance, acessibilidade, SEO
- Playwright: Captura de screenshots
- Visual Analyzer: Análise visual por IA
- Consolidator: Consolidação de resultados
"""

# Interfaces e tipos
from .interfaces import (
    DiagnosticRequest,
    DiagnosticResult,
    LighthouseData,
    ScreenshotData,
    VisualAnalysisResult,
    ViewportConfig,
    ScreenshotMetadata,
    LighthouseMetrics,
    LighthouseCategory
)

# AIDEV-NOTE: Serviços implementados
from .lighthouse_analyzer import LighthouseAnalyzer
from .playwright_screenshots import PlaywrightScreenshots
from .visual_analyzer import VisualAnalyzer
from .consolidator import DiagnosticConsolidator

__all__ = [
    # Interfaces
    "DiagnosticRequest",
    "DiagnosticResult",
    "LighthouseData",
    "ScreenshotData",
    "VisualAnalysisResult",
    "ViewportConfig",
    "ScreenshotMetadata",
    "LighthouseMetrics",
    "LighthouseCategory",

    # Serviços
    "LighthouseAnalyzer",
    "PlaywrightScreenshots",
    "VisualAnalyzer",
    "DiagnosticConsolidator"
]
