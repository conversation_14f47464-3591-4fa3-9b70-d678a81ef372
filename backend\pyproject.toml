[project]
name = "innovation-scope-ai"
version = "0.1.0"
description = "É nós!"
authors = [{ name = "<PERSON>", email = "<EMAIL>" }]
readme = "README.md"
requires-python = ">=3.11,<3.14"
dependencies = [
	"setuptools (>=69.0.0,<70.0.0)",
	"wheel (>=0.42.0,<0.43.0)",
	"fastapi (>=0.115.12,<0.116.0)",
	"uvicorn[standard] (>=0.34.3,<0.35.0)",
	"python-dotenv (>=1.1.0,<2.0.0)",
	"python-multipart (>=0.0.20,<0.0.21)",
	"requests (>=2.32.4,<3.0.0)",
	"pydantic (>=2.11.7,<3.0.0)",
	"pydantic-settings (>=2.9.1,<3.0.0)",
	"loguru (>=0.7.3,<0.8.0)",
	"beautifulsoup4 (>=4.13.4,<5.0.0)",
	"lxml (>=5.4.0,<6.0.0)",
	"aiofiles (>=24.1.0,<25.0.0)",
	"asyncio (>=3.4.3,<4.0.0)",
	"tavily-python (>=0.7.6,<0.8.0)",
	"perplexipy (>=1.3.1,<2.0.0)",
	"agno (>=1.6.2,<2.0.0)",
	"langchain (>=0.3.25,<0.4.0)",
	"langchain-community (>=0.3.25,<0.4.0)",
	"langchain-openai (>=0.3.23,<0.4.0)",
	"langchain-text-splitters (>=0.3.8,<0.4.0)",
	"pandas (>=2.3.0,<3.0.0)",
	"openpyxl (>=3.1.5,<4.0.0)",
	"xlsxwriter (>=3.2.3,<4.0.0)",
	"seaborn (>=0.13.2,<0.14.0)",
	"matplotlib (>=3.10.3,<4.0.0)",
	"scikit-learn (>=1.7.0,<2.0.0)",
	"numpy (>=2.3.0,<3.0.0)",
	"jinja2 (>=3.1.6,<4.0.0)",
	"playwright (>=1.52.0,<2.0.0)",
	"pymongo (>=4.13.1,<5.0.0)",
	"motor (>=3.3.2,<4.0.0)",
	"weasyprint (>=65.1,<66.0)",
	"openai (>=1.86.0,<2.0.0)",
	"edge-tts (>=7.0.2,<8.0.0)",
	"pydub (>=0.25.1,<0.26.0)",
	"reportlab (>=4.4.1,<5.0.0)",
	"xhtml2pdf (>=0.2.17,<0.3.0)",
	"markdown2 (>=2.5.3,<3.0.0)",
	"cohere (>=5.15.0,<6.0.0)",
	"markdown (>=3.8,<4.0)",
	"redis (>=6.2.0,<7.0.0)",
	"aiohttp (>=3.12.13,<4.0.0)",
	"langchain-groq (>=0.2.3,<0.3.0)",
	"langchain-fireworks (>=0.3.0,<0.4.0)",
	"httpx (>=0.27.0,<0.28.0)",
	"pytest (>=8.4.1,<9.0.0)",
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"


[tool.poetry]
package-mode = false

[tool.poetry.group.dev.dependencies]
pytest-asyncio = "^1.0.0"

